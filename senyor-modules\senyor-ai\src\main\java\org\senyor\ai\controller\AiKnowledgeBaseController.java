package org.senyor.ai.controller;

import java.util.List;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.senyor.ai.domain.AiKnowledgeBase;
import org.senyor.ai.domain.vo.AiKnowledgeBaseVo;
import org.senyor.ai.service.IAiKnowledgeBaseService;
import org.senyor.common.core.domain.R;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * AI知识库控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/knowledge")
@Tag(name = "AI知识库管理", description = "AI知识库相关接口")
public class AiKnowledgeBaseController extends BaseController {

    private final IAiKnowledgeBaseService aiKnowledgeBaseService;

    /**
     * 获取知识库列表
     */
    @GetMapping("/list")
    @Operation(summary = "知识库列表", description = "获取AI知识库列表")
    @SaCheckPermission("ai:knowledge:list")
    public R<List<AiKnowledgeBaseVo>> list(AiKnowledgeBase knowledgeBase) {
        return R.ok(aiKnowledgeBaseService.selectAiKnowledgeBaseList(knowledgeBase));
    }

    /**
     * 获取启用的知识库列表
     */
    @GetMapping("/enabled")
    @Operation(summary = "启用知识库", description = "获取启用的AI知识库列表")
    @SaCheckPermission("ai:knowledge:query")
    public R<List<AiKnowledgeBaseVo>> getEnabledKnowledgeBases() {
        return R.ok(aiKnowledgeBaseService.getEnabledKnowledgeBases());
    }

    /**
     * 获取用户知识库列表
     */
    @GetMapping("/user")
    @Operation(summary = "用户知识库", description = "获取当前用户的AI知识库列表")
    @SaCheckPermission("ai:knowledge:query")
    public R<List<AiKnowledgeBaseVo>> getUserKnowledgeBases() {
        return R.ok(aiKnowledgeBaseService.getAiKnowledgeBasesByUserId(LoginHelper.getUserId()));
    }

    /**
     * 获取知识库详情
     */
    @GetMapping("/{knowledgeId}")
    @Operation(summary = "知识库详情", description = "根据知识库ID获取AI知识库详情")
    @SaCheckPermission("ai:knowledge:query")
    public R<AiKnowledgeBaseVo> getInfo(@PathVariable("knowledgeId") Long knowledgeId) {
        return R.ok(aiKnowledgeBaseService.getAiKnowledgeBaseById(knowledgeId));
    }

    /**
     * 创建知识库
     */
    @PostMapping
    @Operation(summary = "创建知识库", description = "创建新的AI知识库")
    @SaCheckPermission("ai:knowledge:add")
    public R<Long> add(@Validated @RequestBody AiKnowledgeBase knowledgeBase) {
        return R.ok(aiKnowledgeBaseService.createAiKnowledgeBase(knowledgeBase));
    }

    /**
     * 更新知识库
     */
    @PutMapping
    @Operation(summary = "更新知识库", description = "更新AI知识库")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> edit(@Validated @RequestBody AiKnowledgeBase knowledgeBase) {
        aiKnowledgeBaseService.updateAiKnowledgeBase(knowledgeBase);
        return R.ok();
    }

    /**
     * 删除知识库
     */
    @DeleteMapping("/{knowledgeIds}")
    @Operation(summary = "删除知识库", description = "删除AI知识库")
    @SaCheckPermission("ai:knowledge:remove")
    public R<Void> remove(@PathVariable Long[] knowledgeIds) {
        aiKnowledgeBaseService.deleteAiKnowledgeBaseByIds(knowledgeIds);
        return R.ok();
    }

    /**
     * 启用/禁用知识库
     */
    @PutMapping("/status/{knowledgeId}/{status}")
    @Operation(summary = "更新状态", description = "启用或禁用AI知识库")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> updateStatus(@PathVariable("knowledgeId") Long knowledgeId, @PathVariable("status") String status) {
        aiKnowledgeBaseService.updateKnowledgeBaseStatus(knowledgeId, status);
        return R.ok();
    }
}
