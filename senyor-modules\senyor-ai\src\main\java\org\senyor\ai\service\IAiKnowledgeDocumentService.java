package org.senyor.ai.service;

import java.util.List;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.domain.bo.AiKnowledgeDocumentBo;
import org.senyor.ai.domain.vo.AiKnowledgeDocumentVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI知识库文档服务接口
 * 
 * <AUTHOR>
 */
public interface IAiKnowledgeDocumentService {
    
    /**
     * 查询单个文档
     * 
     * @param documentId 文档ID
     * @return AI文档对象
     */
    AiKnowledgeDocumentVo getAiKnowledgeDocumentById(Long documentId);
    
    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    List<AiKnowledgeDocumentVo> getAiKnowledgeDocumentsByKnowledgeId(Long knowledgeId);
    
    /**
     * 查询所有文档
     * 
     * @param aiKnowledgeDocument 查询参数
     * @return 文档列表
     */
    List<AiKnowledgeDocumentVo> selectAiKnowledgeDocumentList(AiKnowledgeDocument aiKnowledgeDocument);
    
    /**
     * 根据知识库ID统计文档数量
     * 
     * @param knowledgeId 知识库ID
     * @return 文档数量
     */
    int countDocumentsByKnowledgeId(Long knowledgeId);
    
    /**
     * 创建文档
     * 
     * @param aiKnowledgeDocument 文档对象
     * @return 文档ID
     */
    Long createAiKnowledgeDocument(AiKnowledgeDocument aiKnowledgeDocument);
    
    /**
     * 更新文档
     * 
     * @param aiKnowledgeDocument 文档对象
     * @return 结果
     */
    int updateAiKnowledgeDocument(AiKnowledgeDocument aiKnowledgeDocument);
    
    /**
     * 删除文档
     * 
     * @param documentId 文档ID
     * @return 结果
     */
    int deleteAiKnowledgeDocumentById(Long documentId);
    
    /**
     * 批量删除文档
     * 
     * @param documentIds 文档ID数组
     * @return 结果
     */
    int deleteAiKnowledgeDocumentByIds(Long[] documentIds);
    
    /**
     * 上传文档
     * 
     * @param knowledgeId 知识库ID
     * @param file 文件
     * @return 文档ID
     */
    Long uploadDocument(Long knowledgeId, MultipartFile file);
    
    /**
     * 更新文档状态
     * 
     * @param documentId 文档ID
     * @param status 状态
     * @return 结果
     */
    int updateDocumentStatus(Long documentId, String status);
    
    /**
     * 更新文档处理进度
     * 
     * @param documentId 文档ID
     * @param progress 进度
     * @return 结果
     */
    int updateDocumentProgress(Long documentId, Integer progress);
    
    /**
     * 处理文档（解析、分块、向量化）
     * 
     * @param documentId 文档ID
     * @return 结果
     */
    boolean processDocument(Long documentId);
    
    /**
     * 重新处理文档
     * 
     * @param documentId 文档ID
     * @return 结果
     */
    boolean reprocessDocument(Long documentId);
    
    /**
     * 提取文件内容
     * 
     * @param file 文件
     * @return 文件内容
     */
    String extractContent(MultipartFile file);
} 