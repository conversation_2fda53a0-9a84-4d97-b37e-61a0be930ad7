package org.senyor.ai.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * AI知识库文档分块表 ai_knowledge_chunk
 *
 * <AUTHOR>
 */
@Data
@TableName("ai_knowledge_chunk")
public class AiKnowledgeChunk {

    /**
     * 分块ID
     */
    @TableId(value = "chunk_id", type = IdType.AUTO)
    private Long chunkId;

    /**
     * 文档ID
     */
    private Long documentId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 分块内容
     */
    private String content;

    /**
     * 分块序号
     */
    private Integer chunkIndex;

    /**
     * 分块大小（字符数）
     */
    private Integer chunkSize;

    /**
     * 向量ID（在向量数据库中的ID）
     */
    private String vectorId;

    /**
     * 向量状态（0:未向量化 1:向量化中 2:已完成 3:失败）
     */
    private String vectorStatus;

    /**
     * 向量维度
     */
    private Integer vectorDimension;

    /**
     * 向量数据（Base64编码的JSON数组）
     */
    private String embedding;

    /**
     * 相似度分数
     */
    private Float similarityScore;

    /**
     * 元数据（JSON格式存储额外信息）
     */
    private String metadata;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名
     */
    private String userName;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
