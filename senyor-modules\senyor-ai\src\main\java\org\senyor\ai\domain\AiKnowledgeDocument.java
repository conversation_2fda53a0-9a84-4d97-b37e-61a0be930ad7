package org.senyor.ai.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * AI知识库文档表 ai_knowledge_document
 *
 * <AUTHOR>
 */
@Data
@TableName("ai_knowledge_document")
public class AiKnowledgeDocument {

    /**
     * 文档ID
     */
    @TableId(value = "document_id", type = IdType.AUTO)
    private Long documentId;

    /**
     * 知识库ID
     */
    private Long knowledgeId;

    /**
     * 文档名称
     */
    private String documentName;

    /**
     * 文档类型（text:文本, pdf:PDF, docx:Word, txt:纯文本）
     */
    private String documentType;

    /**
     * 文档内容
     */
    private String content;

    /**
     * 文档大小（字节）
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 文档状态（0:待处理 1:处理中 2:已完成 3:失败）
     */
    private String status;

    /**
     * 处理进度（0-100）
     */
    private Integer progress;

    /**
     * 向量化状态（0:未向量化 1:向量化中 2:已完成 3:失败）
     */
    private String vectorStatus;

    /**
     * 分块数量
     */
    private Integer chunkCount;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名
     */
    private String userName;


    /**
     * 备注
     */
    private String remark;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
