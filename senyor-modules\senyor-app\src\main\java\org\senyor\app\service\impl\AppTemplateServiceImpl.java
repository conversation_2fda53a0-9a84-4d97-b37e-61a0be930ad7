package org.senyor.app.service.impl;

import com.esotericsoftware.minlog.Log;
import com.fasterxml.jackson.core.type.TypeReference;
import org.senyor.app.domain.AppField;
import org.senyor.app.domain.AppTemplateField;
import org.senyor.app.domain.vo.AppFieldVo;
import org.senyor.app.mapper.AppFieldMapper;
import org.senyor.app.mapper.AppTemplateFieldMapper;
import org.senyor.common.core.domain.model.LoginUser;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.core.utils.MapstructUtils;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.senyor.common.core.utils.StringUtils;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.websocket.holder.WebSocketSessionHolder;
import org.senyor.common.websocket.utils.WebSocketUtils;
import org.senyor.system.domain.SysRole;
import org.senyor.system.domain.SysUser;
import org.senyor.system.domain.bo.SysUserBo;
import org.senyor.system.domain.vo.SysFieldVo;
import org.senyor.system.domain.vo.SysRoleVo;
import org.senyor.system.domain.vo.SysUserVo;
import org.senyor.system.mapper.SysRoleMapper;
import org.senyor.system.mapper.SysUserMapper;
import org.senyor.system.mapper.SysUserRoleMapper;
import org.springframework.stereotype.Service;
import org.senyor.app.domain.bo.AppTemplateBo;
import org.senyor.app.domain.vo.AppTemplateVo;
import org.senyor.app.domain.AppTemplate;
import org.senyor.app.mapper.AppTemplateMapper;
import org.senyor.app.service.IAppTemplateService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.socket.WebSocketSession;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class AppTemplateServiceImpl implements IAppTemplateService {

    private final AppTemplateMapper baseMapper;
    private final AppTemplateFieldMapper templateFieldMapper;
    private final AppFieldMapper fieldMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserMapper userMapper;
    private final SysUserRoleMapper userRoleMapper;

    private static final String WEBSOCKET_STATUS = "2";

    private static final String MESSAGE_TITLE = "[档案未完善] 请完善您的个人信息！";
    private static final String MESSAGE_INFO = "请补充您的个人档案信息！";
    private static final String Message_ROUTER = "/user/profile";

    /**
     * 查询模板
     *
     * @param id 主键
     * @return 模板
     */
    @Override
    public AppTemplateVo queryById(Long id){
        AppTemplateVo vo = baseMapper.selectVoById(id);
        //已选择的字段
//        List<Long> fieldIds = templateFieldMapper.selectFieldIdsByTemplateId(id).stream().map(AppTemplateField::getFieldId).toList();
        List<Long> fieldIds = templateFieldMapper.selectList(new LambdaQueryWrapper<AppTemplateField>().eq(AppTemplateField::getTemplateId, id)).stream().map(AppTemplateField::getFieldId).toList();
        vo.setFieldNum(0);
        if (!fieldIds.isEmpty()) {
            vo.setFieldIds(fieldIds);
            vo.setFieldNum(fieldIds.size());
            //封装字段集合
            List<AppFieldVo> fieldList = fieldMapper.selectVoList(new LambdaQueryWrapper<AppField>().in(AppField::getId, fieldIds));
            if (fieldList != null && !fieldList.isEmpty()) {
                vo.setFieldList(fieldList);
            }
        }
        return vo;
    }

    /**
     * 分页查询模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模板分页列表
     */
    @Override
    public TableDataInfo<AppTemplateVo> queryPageList(AppTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AppTemplate> lqw = buildQueryWrapper(bo);
        Page<AppTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        Map<Long, Map<String, Object>> fieldCountMap = templateFieldMapper.selectTemplateFieldCountMap();
        List<Long> roleIds = result.getRecords().stream().map(AppTemplateVo::getRoleId).toList();
        Map<Long, String> roleMap = roleMapper.selectList(new LambdaQueryWrapper<SysRole>().in(!roleIds.isEmpty(),SysRole::getRoleId, roleIds)).stream().collect(Collectors.toMap(SysRole::getRoleId, SysRole::getRoleName));
        for (AppTemplateVo vo : result.getRecords()) {
            if (fieldCountMap != null && !fieldCountMap.isEmpty()) {
                Map<String, Object> resultMap = fieldCountMap.get(vo.getId());
                vo.setFieldNum(0);
                if (resultMap != null) {
                    Integer fieldNum = Integer.parseInt(fieldCountMap.get(vo.getId()).get("count").toString());
                    vo.setFieldNum(fieldNum);
                }
            }
            if (!roleMap.isEmpty()) {
                String roleName = roleMap.get(vo.getRoleId());
                vo.setRoleName(StringUtils.isNotBlank(roleName) ? roleName : "未绑定角色");
            }

        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的模板列表
     *
     * @param bo 查询条件
     * @return 模板列表
     */
    @Override
    public List<AppTemplateVo> queryList(AppTemplateBo bo) {
        LambdaQueryWrapper<AppTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AppTemplate> buildQueryWrapper(AppTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<AppTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), AppTemplate::getTitle, bo.getTitle());
        lqw.eq(StringUtils.isNotBlank(bo.getTopFlag()), AppTemplate::getTopFlag, bo.getTopFlag());
        // 只允许查找角色模板 普查任务模板是一对一的 不允许以列表形式查询
        lqw.eq(AppTemplate::getStatus, "0");
        lqw.orderByDesc(AppTemplate::getTopFlag).orderByDesc(AppTemplate::getCreateTime);
        return lqw;
    }

    /**
     * 新增模板
     *
     * @param bo 模板
     * @return 是否新增成功
     */
    @Override
    @Transactional
    public Long insertByBo(AppTemplateBo bo) {
        List<Long> fieldIds = bo.getFieldIds();
        AppTemplate add = MapstructUtils.convert(bo, AppTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
            if (fieldIds != null && !fieldIds.isEmpty() && bo.getId() != null) {
                List<AppTemplateField> relations = bo.getFieldIds().stream()
                    .map(fieldId -> new AppTemplateField()
                        .setTemplateId(bo.getId())
                        .setFieldId(fieldId))
                    .collect(Collectors.toList());
                flag = templateFieldMapper.insertBatch(relations);
//                flag = templateFieldMapper.insertBatchWithoutTenant(relations);
            }
        }
        return bo.getId();
    }

    /**
     * 修改模板
     *
     * @param bo 模板
     * @return 是否修改成功
     */
    @Override
    @Transactional
    public Boolean updateByBo(AppTemplateBo bo) {
        AppTemplate update = MapstructUtils.convert(bo, AppTemplate.class);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag && bo.getFieldIds() != null) {
            templateFieldMapper.deleteByTemplateId(bo.getId());
            if (!bo.getFieldIds().isEmpty()) {
                List<AppTemplateField> relations = bo.getFieldIds().stream()
                    .map(fieldId -> new AppTemplateField()
                        .setTemplateId(bo.getId())
                        .setFieldId(fieldId))
                    .collect(Collectors.toList());
                flag = templateFieldMapper.insertBatch(relations);
//                flag = templateFieldMapper.insertBatchWithoutTenant(relations);
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AppTemplate entity){
        //验证模板与角色是否是一对一的
        AppTemplate appTemplate = baseMapper.selectOne(new LambdaQueryWrapper<AppTemplate>().eq(AppTemplate::getRoleId, entity.getRoleId()));
        if (appTemplate != null && entity.getRoleId().equals(appTemplate.getRoleId())) {
            throw new ServiceException("一个角色只能绑定一个模板");
        }
    }

    /**
     * 校验并批量删除模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 根据角色ID查询模板
     * @param roleId 角色id
     * */
    @Override
    public AppTemplateVo getTemplateByRoleId(Long roleId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<AppTemplate>().eq(AppTemplate::getRoleId, roleId));
    }
    /**
     * 获取对应模板字段的值
     * */
    @Override
    public AppTemplateVo getTemplateFieldData(Long userId, Long templateId) {
        AppTemplateVo vo = null;
        try {
            vo = queryById(templateId);
            SysUser user = userMapper.selectById(userId);
            if (vo != null && vo.getFieldList() != null && user != null) {
                String moreFields = user.getMoreFields();
                Map<String, String> moreFieldsMap = null;
                if (StringUtils.isNotBlank(moreFields)) {
                    moreFieldsMap = JsonUtils.parseObject(moreFields, new TypeReference<Map<String, String>>() {});
                }
                for (AppFieldVo fvo : vo.getFieldList()) {
                    if ("0".equals(fvo.getStatus())) { // 原始字段
                        try {
                            Field field = SysUser.class.getDeclaredField( toCamelCase(fvo.getFieldFlag()));
                            field.setAccessible(true);
                            Object value = field.get(user);
                            fvo.setRealValue(value != null ? value.toString() : "");
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            fvo.setRealValue("");
                        }
                    }else if (moreFieldsMap != null && "1".equals(fvo.getStatus())) { //拓展字段
                        String value = moreFieldsMap.get(fvo.getFieldFlag());
                        fvo.setRealValue(value);
                    }
                }

            }
        }catch (Exception e) {
            throw new ServiceException("获取扩展字段失败");
        }

        return vo;
    }
    /**
     * 获取全部字段的值
     * */
    @Override
    public AppTemplateVo getAllFieldData(Long userId) {
        AppTemplateVo vo = new AppTemplateVo();
        List<AppFieldVo> fieldList = fieldMapper.selectVoList();
        try {
            SysUser user = userMapper.selectById(userId);
            if (user != null && !fieldList.isEmpty()) {
                String moreFields = user.getMoreFields();
                Map<String, String> moreFieldsMap = null;
                if (StringUtils.isNotBlank(moreFields)) {
                    moreFieldsMap = JsonUtils.parseObject(moreFields, new TypeReference<Map<String, String>>() {});
                }
                for (AppFieldVo fvo : fieldList) {
                    if ("0".equals(fvo.getStatus())) { // 原始字段
                        try {
                            Field field = SysUser.class.getDeclaredField( toCamelCase(fvo.getFieldFlag()));
                            field.setAccessible(true);
                            Object value = field.get(user);
                            fvo.setRealValue(value != null ? value.toString() : "");
                        } catch (NoSuchFieldException | IllegalAccessException e) {
                            fvo.setRealValue("");
                        }
                    }else if (moreFieldsMap != null && "1".equals(fvo.getStatus())) { //拓展字段
                        String value = moreFieldsMap.get(fvo.getFieldFlag());
                        fvo.setRealValue(value);
                    }
                }
                vo.setFieldList(fieldList);
            }
        }catch (Exception e) {
            throw new ServiceException("获取扩展字段失败");
        }

        return vo;
    }

    /**
     * 根据用户ID获取对应模板字段的值
     * @param userId 用户ID
     * */
    @Override
    public AppTemplateVo getTemplateFieldDataByUserId(Long userId) {
        List<SysRoleVo> roles = roleMapper.selectRolesByUserId(userId);
        //如果用户没有绑定角色
        if (roles == null || roles.isEmpty()) {
            return null;
        }
        //不论绑定几个角色 默认取第一个
        Long roleId = roles.getFirst().getRoleId();
        AppTemplate template = baseMapper.selectOne(new LambdaQueryWrapper<AppTemplate>().eq(AppTemplate::getRoleId, roleId));
        if (template == null) { //角色没有默认模板
            return null;
        }
        return getTemplateFieldData(userId, template.getId());

    }

    /**
     * 一键档案完善 单对单 仅单个用户需要完善
     * @param userId 用户ID
     * */
    @Override
    public Boolean improveByUserId(Long userId) {
        SysUser user = userMapper.selectById(userId);
        if (user == null) {
            throw new ServiceException("该用户不存在！");
        }
        List<SysRoleVo> roles = roleMapper.selectRolesByUserId(userId);
        if (roles == null || roles.isEmpty()) {
            throw new ServiceException("档案完善失败！用户未绑定角色！");
        }
        //默认使用第一个角色的模板
        SysRoleVo role = roles.getFirst();
        AppTemplate template = baseMapper.selectOne(new LambdaQueryWrapper<AppTemplate>().eq(AppTemplate::getRoleId, role.getRoleId()));
        if (template == null) {
            throw new ServiceException("档案完善失败！该角色未绑定模板！");
        }
        AppTemplateVo templateVo = queryById(template.getId());
        boolean flag = true;
        for (AppFieldVo fieldVo : templateVo.getFieldList()) {
            if ("0".equals(fieldVo.getStatus())) { //验证原始字段
                try {
                    Field field = SysUser.class.getDeclaredField(toCamelCase(fieldVo.getFieldFlag()));
                    field.setAccessible(true);
                    Object value = field.get(user);
                    //原始字段存在值为空
                    if (value == null || String.valueOf(value).trim().isEmpty()) {
                        flag = false;
                    }
                }catch (Exception e) {
                    throw new ServiceException("档案完善失败！有原始字段不存在！");
                }
            }else if ("1".equals(fieldVo.getStatus())) { //验证拓展字段
                String moreFields = user.getMoreFields();
                if (StringUtils.isNotBlank(moreFields)) {
                    Map<String, String> moreFieldsMap = JsonUtils.parseObject(moreFields, new TypeReference<Map<String, String>>() {});
                    //拓展字段存在值为空
                    if (moreFieldsMap != null && (moreFieldsMap.get(fieldVo.getFieldFlag()) == null || moreFieldsMap.get(fieldVo.getFieldFlag()).trim().isEmpty())) {
                        flag = false;
                    }
                }else {
                    flag = false;
                }
            }
        }
        Log.info("Flag = {}", String.valueOf(flag));
        //如果该用户档案未完善 发送WebSocket消息
        if (!flag) {
            Log.info("未完善档案");
            // 修改状态为部分缺失
            if (user.getProfileFlag() != null && !"1".equals(user.getProfileFlag())) {
                user.setProfileFlag("1");
                userMapper.updateById(user);
            }
            HashSet<Long> ids = new HashSet<>();
            ids.add(userId);
            WebSocketUtils.sendMessageToGroup(ids, MESSAGE_TITLE, WEBSOCKET_STATUS,MESSAGE_INFO,Message_ROUTER);
        }else {
            //修改状态为已完善
            if (user.getProfileFlag() != null && !"2".equals(user.getProfileFlag())) {
                user.setProfileFlag("2");
                userMapper.updateById(user);
            }
            throw new ServiceException("该用户档案无需继续完善！");
        }
        return true;
    }

    /**
     * API - 修改用户自己的模板字段
     * */
    @Override
    public Boolean editFieldData(SysUserBo bo) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            throw new ServiceException("登录状态异常 请重新登录!");
        }
        if (bo == null || bo.getFieldList() == null || bo.getFieldList().isEmpty()) {
            return false;
        }
        bo.setUserId(loginUser.getUserId());
        List<SysFieldVo> fieldList = bo.getFieldList();
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, String> extensionFields = new HashMap<>();
        for (SysFieldVo field : fieldList) {
            if ("0".equals(field.getStatus())) {//原始字段
                fieldMap.put(field.getFieldFlag(), field.getRealValue());
            } else if ("1".equals(field.getStatus())) {//扩展字段
                extensionFields.put(field.getFieldFlag(), field.getRealValue());
            }
        }
        String jsonStr = JsonUtils.toJsonString(extensionFields);
        // 动态更新
        int rows = userMapper.updateUserFields(bo.getUserId(), fieldMap, jsonStr);
        return rows > 0;
    }

    /**
     * 一键完善所有 已弃用
     * @param templateId 模板id
     * */
    @Override
    public Boolean improve(Long templateId) {
        AppTemplateVo template = queryById(templateId);
        //需要提醒的用户列表
        Set<Long> targetUsers = new HashSet<>();
        //用户列表
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(template.getRoleId());
        if (userIds != null && !userIds.isEmpty()) {
            List<SysUser> users = userMapper.selectList(new LambdaQueryWrapper<SysUser>().in(SysUser::getUserId, userIds));
            //验证字段完整性
            try {
                for (SysUser user : users) {
                    for (AppFieldVo fieldVo : template.getFieldList()) {
                        if ("0".equals(fieldVo.getStatus())) { //验证原始字段
                            Field field = SysUser.class.getDeclaredField(toCamelCase(fieldVo.getFieldFlag()));
                            field.setAccessible(true);
                            Object value = field.get(user);
                            //如果=null则表示该字段没有值 添加到目标用户ID集合
                            if (value == null || String.valueOf(value).trim().isEmpty()) {
                                targetUsers.add(user.getUserId());
                            }
                        }else if ("1".equals(fieldVo.getStatus())) { //验证拓展字段
                            String moreFields = user.getMoreFields();
                            if (StringUtils.isNotBlank(moreFields)) {
                                Map<String, String> moreFieldsMap = JsonUtils.parseObject(moreFields, new TypeReference<Map<String, String>>() {});
                                //如果more_fields字段不为空 解析JSON成功且Map中对应字段的值=null 添加到目标用户ID集合
                                if (moreFieldsMap != null && (moreFieldsMap.get(fieldVo.getFieldFlag()) == null || moreFieldsMap.get(fieldVo.getFieldFlag()).trim().isEmpty())) {
                                    targetUsers.add(user.getUserId());
                                }
                            }else {
                                targetUsers.add(user.getUserId());
                            }
                        }
                    }
                }
            }catch (Exception e) {
                throw new ServiceException("一键完善失败");
            }

        }
        Log.info("需要通知的用户ID：{}", String.valueOf(targetUsers));
        WebSocketUtils.sendMessageToGroup(targetUsers, MESSAGE_TITLE,WEBSOCKET_STATUS,MESSAGE_INFO,Message_ROUTER);
        return true;
    }

    // 驼峰转换
    private String toCamelCase(String columnName) {
        StringBuilder result = new StringBuilder();
        boolean nextUpper = false;
        for (char c : columnName.toCharArray()) {
            if (c == '_') {
                nextUpper = true;
            } else {
                result.append(nextUpper ? Character.toUpperCase(c) : c);
                nextUpper = false;
            }
        }
        return result.toString();
    }

}
