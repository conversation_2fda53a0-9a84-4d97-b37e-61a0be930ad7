<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.statistics.mapper.StatisticsRiskWarningRecordMapper">

    <resultMap id="RiskWarningStatsResult" type="org.senyor.statistics.domain.vo.StatisticsRiskWarningStatsVO">
        <result property="value" column="count"/>
        <result property="name" column="warning_level_name"/>
        <association property="itemStyle" javaType="org.senyor.statistics.domain.vo.StatisticsRiskWarningStatsVO$ItemStyle">
            <result property="color" column="warning_level_color"/>
        </association>
    </resultMap>

    <resultMap id="DeptWarningStatsResult" type="org.senyor.statistics.domain.vo.StatisticsDeptWarningVO">
        <result property="deptName" column="dept_name"/>
        <result property="warningRate" column="warning_rate"/>
        <result property="level1" column="level_1"/>
        <result property="level2" column="level_2"/>
        <result property="level3" column="level_3"/>
        <result property="level4" column="level_4"/>
        <result property="level5" column="level_5"/>
    </resultMap>

    <resultMap id="GenderWarningStatsResult" type="org.senyor.statistics.domain.vo.StatisticsGenderWarningVO">
        <result property="gender" column="sex"/>
        <result property="genderDesc" column="gender_desc"/>
        <result property="warningRate" column="warning_rate"/>
        <result property="totalCount" column="total_users"/>
        <result property="warningCount" column="warning_users"/>
    </resultMap>

    <select id="selectWarningLevelStats" resultMap="RiskWarningStatsResult">
        SELECT
        COUNT(DISTINCT r.user_id) AS count,
        r.warning_level_id,
        c.warning_name AS warning_level_name,
        c.warning_color AS warning_level_color
        FROM
        risk_warning_record r
        LEFT JOIN
        scale_early_warning_level_config c
        ON r.warning_level_id = c.warning_level_id
        AND c.type = 'scale'
        WHERE
        1=1
        <if test="planId != null">
            AND r.plan_id = #{planId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        GROUP BY
        r.warning_level_id, c.warning_name, c.warning_color;
    </select>

    <select id="selectDeptWarningStats" resultMap="DeptWarningStatsResult">
        SELECT
            dept_name,
            CONCAT(ROUND(IFNULL(warning_users * 100.0 / NULLIF(total_users, 0), 0), 2), '%') AS warning_rate,
            CAST(IFNULL(level_1, 0) AS SIGNED) AS level_1,
            CAST(IFNULL(level_2, 0) AS SIGNED) AS level_2,
            CAST(IFNULL(level_3, 0) AS SIGNED) AS level_3,
            CAST(IFNULL(level_4, 0) AS SIGNED) AS level_4,
            CAST(IFNULL(level_5, 0) AS SIGNED) AS level_5
        FROM (
            SELECT
                d.dept_name,
                r.dept_id,
                COUNT(DISTINCT CASE WHEN r.warning_level_id = 1 THEN r.user_id END) AS level_1,
                COUNT(DISTINCT CASE WHEN r.warning_level_id = 2 THEN r.user_id END) AS level_2,
                COUNT(DISTINCT CASE WHEN r.warning_level_id = 3 THEN r.user_id END) AS level_3,
                COUNT(DISTINCT CASE WHEN r.warning_level_id = 4 THEN r.user_id END) AS level_4,
                COUNT(DISTINCT CASE WHEN r.warning_level_id = 5 THEN r.user_id END) AS level_5,
                COUNT(DISTINCT r.user_id) AS total_users,
                COUNT(DISTINCT CASE WHEN r.warning_level_id > 1 THEN r.user_id END) AS warning_users
            FROM
                risk_warning_record r
            LEFT JOIN
                sys_dept d ON r.dept_id = d.dept_id
            WHERE
                1=1
            <if test="planId != null">
                AND r.plan_id = #{planId}
            </if>
            <if test="scaleId != null">
                AND r.scale_id = #{scaleId}
            </if>
            GROUP BY
                r.dept_id, d.dept_name
        ) dept_stats
        ORDER BY
            warning_users DESC, total_users DESC;
    </select>

    <select id="searchAllLevel" resultType="org.senyor.statistics.domain.vo.ScaleWarningLevelVO">
        select * from scale_early_warning_level_config where type = 'scale'
    </select>

    <select id="selectGenderWarningStats" resultMap="GenderWarningStatsResult">
        SELECT
            r.sex,
            CASE
                WHEN r.sex = '0' THEN '男'
                WHEN r.sex = '1' THEN '女'
                ELSE '未知'
            END as gender_desc,
            CONCAT(ROUND(IFNULL(SUM(CASE WHEN c.warning = 'Y' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(DISTINCT r.user_id), 0), 0), 2), '%') AS warning_rate,
            COUNT(DISTINCT r.user_id) AS total_users,
            COUNT(DISTINCT CASE WHEN c.warning = 'Y' THEN r.user_id END) AS warning_users
        FROM
            scale_early_warning_record r
        LEFT JOIN
            scale_early_warning_level_config c ON r.warning_level_id = c.warning_level_id AND c.type = 'scale'
        WHERE
            1=1
        <if test="planId != null">
            AND r.plan_id = #{planId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        GROUP BY
            r.sex
        ORDER BY
            r.sex ASC
    </select>

    <!-- 获取按性别和预警等级统计的人数 -->
    <select id="selectGenderLevelCounts" resultType="org.senyor.statistics.domain.vo.GenderLevelDetailCountVO">
        SELECT
            r.sex,
            r.warning_level_id as warningLevelId,
            COUNT(DISTINCT r.user_id) as levelCount
        FROM
            scale_early_warning_record r
        WHERE
            1=1
        <if test="planId != null">
            AND r.plan_id = #{planId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        AND r.sex IN ('0', '1') <!-- 只统计男女性别 -->
        GROUP BY
            r.sex, r.warning_level_id
        ORDER BY
            r.sex ASC, r.warning_level_id ASC
    </select>

    <!-- 获取按性别和预警等级统计的人数（用于对称条形图） -->
    <select id="selectGenderWarningDataForChart" resultType="org.senyor.statistics.domain.vo.GenderWarningChartVO">
        SELECT
            r.sex,
            r.warning_level_id as warningLevelId,
            COUNT(DISTINCT r.user_id) as count
        FROM
            scale_early_warning_record r
        INNER JOIN (
            SELECT
                user_id,
                sex,
                MAX(warning_level_id) as max_warning_level_id
            FROM
                scale_early_warning_record
            WHERE
                sex IN ('0', '1')
            GROUP BY
                user_id, sex
        ) m ON r.user_id = m.user_id AND r.sex = m.sex AND r.warning_level_id = m.max_warning_level_id
        WHERE
            r.sex IN ('0', '1')
        GROUP BY
            r.sex, r.warning_level_id
        ORDER BY
            r.sex ASC, r.warning_level_id ASC
    </select>

    <!-- 获取性别比例统计数据 -->
    <select id="selectGenderRatioStats" resultType="org.senyor.statistics.domain.vo.GenderRatioStatsVO">
        SELECT
            r.sex,
            COUNT(DISTINCT r.user_id) AS totalUsers
        FROM
            scale_early_warning_record r
                INNER JOIN (
                SELECT user_id, MAX(warning_level_id) AS max_warning_level_id  -- 修正别名
                FROM scale_early_warning_record
                WHERE sex IN ('0', '1')
                GROUP BY user_id
            ) m ON r.user_id = m.user_id
                AND r.warning_level_id = m.max_warning_level_id  -- 修正关联字段
                INNER JOIN scale_early_warning_level_config c ON
                r.warning_level_id = c.warning_level_id
                    AND c.type = 'scale'
                    AND c.warning = 'Y'
        WHERE
            r.sex IN ('0', '1')
        GROUP BY
            r.sex
        ORDER BY
            r.sex ASC;
    </select>

    <select id="selectGenderWarningStatsWithLevels" resultType="org.senyor.statistics.domain.vo.GenderWarningStatsWithLevelsVO">
        SELECT
            r.sex,
            CASE
                WHEN r.sex = '0' THEN '男'
                WHEN r.sex = '1' THEN '女'
                ELSE '未知'
            END as genderDesc,
            CONCAT(ROUND(IFNULL(COUNT(DISTINCT CASE WHEN c.warning = 'Y' THEN r.user_id END) * 100.0 / NULLIF(COUNT(DISTINCT r.user_id), 0), 0), 2), '%') AS warningRate,
            COUNT(DISTINCT r.user_id) AS totalUsers,
            COUNT(DISTINCT CASE WHEN c.warning = 'Y' THEN r.user_id END) AS warningUsers
        FROM
            scale_early_warning_record r
        LEFT JOIN
            scale_early_warning_level_config c ON r.warning_level_id = c.warning_level_id AND c.type = 'scale'
        WHERE
            1=1
            AND r.sex IN ('0', '1') <!-- 只统计男女性别 -->
        <if test="planId != null">
            AND r.plan_id = #{planId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        GROUP BY
            r.sex
        ORDER BY
            r.sex ASC
    </select>

    <!-- 获取按性别和预警等级分组统计的人数 -->
    <select id="selectGenderLevelDetailCounts" resultType="org.senyor.statistics.domain.vo.GenderLevelDetailCountVO">
        SELECT
            r.sex,
            r.warning_level_id as warningLevelId,
            COUNT(DISTINCT r.user_id) as levelCount
        FROM
            scale_early_warning_record r
        WHERE
            1=1
            AND r.sex IN ('0', '1') <!-- 只统计男女性别 -->
        <if test="planId != null">
            AND r.plan_id = #{planId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        GROUP BY
            r.sex, r.warning_level_id
        ORDER BY
            r.sex ASC, r.warning_level_id ASC
    </select>

    <!-- 获取不同机构（年级）的不同程度预警人数 -->
    <select id="selectGradeWarningStats" resultType="org.senyor.statistics.domain.vo.GradeWarningStatsVO">
        SELECT
            d.dept_id as deptId,
            d.dept_name as deptName,
            IFNULL(r.warning_level_id, 0) as warningLevelId,
            COUNT(DISTINCT u.user_id) as count,
            (SELECT COUNT(DISTINCT user_id)
             FROM scale_early_warning_record
             WHERE dept_id = d.dept_id) as deptTotal
        FROM
            sys_dept d
        LEFT JOIN
            (SELECT user_id, dept_id, MAX(warning_level_id) as max_warning_level_id
             FROM scale_early_warning_record
             GROUP BY user_id, dept_id) AS u
        ON d.dept_id = u.dept_id
        LEFT JOIN
            scale_early_warning_record r
        ON u.user_id = r.user_id
            AND u.dept_id = r.dept_id
            AND u.max_warning_level_id = r.warning_level_id
        WHERE
            d.status = '0'  <!-- 只统计正常状态的部门 -->
        GROUP BY
            d.dept_id, d.dept_name, IFNULL(r.warning_level_id, 0)
        ORDER BY
            d.dept_name ASC, IFNULL(r.warning_level_id, 0) ASC
    </select>

</mapper>
