export interface GeoJSONSource {
    type: 'FeatureCollection';
    features: Feature[];
}

export interface Feature {
    type: 'Feature';
    properties: MapFeatureProperties | DataPointProperties;
    geometry: {
        type: 'Polygon' | 'MultiPolygon';
        coordinates: number[][][] | number[][][][];
    };
}

export interface MapFeatureProperties {
    adcode: number;
    name: string;
    level: 'country' | 'province' | 'city' | 'district' | 'town';
    parent?: {
        adcode: number;
    };
    center: [number, number];
}

export interface DataPointProperties {
    id: string;
    adcode: number;
    name: string;
    type: 'hospital' | 'school' | 'business' | 'tenant';
    address?: string;
    count: number;
    coordinates: [number, number];
    tenantId?: number;
}


export type MapLevel = 'nation' | 'province' | 'city' | 'district' | 'town' | 'tenant';

export type WorkerMessage =
    | {
    type: 'init';
    payload: {
        geoData: GeoJSONSource;
        featureProps: MapFeatureProperties;
        userData: Record<string, number>;
        tenantPoints?: DataPointProperties[];
    }
}
    | {
    type: 'drill';
    payload: { feature: Feature }
}
    | {
    type: 'update';
    payload: {
        geoData: GeoJSONSource;
        featureProps: MapFeatureProperties;
        userData: Record<string, number>;
        tenantPoints?: DataPointProperties[];
    }
};
