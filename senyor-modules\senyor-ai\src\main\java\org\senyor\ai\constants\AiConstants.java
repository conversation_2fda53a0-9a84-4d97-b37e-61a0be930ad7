package org.senyor.ai.constants;

/**
 * AI模块常量
 *
 * <AUTHOR>
 */
public class AiConstants {

    /**
     * 向量化状态
     */
    public static class VectorStatus {
        /** 未向量化 */
        public static final String PENDING = "0";
        /** 向量化中 */
        public static final String PROCESSING = "1";
        /** 已完成 */
        public static final String COMPLETED = "2";
        /** 失败 */
        public static final String FAILED = "3";
    }

    /**
     * 文档状态
     */
    public static class DocumentStatus {
        /** 待处理 */
        public static final String PENDING = "0";
        /** 处理中 */
        public static final String PROCESSING = "1";
        /** 已完成 */
        public static final String COMPLETED = "2";
        /** 失败 */
        public static final String FAILED = "3";
    }

    /**
     * 知识库类型
     */
    public static class KnowledgeType {
        /** 文档库 */
        public static final String DOCUMENT = "document";
        /** 问答库 */
        public static final String QA = "qa";
        /** 自定义 */
        public static final String CUSTOM = "custom";
    }

    /**
     * 文档类型
     */
    public static class DocumentType {
        /** 文本 */
        public static final String TEXT = "text";
        /** PDF */
        public static final String PDF = "pdf";
        /** Word */
        public static final String DOCX = "docx";
        /** 纯文本 */
        public static final String TXT = "txt";
        /** 图片 */
        public static final String IMAGE = "image";
    }

    /**
     * 消息角色
     */
    public static class MessageRole {
        /** 用户 */
        public static final String USER = "user";
        /** 助手 */
        public static final String ASSISTANT = "assistant";
        /** 系统 */
        public static final String SYSTEM = "system";
    }

    /**
     * 向量配置
     */
    public static class VectorConfig {
        /** 向量维度 */
        public static final int DIMENSION = 1536;
        /** 最小相似度 */
        public static final double MIN_SIMILARITY = 0.7;
        /** 默认返回数量 */
        public static final int TOP_K = 5;
        /** 过期时间（天） */
        public static final int EXPIRE_DAYS = 30;
    }

    /**
     * Redis Key前缀
     */
    public static class RedisKey {
        /** 向量前缀 */
        public static final String VECTOR_PREFIX = "ai:vector:";
        /** 索引前缀 */
        public static final String INDEX_PREFIX = "ai:index:";
        /** 元数据前缀 */
        public static final String METADATA_PREFIX = "ai:metadata:";
    }

    /**
     * 模型配置
     */
    public static class Model {
        /** 默认模型 */
        public static final String DEFAULT_MODEL = "deepseek-r1";
        /** 嵌入模型 */
        public static final String EMBEDDING_MODEL = "text-embedding-v2";
    }
} 