# Redis向量存储配置

# DashScope配置
dashscope:
  api-key: sk-43138546db0b4ea383b796eab5fec2e7

# 向量化配置
ai:
  vector:
    # 向量维度（DashScope text-embedding-v2模型）
    dimension: 1536
    # 默认相似度阈值
    min-similarity: 0.7
    # 默认返回结果数量
    top-k: 5
    # 向量数据过期时间（天）
    expire-days: 30
    # 批量处理大小
    batch-size: 10
    # 并行处理线程数
    thread-pool-size: 4

# Redis配置
spring:
  redis:
    # 连接池配置
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 3000ms
    # 超时配置
    timeout: 5000ms
    # 重试配置
    retry:
      max-attempts: 3
