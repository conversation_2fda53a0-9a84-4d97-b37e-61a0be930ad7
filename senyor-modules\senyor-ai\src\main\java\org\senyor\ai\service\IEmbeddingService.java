package org.senyor.ai.service;

import java.util.List;

/**
 * 向量化服务接口
 * 用于将文本转换为向量表示
 *
 * <AUTHOR>
 */
public interface IEmbeddingService {

    /**
     * 将文本转换为向量
     *
     * @param text 要向量化的文本
     * @return 向量数组
     */
    List<Float> getEmbedding(String text);

    /**
     * 批量将文本转换为向量
     *
     * @param texts 要向量化的文本列表
     * @return 向量数组列表
     */
    List<List<Float>> getEmbeddings(List<String> texts);

    /**
     * 获取向量维度
     *
     * @return 向量维度
     */
    int getVectorDimension();

    /**
     * 计算两个向量的余弦相似度
     *
     * @param vector1 向量1
     * @param vector2 向量2
     * @return 相似度分数 (0-1)
     */
    double calculateCosineSimilarity(List<Float> vector1, List<Float> vector2);
} 