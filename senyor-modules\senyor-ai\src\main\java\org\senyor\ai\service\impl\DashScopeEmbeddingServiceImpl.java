package org.senyor.ai.service.impl;

import com.alibaba.dashscope.embeddings.TextEmbedding;
import com.alibaba.dashscope.embeddings.TextEmbeddingParam;
import com.alibaba.dashscope.embeddings.TextEmbeddingResult;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.config.DashScopeConfig;
import org.senyor.ai.service.IEmbeddingService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 基于阿里云DashScope的向量化服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DashScopeEmbeddingServiceImpl implements IEmbeddingService {

    private final DashScopeConfig dashScopeConfig;
    private final ObjectMapper objectMapper;

    // 使用text-embedding-v2模型，向量维度为1536
    private static final String EMBEDDING_MODEL = "text-embedding-v2";
    private static final int VECTOR_DIMENSION = 1536;

    @Override
    public List<Float> getEmbedding(String text) {
        try {
            log.debug("开始向量化文本，长度: {}", text.length());

            // 创建向量化参数
            TextEmbeddingParam param = TextEmbeddingParam.builder()
                .model(EMBEDDING_MODEL)
                .text(text)
                .apiKey(dashScopeConfig.getDashscopeApiKey())
                .build();

            // 创建TextEmbedding实例并调用API
            TextEmbedding textEmbedding = new TextEmbedding();
            TextEmbeddingResult result = textEmbedding.call(param);

            if (result.getOutput() != null && result.getOutput().getEmbeddings() != null
                && !result.getOutput().getEmbeddings().isEmpty()) {

                // 获取第一个文本的向量并转换为Float类型
                List<Double> doubleEmbedding = result.getOutput().getEmbeddings().get(0).getEmbedding();
                List<Float> embedding = new ArrayList<>();
                for (Double d : doubleEmbedding) {
                    embedding.add(d.floatValue());
                }

                log.debug("文本向量化成功，向量维度: {}", embedding.size());
                return embedding;
            } else {
                log.error("向量化结果为空");
                throw new RuntimeException("向量化结果为空");
            }

        } catch (NoApiKeyException e) {
            log.error("DashScope API Key未配置", e);
            throw new RuntimeException("DashScope API Key未配置", e);
        } catch (ApiException e) {
            log.error("DashScope API调用失败", e);
            throw new RuntimeException("DashScope API调用失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("文本向量化失败", e);
            throw new RuntimeException("文本向量化失败", e);
        }
    }

    @Override
    public List<List<Float>> getEmbeddings(List<String> texts) {
        try {
            log.debug("开始批量向量化，文本数量: {}", texts.size());

            List<List<Float>> embeddings = new ArrayList<>();
            
            // 逐个处理每个文本，因为DashScope的text-embedding-v2模型不支持批量输入
            for (String text : texts) {
                List<Float> embedding = getEmbedding(text);
                embeddings.add(embedding);
            }

            log.debug("批量向量化成功，向量数量: {}", embeddings.size());
            return embeddings;
        } catch (Exception e) {
            log.error("批量向量化失败", e);
            throw new RuntimeException("批量向量化失败", e);
        }
    }

    @Override
    public int getVectorDimension() {
        return VECTOR_DIMENSION;
    }

    @Override
    public double calculateCosineSimilarity(List<Float> vector1, List<Float> vector2) {
        if (vector1.size() != vector2.size()) {
            throw new IllegalArgumentException("向量维度不匹配");
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.size(); i++) {
            double v1 = vector1.get(i);
            double v2 = vector2.get(i);
            dotProduct += v1 * v2;
            norm1 += v1 * v1;
            norm2 += v2 * v2;
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }
}
