<template>
    <div class="dashboard">
        <div class="header">
            <el-tooltip :content="$t('navbar.full')" effect="dark" placement="bottom">
                <screenfull id="screenfull" class="right-menu-item hover-effect left-positioned" />
            </el-tooltip>
            <dv-decoration8 style="width: 400px; height: 70px" />
            <div class="title">心理健康数据大屏</div>
            <dv-decoration8 style="width: 400px; height: 70px" :reverse="true" />
            <div class="digital-clock">{{ currentTime }}</div>
        </div>
        <div class="main-content">
            <div v-if="!showDataPointModules" class="tech-border left-panel">
                <!-- 基础数据实时看板 -->
                <dv-border-box1 class="chart-container">
                    <BasicStats :refresh-interval="refreshTime"/>
                </dv-border-box1>
                <!-- 附属单位预警分析 -->
                <dv-border-box1 class="chart-container">
                    <UnitWarning :refresh-interval="refreshTime"/>
                </dv-border-box1>
                <!-- 人员状况分析 -->
                <dv-border-box1 class="chart-container">
                    <PersonnelAnalysis :refresh-interval="refreshTime"/>
                </dv-border-box1>
            </div>

            <div class="center-area">
                <!-- 地图 -->
                <dv-border-box1 v-if="!showDataPointModules" class="tech-border map-area">
                    <DrillDownMap @level-change="handleLevelChange" @data-point-click="handleDataPointClick" />
                </dv-border-box1>
                <!-- 心理资源分布 -->
                <dv-border-box1 v-if="!showDataPointModules" class="tech-border bottom-panel">
                    <AssessmentAnalysis :refresh-interval="refreshTime"/>
                </dv-border-box1>

                <div v-if="showDataPointModules" class="data-point-modules tech-border">
                    <div class="module-header">
                        <div class="module-title">{{ selectedPoint?.name || '详细统计' }}</div>
                        <button class="back-button" @click="closeDataPointModules" >返回</button>
                    </div>
                    <div class="modules-container">
                        <div class="module-row">
                            <!-- 机构信息轮播 -->
                            <dv-border-box1 class="module-item">
                                <GradeInfoCarousel />
                            </dv-border-box1>
                            <!-- 用户总览 -->
                            <dv-border-box1 class="module-item">
                                <UserOverview />
                            </dv-border-box1>
                        </div>
                        <div class="module-row">
                            <!-- 预警分析 -->
                            <dv-border-box1 class="module-item">
                                <WarningAnalysis />
                            </dv-border-box1>
                            <!-- 心理测评看板 -->
                            <dv-border-box1 class="module-item">
                                <AssessmentEvaluation />
                            </dv-border-box1>
                        </div>
                        <div class="module-row">
                            <!-- 设备终端看板 -->
                            <dv-border-box1 class="module-item">
                                <DeviceStatusBoard />
                            </dv-border-box1>
                            <!-- 科普减压雷达图 -->
                            <dv-border-box1 class="module-item">
                                <ScienceReliefRadar />
                            </dv-border-box1>
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="!showDataPointModules" class="tech-border right-panel">
                <dv-border-box1 class="chart-container">
                    <ConsultingStats :refresh-interval="refreshTime"/>
                </dv-border-box1>
                <!-- 科普减压 -->
                <dv-border-box1 class="chart-container">
                    <ScienceRelief :refresh-interval="refreshTime"/>
                </dv-border-box1>
            </div>
        </div>
        <div class="floating-particles"></div>
    </div>
</template>

<script setup lang="ts">
import { BorderBox1 as DvBorderBox1 } from '@kjgl77/datav-vue3';
import { onMounted, ref, reactive, provide, onBeforeUnmount, nextTick, watch } from 'vue';
import DrillDownMap from '@/components/DataScreen/DrillDownMap.vue';
import { DataPointProperties, MapFeatureProperties, MapLevel } from '@/components/DataScreen/dataWorker/dataScreenTypes';
import UnitWarning from '@/components/DataScreen/modules/UnitWarning.vue';
import BasicStats from '@/components/DataScreen/modules/BasicStats.vue';
import PersonnelAnalysis from '@/components/DataScreen/modules/PersonnelAnalysis.vue';
import AssessmentAnalysis from '@/components/DataScreen/modules/AssessmentAnalysis.vue';
import ConsultingStats from '@/components/DataScreen/modules/ConsultingStats.vue';
import ScienceRelief from '@/components/DataScreen/modules/ScienceRelief.vue';
import BottomModule from '@/components/DataScreen/modules/BottomModule.vue';
import EventEmitter from '@/utils/EventEmitter';

// 新模块导入
import UserOverview from '@/components/DataScreen/modules/UserOverview.vue';
import GradeInfoCarousel from '@/components/DataScreen/modules/GradeInfoCarousel.vue';
import WarningAnalysis from '@/components/DataScreen/modules/WarningAnalysis.vue';
import AssessmentEvaluation from '@/components/DataScreen/modules/AssessmentEvaluation.vue';
import ScienceReliefRadar from '@/components/DataScreen/modules/ScienceReliefRadar.vue';
import DeviceStatusBoard from '@/components/DataScreen/modules/DeviceStatusBoard.vue';
import {dynamicClear} from "@/api/system/tenant";

// 数据点模块显示控制
const showDataPointModules = ref(false);
const selectedPoint = ref<DataPointProperties | null>(null);
const refreshTime = 60000; // 组件刷新时间 默认1分钟
// 数字时钟
const currentTime = ref('00:00:00');
let clockTimer: number | null = null;

const updateClock = () => {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const date = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const day = days[now.getDay()];
    currentTime.value = `${year}-${month}-${date} ${hours}:${minutes}:${seconds} ${day}`;
};

// 区域统计数据
const areaStats = reactive({
    counselors: 0,
    users: 0
});

// 区域详情标签页配置
const regionTabs = [
    { id: 'basic', name: '基础数据' },
    { id: 'personnel', name: '人员状况' },
    { id: 'assessment', name: '测评活动' },
    { id: 'consulting', name: '心理咨询' },
    { id: 'science', name: '科普减压' },
    { id: 'warning', name: '单位预警' }
];

// 当前激活的区域标签页
const activeRegionTab = ref('basic');

// 当前选择的区域
const currentRegion = ref({ id: 'all', name: '全部' });

// 用于在组件间共享的事件总线
const regionChangeEvent = new EventEmitter();

// 是否是机构级账号
const isTenantLevel = ref(false)

// 提供事件总线给所有子组件
provide('regionChangeEvent', regionChangeEvent);

const handleLevelChange = (level: MapLevel, feature: MapFeatureProperties) => {
    console.log('层级变化:', level, JSON.stringify(feature));
    // 更新当前区域信息
    currentRegion.value = {
        id: feature.adcode?.toString() || 'all',
        name: feature.name || '全部'
    };
    console.log(currentRegion.value)
    if (level == 'town'){
        showDataPointModules.value = true
        isTenantLevel.value = true
    }

    // 对数据进行更显著的变化处理
    // 根据层级和区域ID生成一个随机乘数，实现层级越深数据越少的效果
    const generateRandomMultiplier = () => {
        const levelMultipliers = {
            'nation': 1,
            'province': 0.6 + Math.random() * 0.2,
            'city': 0.3 + Math.random() * 0.15,
            'district': 0.1 + Math.random() * 0.1
        };

        const baseMultiplier = levelMultipliers[level] || 1;
        return baseMultiplier * (0.8 + Math.random() * 0.4);
    };

    // 创建更显著的数据变化对象
    const dataChangeInfo = {
        multiplier: generateRandomMultiplier(),
        level,
        feature,
        region: currentRegion.value
    };

    // 通知所有模块区域变化 - 带有更显著变化因子
    regionChangeEvent.emit('region-change', dataChangeInfo);

    // 模拟获取该区域的统计数据 - 也使用更显著的变化因子
    fetchRegionStats(currentRegion.value.id, dataChangeInfo.multiplier);

    // 触发所有图表重新渲染
    nextTick(() => {
        // 立即触发一次window resize事件，这会使echarts图表自动调整大小和重绘
        window.dispatchEvent(new Event('resize'));

        // 额外添加一个延迟触发，确保数据更新后图表能正确渲染
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
            console.log('触发图表更新完成');
        }, 300);
    });
};

// 模拟获取区域统计数据的函数
const fetchRegionStats = (regionId: string, multiplier = 1) => {
    console.log(`获取区域 ${regionId} 的统计数据，变化因子: ${multiplier}`);

    // 模拟数据更新 - 使用更显著的变化因子
    areaStats.counselors = Math.floor(Math.random() * 100 * multiplier) + 20;
    areaStats.users = Math.floor(Math.random() * 1000 * multiplier) + 200;
};

// 观察数据点模块显示状态的变化
watch(showDataPointModules, (newValue) => {
    if (newValue) {
        // 当显示模块时，延迟触发resize以确保所有图表正确渲染
        nextTick(() => {
            console.log('准备触发resize事件');

            // 立即触发resize事件
            window.dispatchEvent(new Event('resize'));

            // 然后使用递增延迟多次触发，确保所有图表都能正确渲染
            const delays = [100, 300, 600, 1000, 1500, 2000];
            delays.forEach((delay) => {
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'));
                    console.log(`${delay}ms后触发resize事件`);
                }, delay);
            });
        });
    }
});

// 监听全屏变化
const handleFullscreenChange = () => {
    // 全屏状态变化时，触发图表重绘
    console.log('全屏状态变化，触发图表重绘');

    // 使用多个延迟确保在全屏转换完成后重绘图表
    const delays = [100, 300, 600, 1000];
    delays.forEach((delay) => {
        setTimeout(() => {
            window.dispatchEvent(new Event('resize'));
        }, delay);
    });
};

// 修改 handleDataPointClick 函数
const handleDataPointClick = (point: DataPointProperties) => {
    console.log('收到数据点点击:', point);

    // 保存选中的数据点
    selectedPoint.value = point;

    // 显示数据点相关模块
    showDataPointModules.value = true;

    // 点击数据点时也可以触发相关数据更新
    regionChangeEvent.emit('data-point-click', point);
};

const clearTenant = async () => {
    await dynamicClear()
}

// 关闭数据点模块，返回主视图
const closeDataPointModules = async () => {
    await clearTenant()
    showDataPointModules.value = false;
    selectedPoint.value = null;
};

// 创建浮动粒子效果
const createParticles = () => {
    const particlesContainer = document.querySelector('.floating-particles');
    if (!particlesContainer) return;

    // 清空容器
    particlesContainer.innerHTML = '';

    // 创建粒子
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';

        // 随机位置
        const x = Math.random() * 100;
        const y = Math.random() * 100;

        // 随机大小
        const size = Math.random() * 5 + 1;

        // 随机动画延迟
        const delay = Math.random() * 5;

        // 随机动画持续时间
        const duration = Math.random() * 20 + 10;

        // 设置样式
        particle.style.left = `${x}%`;
        particle.style.top = `${y}%`;
        particle.style.width = `${size}px`;
        particle.style.height = `${size}px`;
        particle.style.animationDelay = `${delay}s`;
        particle.style.animationDuration = `${duration}s`;

        particlesContainer.appendChild(particle);
    }
};

onMounted(() => {
    // 初始化时钟
    updateClock();
    clockTimer = window.setInterval(updateClock, 1000);

    // 初始化粒子效果
    createParticles();

    // 初始化时加载默认数据
    fetchRegionStats('all');

    // 添加全屏变化事件监听
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('mozfullscreenchange', handleFullscreenChange);
    document.addEventListener('MSFullscreenChange', handleFullscreenChange);
});

onBeforeUnmount(() => {
    // 清理定时器
    if (clockTimer) {
        clearInterval(clockTimer);
    }

    // 移除全屏变化事件监听
    document.removeEventListener('fullscreenchange', handleFullscreenChange);
    document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.removeEventListener('mozfullscreenchange', handleFullscreenChange);
    document.removeEventListener('MSFullscreenChange', handleFullscreenChange);
});
</script>

<style>
.dashboard {
    position: relative;
    width: 100vw;
    height: 100vh;
    padding: 0;
    background: #033c76;
    background-image: radial-gradient(circle at 20% 80%, rgba(41, 103, 188, 0.2), transparent),
        radial-gradient(circle at 80% 20%, rgba(12, 55, 118, 0.3), transparent),
        linear-gradient(to bottom, rgba(16, 35, 75, 0.8), rgba(16, 35, 75, 0.6));
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* 数据点点击后显示的模块样式 */
.data-point-modules {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 12px;
    box-sizing: border-box;
    background: rgba(16, 35, 75, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(5px);
}

.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    height: 30px;
}

.module-title {
    font-size: 18px;
    color: #4ecdc4;
    font-weight: bold;
}

.back-button {
    background: rgba(24, 144, 255, 0.2);
    border: 1px solid rgba(24, 144, 255, 0.5);
    color: #4ecdc4;
    font-size: 14px;
    padding: 5px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
}

.back-button:hover {
    background: rgba(24, 144, 255, 0.4);
}

.modules-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 12px;
    height: calc(100% - 36px);
}

.module-row {
    flex: 1;
    display: flex;
    gap: 12px;
    height: calc(33.333% - 8px);
    min-height: 0;
}

.module-item {
    flex: 1;
    background: rgba(16, 35, 75, 0.3);
    border-radius: 6px;
    backdrop-filter: blur(3px);
    overflow: hidden;
    transition: all 0.3s;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(78, 205, 196, 0.1);
    height: 100%;
    display: flex;
    flex-direction: column;
    max-height: 100%;
}

.module-item > * {
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.module-item:hover {
    box-shadow: 0 5px 15px rgba(78, 205, 196, 0.2);
    transform: translateY(-2px);
}

/* 添加科技风格边框 */
.tech-border {
    position: relative;
    overflow: hidden;
}

.tech-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: 1px solid rgba(78, 205, 196, 0.1);
    border-radius: 8px;
    box-sizing: border-box;
    pointer-events: none;
}

.tech-border::after {
    content: '';
    position: absolute;
    top: -1px;
    left: -1px;
    width: 20%;
    height: 1px;
    background: linear-gradient(to right, transparent, #4ecdc4);
    animation: techBorderAnim 4s infinite linear;
}

@keyframes techBorderAnim {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(500%);
    }
}

/* 浮动粒子效果 */
.floating-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 0;
}

.particle {
    position: absolute;
    background-color: rgba(78, 205, 196, 0.3);
    border-radius: 50%;
    opacity: 0;
    animation: float linear infinite;
}

@keyframes float {
    0% {
        transform: translateY(0) translateX(0);
        opacity: 0;
    }

    10% {
        opacity: 0.8;
    }

    90% {
        opacity: 0.4;
    }

    100% {
        transform: translateY(-100px) translateX(20px);
        opacity: 0;
    }
}

.header {
    height: 70px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    background: rgba(0, 20, 40, 0.5);
    position: relative;
    overflow: hidden;
    z-index: 10;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
}

.digital-clock {
    position: absolute;
    right: 20px;
    font-size: 20px;
    font-family: 'Courier New', monospace;
    letter-spacing: 2px;
    color: #4ecdc4;
    text-shadow: 0 0 8px rgba(78, 205, 196, 0.7);
}

/* 左侧定位的全屏按钮样式 */
.left-positioned {
    position: absolute;
    left: 20px;
    z-index: 20;
    color: #4ecdc4;
    cursor: pointer;
    font-size: 20px;
    transition: all 0.3s;
}

.left-positioned:hover {
    transform: scale(1.2);
    color: #fff;
    text-shadow: 0 0 8px rgba(78, 205, 196, 0.9);
}

.header::before,
.header::after {
    content: '';
    position: absolute;
    height: 1px;
    width: 50%;
    background: linear-gradient(to right, transparent, #4ecdc4, transparent);
}

.header::before {
    top: 0;
    left: 25%;
    animation: scanHeaderLine 6s infinite linear;
}

.header::after {
    bottom: 0;
    left: 25%;
    animation: scanHeaderLine 6s infinite linear 3s;
}

@keyframes scanHeaderLine {
    0% {
        left: 0%;
        width: 0%;
        opacity: 0;
    }

    10% {
        opacity: 1;
        width: 25%;
    }

    50% {
        width: 50%;
    }

    90% {
        width: 25%;
        opacity: 1;
    }

    100% {
        left: 100%;
        width: 0%;
        opacity: 0;
    }
}

.title {
    font-size: 38px;
    font-weight: bold;
    color: #4ecdc4;
    letter-spacing: 6px;
    text-shadow: 0 0 18px rgba(78, 205, 196, 0.9);
    position: relative;
    animation: titleGlow 3s infinite;
    z-index: 15;
    padding: 0 30px;
    margin: 0 15px;
    background: linear-gradient(to right, rgba(0, 44, 81, 0.1), rgba(0, 44, 81, 0.5), rgba(0, 44, 81, 0.1));
    border-radius: 5px;
    border-top: 1px solid rgba(78, 205, 196, 0.3);
    border-bottom: 1px solid rgba(78, 205, 196, 0.3);
}

@keyframes titleGlow {
    0% {
        text-shadow: 0 0 18px rgba(78, 205, 196, 0.5);
    }

    50% {
        text-shadow: 0 0 30px rgba(78, 205, 196, 1);
    }

    100% {
        text-shadow: 0 0 18px rgba(78, 205, 196, 0.5);
    }
}

.title::before,
.title::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 30px;
    height: 2px;
    background: linear-gradient(to right, transparent, #4ecdc4);
}

.title::before {
    left: -40px;
    transform: translateY(-50%);
}

.title::after {
    right: -40px;
    transform: translateY(-50%) rotate(180deg);
}

.main-content {
    flex: 1;
    display: flex;
    width: 100%;
    padding: 0.5vw;
    box-sizing: border-box;
    background-image: linear-gradient(to right, rgba(32, 74, 135, 0.05) 1px, transparent 1px),
        linear-gradient(to bottom, rgba(32, 74, 135, 0.05) 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
    z-index: 5;
    min-height: 0;
    height: calc(100% - 70px);
}

.left-panel,
.right-panel {
    width: 28%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background: transparent;
    z-index: 10;
    transition: all 0.5s ease;
    overflow: hidden;
}

.center-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
    min-height: 0;
    margin: 0 0.5vw;
    overflow: hidden;
}

.map-area {
    flex: 1.7;
    position: relative;
    overflow: hidden;
    transition: all 0.5s ease;
    min-height: 0;
    border-radius: 8px;
    backdrop-filter: blur(5px);
    background: rgba(16, 35, 75, 0.2);
    height: 0;
}

.bottom-panel {
    flex: 1;
    margin-top: 0.5vw;
    background: rgba(16, 35, 75, 0.2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
    min-height: 0;
    display: flex;
    backdrop-filter: blur(5px);
    height: 0;
}

.bottom-panel::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(to right, transparent, rgba(78, 205, 196, 0.3), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% {
        opacity: 0.3;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.3;
    }
}

.left-panel.hidden {
    transform: translateX(-110%);
    opacity: 0;
    width: 0;
}

.right-panel.hidden {
    transform: translateX(110%);
    opacity: 0;
    width: 0;
}

.map-area.expanded {
    width: 100%;
    padding: 0;
}

.chart-container {
    position: relative;
    margin-bottom: 0.5vw;
    background: rgba(16, 35, 75, 0.2);
    border-radius: 8px;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

/* 调整左侧面板的模块高度比例 */
.left-panel .chart-container:nth-child(1) {
    height: 31%;
}

.left-panel .chart-container:nth-child(2) {
    height: 31%;
}

.left-panel .chart-container:nth-child(3) {
    height: calc(38% - 0.5vw);
}

/* 调整右侧面板的模块高度比例 */
.right-panel .chart-container:nth-child(1) {
    height: 48%;
    display: flex;
    overflow: hidden;
}

.right-panel .chart-container:nth-child(2) {
    height: calc(52% - 0.5vw);
    margin-bottom: 0;
    display: flex;
    overflow: hidden;
}

.chart-container:hover {
    box-shadow: 0 0 15px rgba(78, 205, 196, 0.2);
    transform: translateY(-2px);
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(78, 205, 196, 0.1) 0%, transparent 100px),
        linear-gradient(45deg, transparent calc(100% - 100px), rgba(78, 205, 196, 0.1) 100%);
    opacity: 0.5;
    z-index: -1;
}

.chart-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    border-top: 2px solid rgba(78, 205, 196, 0.5);
    border-left: 2px solid rgba(78, 205, 196, 0.5);
    border-top-left-radius: 8px;
    animation: borderPulse 3s infinite;
}

@keyframes borderPulse {
    0% {
        opacity: 0.3;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0.3;
    }
}

/* 添加动态光效 */
@keyframes scanline {
    0% {
        transform: translateY(-100%);
        opacity: 0;
    }

    10% {
        opacity: 0.3;
    }

    90% {
        opacity: 0.3;
    }

    100% {
        transform: translateY(100%);
        opacity: 0;
    }
}

.dashboard::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(to bottom, transparent, rgba(78, 205, 196, 0.2), transparent);
    animation: scanline 8s linear infinite;
    pointer-events: none;
    z-index: 3;
}

/* 根据视窗大小的适配 */
@media screen and (min-width: 1920px) {
    .main-content {
        padding: 0.6vw;
    }

    .left-panel,
    .right-panel {
        width: 26%;
    }

    .center-area {
        flex: 1.2;
        margin: 0 0.6vw;
    }

    .bottom-panel {
        margin-top: 0.6vw;
    }

    .chart-container {
        margin-bottom: 0.6vw;
    }

    .title {
        font-size: 42px;
    }

    /* 调整左侧面板的模块高度比例 */
    .left-panel .chart-container:nth-child(3) {
        height: calc(38% - 0.6vw);
    }

    /* 调整右侧面板的模块高度比例 */
    .right-panel .chart-container:nth-child(2) {
        height: calc(52% - 0.6vw);
    }
}

@media screen and (min-width: 2560px) {
    .main-content {
        padding: 0.8vw;
    }

    .left-panel,
    .right-panel {
        width: 24%;
    }

    .center-area {
        flex: 1.5;
        margin: 0 0.8vw;
    }

    .title {
        font-size: 48px;
        letter-spacing: 8px;
    }

    .header {
        height: 60px;
    }

    .bottom-panel {
        margin-top: 0.8vw;
    }

    .chart-container {
        margin-bottom: 0.8vw;
    }

    /* 调整左侧面板的模块高度比例 */
    .left-panel .chart-container:nth-child(3) {
        height: calc(38% - 0.8vw);
    }

    /* 调整右侧面板的模块高度比例 */
    .right-panel .chart-container:nth-child(2) {
        height: calc(52% - 0.8vw);
    }
}

/* 防止超出屏幕和变形的关键修复 */
@media screen and (max-height: 900px) {
    .chart-container,
    .map-area,
    .bottom-panel {
        transform: none !important;
        margin-bottom: 0.3vw;
    }

    .left-panel .chart-container:nth-child(1) {
        height: 32%;
    }

    .left-panel .chart-container:nth-child(2) {
        height: 32%;
    }

    .left-panel .chart-container:nth-child(3) {
        height: calc(36% - 0.3vw);
    }

    .right-panel .chart-container:nth-child(1) {
        height: 49%;
    }

    .right-panel .chart-container:nth-child(2) {
        height: calc(51% - 0.3vw);
    }

    .bottom-panel {
        margin-top: 0.3vw;
    }
}
</style>
