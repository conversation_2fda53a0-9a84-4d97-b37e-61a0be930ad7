/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AIChat: typeof import('./../components/AI/AIChat.vue')['default']
    AppointmentUserSelect: typeof import('./../components/AppointmentUserSelect/index.vue')['default']
    ApprovalRecord: typeof import('./../components/Process/approvalRecord.vue')['default']
    AssessmentAnalysis: typeof import('./../components/DataScreen/modules/AssessmentAnalysis.vue')['default']
    AssessmentEvaluation: typeof import('./../components/DataScreen/modules/AssessmentEvaluation.vue')['default']
    AudioUpload: typeof import('./../components/AudioUpload/index.vue')['default']
    BaseInfo: typeof import('./../components/ToolkitSelect/config/baseInfo.vue')['default']
    BasicStats: typeof import('./../components/DataScreen/modules/BasicStats.vue')['default']
    BottomModule: typeof import('./../components/DataScreen/modules/BottomModule.vue')['default']
    BpmnDesign: typeof import('./../components/BpmnDesign/index.vue')['default']
    BpmnView: typeof import('./../components/BpmnView/index.vue')['default']
    Breadcrumb: typeof import('./../components/Breadcrumb/index.vue')['default']
    BuildCode: typeof import('./../components/BuildCode/index.vue')['default']
    ConsultantManagement: typeof import('./../components/ConsultantManagement/index.vue')['default']
    ConsultingStats: typeof import('./../components/DataScreen/modules/ConsultingStats.vue')['default']
    Dashboard: typeof import('./../components/DataScreen/dashboard.vue')['default']
    DeviceStatusBoard: typeof import('./../components/DataScreen/modules/DeviceStatusBoard.vue')['default']
    DictTag: typeof import('./../components/DictTag/index.vue')['default']
    DistrictTree: typeof import('./../components/DistrictTree/index.vue')['default']
    DrillDownMap: typeof import('./../components/DataScreen/DrillDownMap.vue')['default']
    EChartsWrapper: typeof import('./../components/EChartsWrapper/index.vue')['default']
    Editor: typeof import('./../components/Editor/index.vue')['default']
    ElAutocomplete: typeof import('element-plus/es')['ElAutocomplete']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElBreadcrumb: typeof import('element-plus/es')['ElBreadcrumb']
    ElBreadcrumbItem: typeof import('element-plus/es')['ElBreadcrumbItem']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCheckboxGroup: typeof import('element-plus/es')['ElCheckboxGroup']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDrawer: typeof import('element-plus/es')['ElDrawer']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElMenu: typeof import('element-plus/es')['ElMenu']
    ElMenuItem: typeof import('element-plus/es')['ElMenuItem']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElScrollbar: typeof import('element-plus/es')['ElScrollbar']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSubMenu: typeof import('element-plus/es')['ElSubMenu']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElText: typeof import('element-plus/es')['ElText']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    FastQuestionsHand: typeof import('./../components/ScaleQuestions/fastQuestionsHand.vue')['default']
    FieldSelect: typeof import('./../components/FieldSelect/index.vue')['default']
    FileUpload: typeof import('./../components/FileUpload/index.vue')['default']
    GradeInfoCarousel: typeof import('./../components/DataScreen/modules/GradeInfoCarousel.vue')['default']
    Hamburger: typeof import('./../components/Hamburger/index.vue')['default']
    HeaderSearch: typeof import('./../components/HeaderSearch/index.vue')['default']
    IconSelect: typeof import('./../components/IconSelect/index.vue')['default']
    IFrame: typeof import('./../components/iFrame/index.vue')['default']
    ImageDisplay: typeof import('./../components/ImageDisplay/index.vue')['default']
    ImagePreview: typeof import('./../components/ImagePreview/index.vue')['default']
    ImageUpload: typeof import('./../components/ImageUpload/index.vue')['default']
    LangSelect: typeof import('./../components/LangSelect/index.vue')['default']
    MultiInstanceUser: typeof import('./../components/Process/multiInstanceUser.vue')['default']
    PageAndView: typeof import('./../components/PageAndView/index.vue')['default']
    Pagination: typeof import('./../components/Pagination/index.vue')['default']
    ParentView: typeof import('./../components/ParentView/index.vue')['default']
    PDFUpload: typeof import('./../components/PDFUpload/PDFUpload.vue')['default']
    PersonnelAnalysis: typeof import('./../components/DataScreen/modules/PersonnelAnalysis.vue')['default']
    PersonReport: typeof import('./../components/PersonReport/index.vue')['default']
    PublicImageUpload: typeof import('./../components/PublicImageUpload/index.vue')['default']
    QuestionCheckBox: typeof import('./../components/SurveyQuestions/QuestionCheckBox.vue')['default']
    QuestionFill: typeof import('./../components/SurveyQuestions/QuestionFill.vue')['default']
    QuestionInput: typeof import('./../components/SurveyQuestions/QuestionInput.vue')['default']
    QuestionRadio: typeof import('./../components/SurveyQuestions/QuestionRadio.vue')['default']
    QuestionSelect: typeof import('./../components/SurveyQuestions/QuestionSelect.vue')['default']
    QuestionTextarea: typeof import('./../components/SurveyQuestions/QuestionTextarea.vue')['default']
    QuestionUpload: typeof import('./../components/SurveyQuestions/QuestionUpload.vue')['default']
    QuizPage: typeof import('./../components/ScaleQuestions/QuizPage.vue')['default']
    RecommendSceneSelect: typeof import('./../components/RecommendSceneSelect/index.vue')['default']
    Render: typeof import('./../components/BuildCode/render.vue')['default']
    RightToolbar: typeof import('./../components/RightToolbar/index.vue')['default']
    RiskConfig: typeof import('./../components/ToolkitSelect/config/riskConfig.vue')['default']
    RiskReport: typeof import('./../components/RiskReport/index.vue')['default']
    RiskToolkitSelect: typeof import('./../components/RiskToolkitSelect/index.vue')['default']
    RoleSceneSelect: typeof import('./../components/RoleSceneSelect/index.vue')['default']
    RoleSelect: typeof import('./../components/RoleSelect/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RuoYiDoc: typeof import('./../components/RuoYiDoc/index.vue')['default']
    RuoYiGit: typeof import('./../components/RuoYiGit/index.vue')['default']
    ScaleConfig: typeof import('./../components/ToolkitSelect/config/scaleConfig.vue')['default']
    ScaleInfoReport: typeof import('./../components/ScaleInfoReport/index.vue')['default']
    ScaleSelect: typeof import('./../components/ScaleSelect/index.vue')['default']
    ScenarioSelect: typeof import('./../components/ScenarioSelect/index.vue')['default']
    ScienceRelief: typeof import('./../components/DataScreen/modules/ScienceRelief.vue')['default']
    ScienceReliefRadar: typeof import('./../components/DataScreen/modules/ScienceReliefRadar.vue')['default']
    Screenfull: typeof import('./../components/Screenfull/index.vue')['default']
    SizeSelect: typeof import('./../components/SizeSelect/index.vue')['default']
    SubmitVerify: typeof import('./../components/Process/submitVerify.vue')['default']
    SvgIcon: typeof import('./../components/SvgIcon/index.vue')['default']
    ToolkitSelect: typeof import('./../components/ToolkitSelect/index.vue')['default']
    TopNav: typeof import('./../components/TopNav/index.vue')['default']
    TreeSelect: typeof import('./../components/TreeSelect/index.vue')['default']
    UnitWarning: typeof import('./../components/DataScreen/modules/UnitWarning.vue')['default']
    UserOverview: typeof import('./../components/DataScreen/modules/UserOverview.vue')['default']
    UserSelect: typeof import('./../components/UserSelect/index.vue')['default']
    UserSelector: typeof import('./../components/UserSelector/index.vue')['default']
    UserSelectorSingle: typeof import('./../components/UserSelectorSingle/index.vue')['default']
    VideoUpload: typeof import('./../components/VideoUpload/index.vue')['default']
    WarningAnalysis: typeof import('./../components/DataScreen/modules/WarningAnalysis.vue')['default']
  }
  export interface ComponentCustomProperties {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
