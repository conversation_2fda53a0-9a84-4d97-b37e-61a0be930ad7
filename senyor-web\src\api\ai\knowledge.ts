import request from '@/utils/request'

// 查询AI知识库列表
export function listKnowledge(query) {
  return request({
    url: '/ai/knowledge/list',
    method: 'get',
    params: query
  })
}

// 查询AI知识库详细
export function getKnowledge(knowledgeId) {
  return request({
    url: '/ai/knowledge/' + knowledgeId,
    method: 'get'
  })
}

// 新增AI知识库
export function addKnowledge(data) {
  return request({
    url: '/ai/knowledge',
    method: 'post',
    data: data
  })
}

// 修改AI知识库
export function updateKnowledge(data) {
  return request({
    url: '/ai/knowledge',
    method: 'put',
    data: data
  })
}

// 删除AI知识库
export function delKnowledge(knowledgeId) {
  return request({
    url: '/ai/knowledge/' + knowledgeId,
    method: 'delete'
  })
}

// 获取启用的知识库列表
export function getEnabledKnowledgeBases() {
  return request({
    url: '/ai/knowledge/enabled',
    method: 'get'
  })
}

// 获取用户知识库列表
export function getUserKnowledgeBases() {
  return request({
    url: '/ai/knowledge/user',
    method: 'get'
  })
}

// 更新知识库状态
export function updateKnowledgeStatus(knowledgeId, status) {
  return request({
    url: '/ai/knowledge/status/' + knowledgeId + '/' + status,
    method: 'put'
  })
}

// 兼容AIChat.vue的导入
export const getKnowledgeList = listKnowledge; 