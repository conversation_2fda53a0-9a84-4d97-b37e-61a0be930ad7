package org.senyor.ai.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.senyor.ai.domain.AiKnowledgeBase;
import org.senyor.ai.domain.vo.AiKnowledgeBaseVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * AI知识库数据层
 * 
 * <AUTHOR>
 */
public interface AiKnowledgeBaseMapper extends BaseMapper<AiKnowledgeBase> {
    
    /**
     * 查询单个知识库
     * 
     * @param knowledgeId 知识库ID
     * @return AI知识库对象
     */
    AiKnowledgeBaseVo selectAiKnowledgeBaseById(Long knowledgeId);
    
    /**
     * 根据用户ID查询知识库列表
     * 
     * @param userId 用户ID
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> selectAiKnowledgeBasesByUserId(Long userId);
    
    /**
     * 查询所有知识库
     * 
     * @param aiKnowledgeBase 查询参数
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> selectAiKnowledgeBaseList(AiKnowledgeBase aiKnowledgeBase);
    
    /**
     * 查询启用的知识库列表
     * 
     * @return 启用的知识库列表
     */
    List<AiKnowledgeBaseVo> selectEnabledKnowledgeBases();
    
    /**
     * 根据ID数组查询知识库列表
     * 
     * @param knowledgeIds 知识库ID数组
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> selectAiKnowledgeBasesByIds(@Param("knowledgeIds") Long[] knowledgeIds);
} 