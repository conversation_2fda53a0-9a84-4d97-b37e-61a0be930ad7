package org.senyor.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.ai.service.IKnowledgeRetrievalService.KnowledgeChunk;
import org.senyor.common.redis.utils.RedisUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * Redis向量存储服务实现
 * 基于Redis的高性能向量存储和检索服务
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class RedisVectorStoreServiceImpl implements IVectorStoreService {

    private final ObjectMapper objectMapper;

    // 向量维度
    private static final int VECTOR_DIMENSION = 1536;

    // Redis key前缀（使用全局前缀，避免租户隔离）
    private static final String VECTOR_PREFIX = "global:ai:vector:";
    private static final String INDEX_PREFIX = "global:ai:index:";
    private static final String METADATA_PREFIX = "global:ai:metadata:";

    // 线程池用于并行处理
    private final ExecutorService executorService = Executors.newFixedThreadPool(4);

    @Override
    public boolean createIndex(String indexName) {
        try {
            // Redis不需要显式创建索引，使用key前缀即可
            log.info("Redis向量索引准备就绪: {}", indexName);
            return true;
        } catch (Exception e) {
            log.error("Redis向量索引初始化失败: {}", indexName, e);
            return false;
        }
    }

    @Override
    public boolean storeVector(String indexName, Long chunkId, String content, List<Float> embedding, String metadata) {
        try {
            String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkId;
            String metadataKey = METADATA_PREFIX + indexName + ":" + chunkId;

            // 存储向量数据
            Map<String, Object> vectorData = new HashMap<>();
            vectorData.put("chunkId", chunkId);
            vectorData.put("content", content);
            vectorData.put("embedding", embedding);
            vectorData.put("metadata", metadata);
            vectorData.put("timestamp", System.currentTimeMillis());
            vectorData.put("dimension", embedding.size());

            // 使用Redis Hash存储，设置过期时间（30天）
            RedisUtils.setCacheMap(vectorKey, vectorData);
            RedisUtils.expire(vectorKey, 30 * 24 * 60 * 60); // 30天过期

            // 存储元数据索引
            RedisUtils.setCacheObject(metadataKey, metadata);
            RedisUtils.expire(metadataKey, 30 * 24 * 60 * 60);

            // 添加到索引集合
            String indexKey = INDEX_PREFIX + indexName;
            RedisUtils.addCacheSet(indexKey, chunkId.toString());

            log.debug("成功存储向量到Redis，chunkId: {}, index: {}, 维度: {}", chunkId, indexName, embedding.size());
            return true;

        } catch (Exception e) {
            log.error("存储向量到Redis失败，chunkId: {}", chunkId, e);
            return false;
        }
    }

    @Override
    public int batchStoreVectors(String indexName, List<VectorData> vectors) {
        try {
            if (vectors == null || vectors.isEmpty()) {
                log.warn("批量存储向量：向量列表为空");
                return 0;
            }

            log.info("开始批量存储向量到Redis，数量: {}", vectors.size());

            // 使用并行处理提高性能
            List<CompletableFuture<Boolean>> futures = vectors.stream()
                .map(vector -> CompletableFuture.supplyAsync(() ->
                    storeVector(indexName, vector.getChunkId(), vector.getContent(),
                               vector.getEmbedding(), vector.getMetadata()), executorService))
                .collect(Collectors.toList());

            // 等待所有任务完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 统计成功数量
            int successCount = (int) futures.stream()
                .mapToInt(future -> future.join() ? 1 : 0)
                .sum();

            log.info("批量存储向量到Redis完成，成功存储: {}/{} 个", successCount, vectors.size());
            return successCount;

        } catch (Exception e) {
            log.error("批量存储向量到Redis失败", e);
            return 0;
        }
    }

    @Override
    public List<KnowledgeChunk> searchSimilar(String indexName, List<Float> queryEmbedding, int topK, double minScore) {
        try {
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);

            if (chunkIds == null || chunkIds.isEmpty()) {
                log.debug("Redis索引中无向量数据: {}", indexName);
                return new ArrayList<>();
            }

            log.info("开始向量相似度搜索，查询向量维度: {}, 候选向量数量: {}, 索引key: {}",
                queryEmbedding.size(), chunkIds.size(), indexKey);
            log.info("候选chunkIds: {}", chunkIds);

            // 使用并行处理计算相似度
            List<CompletableFuture<Optional<KnowledgeChunk>>> futures = chunkIds.stream()
                .map(chunkIdStr -> CompletableFuture.supplyAsync(() ->
                    calculateSimilarityForChunk(indexName, Long.valueOf(chunkIdStr), queryEmbedding, minScore), executorService))
                .collect(Collectors.toList());

            // 等待所有计算完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

            // 收集结果并排序
            List<KnowledgeChunk> results = futures.stream()
                .map(future -> future.join())
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                .limit(topK)
                .collect(Collectors.toList());

            log.info("Redis向量相似度搜索完成，找到 {} 个结果，最高相似度: {}",
                     results.size(), results.isEmpty() ? 0 : results.get(0).getScore());
            if (!results.isEmpty()) {
                log.info("搜索结果详情: {}", results.stream()
                    .map(r -> String.format("chunkId=%d, score=%.3f, content=%s",
                        r.getChunkId(), r.getScore(), r.getContent().substring(0, Math.min(50, r.getContent().length()))))
                    .collect(Collectors.joining(", ")));
            }
            return results;

        } catch (Exception e) {
            log.error("Redis向量相似度搜索失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 为单个chunk计算相似度
     */
    private Optional<KnowledgeChunk> calculateSimilarityForChunk(String indexName, Long chunkId,
                                                                List<Float> queryEmbedding, double minScore) {
        try {
            String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkId;
            Map<String, Object> vectorData = RedisUtils.getCacheMap(vectorKey);

            if (vectorData == null || vectorData.isEmpty()) {
                return Optional.empty();
            }

            // 处理向量数据反序列化
            Object embeddingObj = vectorData.get("embedding");
            List<Float> storedEmbedding = null;

            if (embeddingObj instanceof String) {
                // 如果是JSON字符串，需要解析
                try {
                    storedEmbedding = objectMapper.readValue((String) embeddingObj,
                        objectMapper.getTypeFactory().constructCollectionType(List.class, Float.class));
                } catch (Exception e) {
                    log.warn("解析向量JSON失败: {}", embeddingObj, e);
                    return Optional.empty();
                }
            } else if (embeddingObj instanceof List) {
                // 如果是List，直接转换
                @SuppressWarnings("unchecked")
                List<Object> rawList = (List<Object>) embeddingObj;
                storedEmbedding = rawList.stream()
                    .map(obj -> {
                        if (obj instanceof Number) {
                            return ((Number) obj).floatValue();
                        }
                        return 0.0f;
                    })
                    .collect(Collectors.toList());
            }

            if (storedEmbedding == null || storedEmbedding.size() != queryEmbedding.size()) {
                log.warn("向量数据无效，chunkId: {}, 期望维度: {}, 实际维度: {}",
                    chunkId, queryEmbedding.size(),
                    storedEmbedding != null ? storedEmbedding.size() : 0);
                return Optional.empty();
            }

            // 计算余弦相似度
            double similarity = calculateCosineSimilarity(queryEmbedding, storedEmbedding);

            if (similarity >= minScore) {
                KnowledgeChunk chunk = new KnowledgeChunk();
                chunk.setContent((String) vectorData.get("content"));
                chunk.setScore(similarity);
                chunk.setChunkId(chunkId);

                // 解析元数据
                String metadata = (String) vectorData.get("metadata");
                if (metadata != null) {
                    try {
                        Map<String, Object> metadataMap = objectMapper.readValue(metadata, Map.class);
                        chunk.setDocumentId(Long.valueOf((Integer) metadataMap.get("documentId")));
                        chunk.setKnowledgeId(Long.valueOf((Integer) metadataMap.get("knowledgeId")));
                        chunk.setSource((String) metadataMap.get("documentName"));
                    } catch (Exception e) {
                        log.warn("解析元数据失败: {}", metadata, e);
                    }
                }

                return Optional.of(chunk);
            }

            return Optional.empty();

        } catch (Exception e) {
            log.warn("计算chunk相似度失败，chunkId: {}", chunkId, e);
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteVector(String indexName, Long chunkId) {
        try {
            String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkId;
            String metadataKey = METADATA_PREFIX + indexName + ":" + chunkId;
            String indexKey = INDEX_PREFIX + indexName;

            // 删除向量数据
            RedisUtils.deleteObject(vectorKey);

            // 删除元数据
            RedisUtils.deleteObject(metadataKey);

            // 从索引集合中移除（这里需要特殊处理，因为Redis Set没有直接的remove方法）
            // 我们通过重新创建Set来移除元素
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);
            if (chunkIds != null) {
                chunkIds.remove(chunkId.toString());
                RedisUtils.setCacheSet(indexKey, chunkIds);
            }

            log.debug("成功删除Redis向量，chunkId: {}, index: {}", chunkId, indexName);
            return true;

        } catch (Exception e) {
            log.error("删除Redis向量失败，chunkId: {}", chunkId, e);
            return false;
        }
    }

    @Override
    public int deleteVectorsByKnowledgeId(String indexName, Long knowledgeId) {
        try {
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);

            if (chunkIds == null || chunkIds.isEmpty()) {
                return 0;
            }

            int deletedCount = 0;
            List<String> keysToDelete = new ArrayList<>();

            // 遍历所有chunkId，检查是否属于指定知识库
            for (String chunkIdStr : chunkIds) {
                try {
                    Long chunkId = Long.valueOf(chunkIdStr);
                    String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkId;
                    Map<String, Object> vectorData = RedisUtils.getCacheMap(vectorKey);

                    if (vectorData != null && vectorData.containsKey("metadata")) {
                        String metadata = (String) vectorData.get("metadata");
                        if (metadata != null) {
                            try {
                                Map<String, Object> metadataMap = objectMapper.readValue(metadata, Map.class);
                                Long docKnowledgeId = Long.valueOf((Integer) metadataMap.get("knowledgeId"));

                                if (knowledgeId.equals(docKnowledgeId)) {
                                    // 删除向量数据
                                    keysToDelete.add(vectorKey);
                                    keysToDelete.add(METADATA_PREFIX + indexName + ":" + chunkId);
                                    deletedCount++;
                                }
                            } catch (Exception e) {
                                log.warn("解析元数据失败，chunkId: {}", chunkId, e);
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的chunkId: {}", chunkIdStr);
                }
            }

            // 批量删除
            if (!keysToDelete.isEmpty()) {
                RedisUtils.deleteObject(keysToDelete);

                // 从索引集合中移除
                chunkIds.removeAll(keysToDelete.stream()
                    .map(key -> key.substring(key.lastIndexOf(":") + 1))
                    .collect(Collectors.toSet()));
                RedisUtils.setCacheSet(indexKey, chunkIds);
            }

            log.info("成功删除知识库的Redis向量数据，知识库ID: {}, 删除数量: {}", knowledgeId, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除知识库向量数据失败，知识库ID: {}", knowledgeId, e);
            return 0;
        }
    }

    @Override
    public int deleteVectorsByDocumentId(String indexName, Long documentId) {
        try {
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);

            if (chunkIds == null || chunkIds.isEmpty()) {
                return 0;
            }

            int deletedCount = 0;
            List<String> keysToDelete = new ArrayList<>();
            Set<String> chunkIdsToRemove = new HashSet<>();

            // 遍历所有chunkId，检查是否属于指定文档
            for (String chunkIdStr : chunkIds) {
                try {
                    Long chunkId = Long.valueOf(chunkIdStr);
                    String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkId;
                    Map<String, Object> vectorData = RedisUtils.getCacheMap(vectorKey);

                    if (vectorData != null && vectorData.containsKey("metadata")) {
                        String metadata = (String) vectorData.get("metadata");
                        if (metadata != null) {
                            try {
                                Map<String, Object> metadataMap = objectMapper.readValue(metadata, Map.class);
                                Long docDocumentId = Long.valueOf((Integer) metadataMap.get("documentId"));

                                if (documentId.equals(docDocumentId)) {
                                    // 删除向量数据
                                    keysToDelete.add(vectorKey);
                                    keysToDelete.add(METADATA_PREFIX + indexName + ":" + chunkId);
                                    chunkIdsToRemove.add(chunkIdStr);
                                    deletedCount++;
                                }
                            } catch (Exception e) {
                                log.warn("解析元数据失败，chunkId: {}", chunkId, e);
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    log.warn("无效的chunkId: {}", chunkIdStr);
                }
            }

            // 批量删除
            if (!keysToDelete.isEmpty()) {
                RedisUtils.deleteObject(keysToDelete);

                // 从索引集合中移除
                chunkIds.removeAll(chunkIdsToRemove);
                RedisUtils.setCacheSet(indexKey, chunkIds);
            }

            log.info("成功删除文档的Redis向量数据，文档ID: {}, 删除数量: {}", documentId, deletedCount);
            return deletedCount;
        } catch (Exception e) {
            log.error("删除文档向量数据失败，文档ID: {}", documentId, e);
            return 0;
        }
    }

    /**
     * 计算余弦相似度
     */
    private double calculateCosineSimilarity(List<Float> vector1, List<Float> vector2) {
        if (vector1.size() != vector2.size()) {
            throw new IllegalArgumentException("向量维度不匹配: " + vector1.size() + " vs " + vector2.size());
        }

        double dotProduct = 0.0;
        double norm1 = 0.0;
        double norm2 = 0.0;

        for (int i = 0; i < vector1.size(); i++) {
            double v1 = vector1.get(i);
            double v2 = vector2.get(i);
            dotProduct += v1 * v2;
            norm1 += v1 * v1;
            norm2 += v2 * v2;
        }

        if (norm1 == 0.0 || norm2 == 0.0) {
            return 0.0;
        }

        return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2));
    }

    /**
     * 获取向量存储统计信息
     */
    public Map<String, Object> getVectorStats(String indexName) {
        try {
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);

            Map<String, Object> stats = new HashMap<>();
            stats.put("totalVectors", chunkIds != null ? chunkIds.size() : 0);
            stats.put("indexName", indexName);
            stats.put("vectorDimension", VECTOR_DIMENSION);
            stats.put("storageType", "Redis");

            return stats;
        } catch (Exception e) {
            log.error("获取向量统计信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 调试方法：检查Redis中的向量数据
     */
    public Map<String, Object> debugVectorData(String indexName) {
        try {
            Map<String, Object> debugInfo = new HashMap<>();

            // 检查索引
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);
            debugInfo.put("indexKey", indexKey);
            debugInfo.put("chunkIds", chunkIds);
            debugInfo.put("chunkCount", chunkIds != null ? chunkIds.size() : 0);

            // 检查前几个向量数据
            if (chunkIds != null && !chunkIds.isEmpty()) {
                List<Map<String, Object>> sampleData = new ArrayList<>();
                int count = 0;
                for (String chunkIdStr : chunkIds) {
                    if (count >= 3) break; // 只检查前3个

                    String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkIdStr;
                    Map<String, Object> vectorData = RedisUtils.getCacheMap(vectorKey);
                    if (vectorData != null) {
                        Map<String, Object> sample = new HashMap<>();
                        sample.put("chunkId", chunkIdStr);
                        sample.put("vectorKey", vectorKey);
                        sample.put("content", vectorData.get("content"));
                        sample.put("metadata", vectorData.get("metadata"));
                        sample.put("dimension", vectorData.get("dimension"));
                        sample.put("hasEmbedding", vectorData.containsKey("embedding"));
                        sampleData.add(sample);
                    }
                    count++;
                }
                debugInfo.put("sampleData", sampleData);
            }

            return debugInfo;
        } catch (Exception e) {
            log.error("调试向量数据失败", e);
            return Map.of("error", e.getMessage());
        }
    }



    /**
     * 清理旧的向量数据（使用租户前缀的key）
     */
    public int cleanupOldVectorData() {
        try {
            log.info("开始清理旧的向量数据");
            int cleanedCount = 0;

            // 清理旧的向量数据（使用租户前缀的key）
            Collection<String> oldVectorKeys = RedisUtils.keys("null:ai:vector:*");
            for (String key : oldVectorKeys) {
                RedisUtils.deleteObject(key);
                cleanedCount++;
            }

            // 清理旧的索引数据
            Collection<String> oldIndexKeys = RedisUtils.keys("null:ai:index:*");
            for (String key : oldIndexKeys) {
                RedisUtils.deleteObject(key);
                cleanedCount++;
            }

            // 清理旧的元数据
            Collection<String> oldMetadataKeys = RedisUtils.keys("null:ai:metadata:*");
            for (String key : oldMetadataKeys) {
                RedisUtils.deleteObject(key);
                cleanedCount++;
            }

            log.info("清理旧的向量数据完成，共清理 {} 个key", cleanedCount);
            return cleanedCount;
        } catch (Exception e) {
            log.error("清理旧的向量数据失败", e);
            return 0;
        }
    }

    public int cleanupExpiredVectors(String indexName) {
        try {
            String indexKey = INDEX_PREFIX + indexName;
            Set<String> chunkIds = RedisUtils.getCacheSet(indexKey);

            if (chunkIds == null || chunkIds.isEmpty()) {
                return 0;
            }

            int cleanedCount = 0;
            Set<String> validChunkIds = new HashSet<>();

            for (String chunkIdStr : chunkIds) {
                String vectorKey = VECTOR_PREFIX + indexName + ":" + chunkIdStr;
                if (RedisUtils.hasKey(vectorKey)) {
                    validChunkIds.add(chunkIdStr);
                } else {
                    cleanedCount++;
                }
            }

            // 更新索引集合，只保留有效的chunkId
            if (cleanedCount > 0) {
                RedisUtils.setCacheSet(indexKey, validChunkIds);
            }

            log.info("清理过期向量数据完成，清理数量: {}", cleanedCount);
            return cleanedCount;

        } catch (Exception e) {
            log.error("清理过期向量数据失败", e);
            return 0;
        }
    }
}

