<template>
    <div class="user-overview">
        <div class="module-title">用户总览</div>
        <div class="kpi-cards">
            <div class="kpi-card" v-for="(item, index) in kpiData" :key="index">
                <div class="card-content">
                    <div class="card-title">{{ item.title }}</div>

                    <!-- 咨询师配比特殊处理 -->
                    <div v-if="item.type === 'ratio'" class="ratio-chart">
                        <div class="ring-chart">
                            <div class="ring" :style="{ background: `conic-gradient(#1890FF ${item.value * 360}deg, rgba(24, 144, 255, 0.1) 0deg)` }"></div>
                            <div class="ring-center">
                                <span>{{ (item.value * 100).toFixed(0) }}%</span>
                            </div>
                        </div>
                        <div class="ratio-text">{{ item.actualValue }}/{{ item.standardValue }}</div>
                    </div>

                    <!-- 心理档案特殊处理 -->
                    <div v-else-if="item.type === 'progress'" class="progress-chart">
                        <div class="progress-bar">
                            <div class="progress" :style="{ width: `${item.value * 100}%` }"></div>
                        </div>
                        <div class="progress-text">{{ (item.value * 100).toFixed(0) }}%</div>
                    </div>

                    <!-- 普通数值显示 -->
                    <div v-else class="value-box">
                        <span class="value count-up">{{ item.value }}</span>
                        <span class="unit">{{ item.unit }}</span>
                    </div>

                    <div class="trend-info" v-if="hasTrend(item.trend)" >
            <span :class="['trend', getTrendClass(item.trend)]">
              {{ formatTrend(item.trend) }}
            </span>
                        <span class="trend-period">较上月</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import {getUserBaseStats} from "@/api/statistics/homePage";

// 工具函数 - 检查是否有趋势数据
const hasTrend = (trend: number | string | null | undefined): boolean => {
    return trend !== null && trend !== undefined && trend !== '';
};

// 工具函数 - 获取趋势样式类
const getTrendClass = (trend: number | string | null | undefined): string => {
    if (!hasTrend(trend)) return '';

    let trendValue: number;
    if (typeof trend === 'string') {
        trendValue = parseFloat(trend);
    } else if (typeof trend === 'number') {
        trendValue = trend;
    } else {
        return '';
    }

    return trendValue > 0 ? 'up' : 'down';
};

// 工具函数 - 格式化趋势显示
const formatTrend = (trend: number | string | null | undefined): string => {
    if (!hasTrend(trend)) return '';

    let trendValue: number;
    if (typeof trend === 'string') {
        trendValue = parseFloat(trend);
    } else if (typeof trend === 'number') {
        trendValue = trend;
    } else {
        return '';
    }

    return `${trendValue > 0 ? '+' : ''}${trendValue}%`;
};

// KPI数据
const kpiData = ref([
    {
        title: '用户总数',
        value: 0,
        unit: '人',
        trend: 0,
        type: 'number'
    },
    {
        title: '当月新增用户',
        value: 0,
        unit: '人',
        trend: 0,
        type: 'number'
    },
    {
        title: '咨询师配比',
        value: 0, // 实际配比/标准配比
        actualValue: 0,
        standardValue: 100,
        trend: 0,
        type: 'ratio'
    },
    {
        title: '心理档案',
        value: 0, // 完成率
        unit: '',
        trend: null,
        type: 'progress'
    },
    {
        title: '预警总数',
        value: 0,
        unit: '人',
        trend: 0,
        type: 'number'
    }
]);

// 获取首页统计数据
const fetchHomePageStats = async () => {
    try {
        const res = await getUserBaseStats();
        if (res.code === 200 && res.data) {
            // 更新用户总数
            kpiData.value[0].value = res.data.systemUserNum;
            kpiData.value[0].trend = res.data.systemUserNumGrowth;

            // 更新当月新增用户
            kpiData.value[1].value = res.data.newUsersNum;
            kpiData.value[1].trend = res.data.newUsersNumGrowth;

            // 更新咨询师配比
            if (res.data.counselorRatio !== null) {
                kpiData.value[2].standardValue = res.data.systemUserNum;
                kpiData.value[2].actualValue = res.data.consultantNum;
                kpiData.value[2].value = res.data.counselorRatio;
            }
            kpiData.value[2].trend = res.data.consultantNumGrowth;

            // 更新心理档案
            kpiData.value[3].value = res.data.psychologicalProfiles / 100;

            // 更新预警总数
            kpiData.value[4].value = res.data.warningsNum;
            kpiData.value[4].trend = res.data.warningsNumGrowth;
        }
    } catch (error) {
        console.error('获取首页统计数据失败:', error);
    }
};

// 自动刷新定时器
let updateInterval: number | null = null;

onMounted(() => {
    // 获取初始数据
    fetchHomePageStats();

    // 每60秒刷新一次数据
    updateInterval = window.setInterval(fetchHomePageStats, 60000);
});

onBeforeUnmount(() => {
    if (updateInterval) {
        clearInterval(updateInterval);
    }
});
</script>

<style scoped>
.user-overview {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 8px;
  box-sizing: border-box;
}

.module-title {
  font-size: 14px;
  color: #4ECDC4;
  margin-bottom: 5px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
}

.module-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #4ECDC4, rgba(78, 205, 196, 0.3));
  border-radius: 2px;
}

/* 优化KPI卡片布局，采用两行布局 */
.kpi-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 5px;
  flex: 1;
  overflow: hidden;
}

.kpi-card {
  background: rgba(16, 35, 75, 0.3);
  border-radius: 6px;
  transition: all 0.3s;
  cursor: pointer;
  overflow: hidden;
  position: relative;
  backdrop-filter: blur(3px);
}

.kpi-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.kpi-card:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 1px solid rgba(78, 205, 196, 0.5);
  border-radius: 6px;
  box-sizing: border-box;
  pointer-events: none;
  animation: borderGlow 1.5s infinite alternate;
}

@keyframes borderGlow {
  0% { box-shadow: 0 0 5px rgba(78, 205, 196, 0.3); }
  100% { box-shadow: 0 0 10px rgba(78, 205, 196, 0.7); }
}

.card-content {
  padding: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 5px;
}

.value {
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  line-height: 1;
}

.unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.65);
  margin-left: 4px;
}

.trend-info {
  margin-top: auto;
  display: flex;
  align-items: center;
}

.trend {
  font-size: 12px;
  font-weight: bold;
}

.trend.up {
  color: #52c41a;
}

.trend.down {
  color: #f5222d;
}

.trend-period {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.45);
  margin-left: 4px;
}

/* 咨询师配比样式 */
.ratio-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 10px;
}

.ring-chart {
  position: relative;
  width: 40px;
  height: 40px;
  margin-bottom: 4px;
}

.ring {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.ring-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 75%;
  height: 75%;
  background: rgba(16, 35, 75, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #fff;
  font-weight: bold;
}

.ratio-text {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.75);
}

/* 心理档案进度条样式 */
.progress-chart {
  margin-bottom: 10px;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 6px;
}

.progress {
  height: 100%;
  background: linear-gradient(to right, #52c41a, #87d068);
  border-radius: 4px;
  transition: width 0.5s;
}

.progress-text {
  text-align: right;
  font-size: 14px;
  color: #52c41a;
  font-weight: bold;
}

/* 用于数字滚动效果的动画 */
.count-up {
  transition: all 0.5s;
}

.kpi-container {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr;
  grid-auto-rows: 1fr;
  gap: 5px;
  overflow: hidden;
}

@media screen and (max-width: 1440px) {
  .kpi-cards {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(2, 1fr);
    gap: 5px;
  }

  .kpi-card:nth-child(4), .kpi-card:nth-child(5) {
    grid-column: span 1.5;
  }
}
</style>
