package org.senyor.ai.exception;

import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.domain.R;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * AI模块全局异常处理器
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice(basePackages = "org.senyor.ai")
public class AiExceptionHandler {

    /**
     * 处理向量化相关异常
     */
    @ExceptionHandler(VectorizationException.class)
    public R<Void> handleVectorizationException(VectorizationException e) {
        log.error("向量化异常: {}", e.getMessage(), e);
        return R.fail("向量化处理失败: " + e.getMessage());
    }

    /**
     * 处理知识库相关异常
     */
    @ExceptionHandler(KnowledgeBaseException.class)
    public R<Void> handleKnowledgeBaseException(KnowledgeBaseException e) {
        log.error("知识库异常: {}", e.getMessage(), e);
        return R.fail("知识库操作失败: " + e.getMessage());
    }

    /**
     * 处理AI聊天相关异常
     */
    @ExceptionHandler(AiChatException.class)
    public R<Void> handleAiChatException(AiChatException e) {
        log.error("AI聊天异常: {}", e.getMessage(), e);
        return R.fail("AI聊天失败: " + e.getMessage());
    }

    /**
     * 处理通用AI异常
     */
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("AI模块异常: {}", e.getMessage(), e);
        return R.fail("系统异常，请稍后重试");
    }
} 