package org.senyor.ai.service;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.senyor.ai.service.impl.RedisVectorStoreServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Redis向量存储服务测试
 *
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class RedisVectorStoreServiceTest {

    @Autowired
    private RedisVectorStoreServiceImpl vectorStoreService;

    @Test
    public void testVectorStorage() {
        String indexName = "test_index";
        Long chunkId = 1L;
        String content = "这是一个测试文档内容";
        List<Float> embedding = generateTestEmbedding(1536);
        String metadata = "{\"documentId\":1,\"knowledgeId\":1,\"documentName\":\"test.pdf\"}";

        // 测试存储向量
        boolean stored = vectorStoreService.storeVector(indexName, chunkId, content, embedding, metadata);
        assertTrue(stored, "向量存储应该成功");

        // 测试检索向量
        List<KnowledgeChunk> results = vectorStoreService.searchSimilar(indexName, embedding, 5, 0.5);
        assertFalse(results.isEmpty(), "应该能找到相似的向量");
        assertEquals(chunkId, results.get(0).getChunkId(), "应该找到正确的chunkId");

        // 测试删除向量
        boolean deleted = vectorStoreService.deleteVector(indexName, chunkId);
        assertTrue(deleted, "向量删除应该成功");

        // 验证删除后无法检索到
        List<KnowledgeChunk> emptyResults = vectorStoreService.searchSimilar(indexName, embedding, 5, 0.5);
        assertTrue(emptyResults.isEmpty(), "删除后应该无法检索到向量");
    }

    @Test
    public void testBatchVectorStorage() {
        String indexName = "test_batch_index";
        List<VectorData> vectors = new ArrayList<>();

        // 创建测试向量数据
        for (int i = 1; i <= 10; i++) {
            VectorData vector = new VectorData();
            vector.setChunkId((long) i);
            vector.setContent("测试文档内容 " + i);
            vector.setEmbedding(generateTestEmbedding(1536));
            vector.setMetadata("{\"documentId\":" + i + ",\"knowledgeId\":1,\"documentName\":\"test" + i + ".pdf\"}");
            vectors.add(vector);
        }

        // 测试批量存储
        int storedCount = vectorStoreService.batchStoreVectors(indexName, vectors);
        assertEquals(10, storedCount, "应该成功存储10个向量");

        // 测试批量检索
        List<KnowledgeChunk> results = vectorStoreService.searchSimilar(indexName, vectors.get(0).getEmbedding(), 5, 0.5);
        assertFalse(results.isEmpty(), "应该能找到相似的向量");

        // 清理测试数据
        vectorStoreService.deleteVectorsByKnowledgeId(indexName, 1L);
    }

    @Test
    public void testVectorStats() {
        String indexName = "test_stats_index";
        
        // 获取统计信息
        Map<String, Object> stats = vectorStoreService.getVectorStats(indexName);
        assertNotNull(stats, "统计信息不应该为空");
        assertEquals("Redis", stats.get("storageType"), "存储类型应该是Redis");
        assertEquals(1536, stats.get("vectorDimension"), "向量维度应该是1536");
    }

    /**
     * 生成测试向量
     */
    private List<Float> generateTestEmbedding(int dimension) {
        List<Float> embedding = new ArrayList<>();
        Random random = new Random();
        for (int i = 0; i < dimension; i++) {
            embedding.add(random.nextFloat() * 2 - 1); // 生成-1到1之间的随机数
        }
        return embedding;
    }
} 