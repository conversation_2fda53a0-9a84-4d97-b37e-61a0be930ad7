package org.senyor.ai.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.service.impl.RedisVectorStoreServiceImpl;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 向量清理定时任务
 * 定期清理过期的向量数据
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class VectorCleanupTask {

    private final RedisVectorStoreServiceImpl redisVectorStoreService;

    /**
     * 每天凌晨2点执行向量清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredVectors() {
        try {
            log.info("开始执行向量清理定时任务");
            String indexName = "ai_knowledge_vectors";
            int cleanedCount = redisVectorStoreService.cleanupExpiredVectors(indexName);
            log.info("向量清理定时任务完成，清理数量: {}", cleanedCount);
        } catch (Exception e) {
            log.error("向量清理定时任务执行失败", e);
        }
    }

    /**
     * 每小时检查一次向量统计信息
     */
    @Scheduled(cron = "0 0 * * * ?")
    public void checkVectorStats() {
        try {
            String indexName = "ai_knowledge_vectors";
            var stats = redisVectorStoreService.getVectorStats(indexName);
            log.info("向量存储统计信息: {}", stats);
        } catch (Exception e) {
            log.error("获取向量统计信息失败", e);
        }
    }
} 