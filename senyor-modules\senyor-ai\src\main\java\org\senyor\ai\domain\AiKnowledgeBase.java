package org.senyor.ai.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * AI知识库表 ai_knowledge_base
 *
 * <AUTHOR>
 */
@Data
@TableName("ai_knowledge_base")
public class AiKnowledgeBase {

    /**
     * 知识库ID
     */
    @TableId(value = "knowledge_id", type = IdType.AUTO)
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    private String knowledgeName;

    /**
     * 知识库描述
     */
    private String knowledgeDesc;

    /**
     * 知识库类型（document:文档库, qa:问答库, custom:自定义）
     */
    private String knowledgeType;

    /**
     * 知识库状态（0:启用 1:禁用）
     */
    private String status;

    /**
     * 文档数量
     */
    private Integer documentCount;

    /**
     * 向量维度
     */
    private Integer vectorDimension;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名
     */
    private String userName;


    /**
     * 备注
     */
    private String remark;

    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
