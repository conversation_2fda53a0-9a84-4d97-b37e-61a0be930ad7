import request from '@/utils/request';
import { AxiosPromise } from 'axios';
import { TemplateVO, TemplateForm, TemplateQuery } from '@/api/app/template/types';
import {UserForm} from "@/api/system/user/types";

/**
 * 查询模板列表
 * @param query
 * @returns {*}
 */

export const listTemplate = (query?: TemplateQuery): AxiosPromise<TemplateVO[]> => {
    return request({
        url: '/app/template/list',
        method: 'get',
        params: query
    });
};

/**
 * 查询模板详细
 * @param id
 */
export const getTemplate = (id: string | number): AxiosPromise<TemplateVO> => {
    return request({
        url: '/app/template/' + id,
        method: 'get'
    });
};
/**
 * 查询角色所属模板
 * @param roleId
 */
export const getTemplateByRoleId = (roleId: string | number): AxiosPromise<TemplateVO> => {
    return request({
        url: '/app/template/getTemplateByRoleId/' + roleId,
        method: 'get'
    });
};

/**
 * 新增模板
 * @param data
 */
export const addTemplate = (data: TemplateForm) => {
    return request({
        url: '/app/template',
        method: 'post',
        data: data
    });
};

/**
 * 修改模板
 * @param data
 */
export const updateTemplate = (data: TemplateForm) => {
    return request({
        url: '/app/template',
        method: 'put',
        data: data
    });
};

/**
 * 删除模板
 * @param id
 */
export const delTemplate = (id: string | number | Array<string | number>) => {
    return request({
        url: '/app/template/' + id,
        method: 'delete'
    });
};
/**
 * 根据用户和模板id查询包含字段
 * */
export const getTemplateFieldData = (userId: string | number,templateId: string | number): AxiosPromise<TemplateVO> => {
    return request({
        url: '/app/template/getTemplateFieldData/' + userId+'/'+templateId,
        method: 'get'
    });
};
/**
 * 根据用户id查询包含字段
 * */
export const getTemplateFieldDataByUserId = (userId: string | number): AxiosPromise<TemplateVO> => {
    return request({
        url: '/app/template/getTemplateFieldDataByUserId/' + userId,
        method: 'get'
    });
};
/**
 * 根据用户id查询包含字段
 * */
export const getAllFieldData = (userId: string | number): AxiosPromise<TemplateVO> => {
    return request({
        url: '/app/template/getAllFieldData/' + userId,
        method: 'get'
    });
};
/**
 * 一键完善 所有使用该档案的用户
 * */
export const improve = (templateId: string | number) => {
    return request({
        url: '/app/template/improve/'+templateId,
        method: 'get'
    });
};
/**
 * 一键完善 用户档案 单对单
 * */
export const improveByUserId = (userId: string | number) => {
    return request({
        url: '/app/template/improveByUserId/'+userId,
        method: 'get'
    });
};
/**
 * API - 根据用户id查询包含字段
 * */
export const getAllFieldDataByApi = (): AxiosPromise<TemplateVO> => {
    return request({
        url: '/api/templateField/getAllFieldData',
        method: 'get'
    });
};
/**
 * API - 修改用户-原始&拓展字段
 * */
export const updateUserByFieldByApi = (data: UserForm) => {
    return request({
        url: '/api/templateField/editFieldData',
        method: 'put',
        data: data
    });
};
