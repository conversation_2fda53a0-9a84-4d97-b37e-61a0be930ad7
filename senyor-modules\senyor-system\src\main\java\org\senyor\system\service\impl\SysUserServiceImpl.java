package org.senyor.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.constant.CacheNames;
import org.senyor.common.core.constant.UserConstants;
import org.senyor.common.core.domain.dto.UserDTO;
import org.senyor.common.core.domain.model.LoginUser;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.core.service.UserService;
import org.senyor.common.core.utils.*;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.mybatis.helper.DataBaseHelper;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.system.domain.*;
import org.senyor.system.domain.bo.BatchTransferBo;
import org.senyor.system.domain.bo.SysUserBo;
import org.senyor.system.domain.vo.*;
import org.senyor.system.mapper.*;
import org.senyor.system.service.ISysDeptService;
import org.senyor.system.service.ISysUserService;
import org.senyor.system.sysEnum.DictDataEnum;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户 业务层处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService, UserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;
    private final ISysDeptService deptService;

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery, String roleKey) {
        Wrapper<SysUser> wrapper = this.buildQueryWrapper(user,roleKey);

        Page<SysUserVo> page = baseMapper.selectPageUserListByRoleKey(pageQuery.build(), wrapper);
        calculateAndSetAge(page.getRecords());

        return TableDataInfo.build(page);
    }


    @Override
    public List<SysUserVo> selectUserListNoDataScope(SysUserBo user) {
        LambdaQueryWrapper<SysUser> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(user.getUserId() != null, SysUser::getUserId, user.getUserId());
        wrapper.in(CollUtil.isNotEmpty(user.getUserIds()), SysUser::getUserId, user.getUserIds());
        return baseMapper.selectVoList(wrapper);
    }


    /**
     * 获取游客人员信息
     */
    @Override
    public TableDataInfo<SysUserVo> getVisitorList(SysUserBo user, PageQuery pageQuery) {
        List<Long> userIds = getVisitorIds();

        // 构建查询条件并添加用户ID筛选
        Wrapper<SysUser> wrapper = this.buildQueryWrapper(user);
        if (!userIds.isEmpty()) {
            ((QueryWrapper<SysUser>) wrapper).in("u.user_id", userIds);
        }

        // 执行查询并计算年龄
        Page<SysUserVo> page = baseMapper.selectPageUserList(pageQuery.build(), wrapper);
        calculateAndSetAge(page.getRecords());
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<SysUserVo> getUserProfiles(SysUserBo user, PageQuery pageQuery) {
        List<Long> visitorIds = null;//getVisitorIds();
        Wrapper<SysUser> sysUserWrapper = this.buildQueryWrapper(user);
        ((QueryWrapper<SysUser>) sysUserWrapper).notIn(visitorIds != null && !visitorIds.isEmpty(), "u.user_id", visitorIds);

        Page<SysUserVo> page = baseMapper.selectPageUserList(pageQuery.build(), sysUserWrapper);
        calculateAndSetAge(page.getRecords());
        //TODO:递归操作 响应较慢 后续优化
        /*for (SysUserVo sysUserVo : page.getRecords()) {
            List<SysUserRole> sysUserRoles = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, sysUserVo.getUserId()));
            if (sysUserRoles != null && !sysUserRoles.isEmpty()) {
                SysUserRole sysUserRole = sysUserRoles.getFirst();
                String roleName = roleMapper.selectById(sysUserRole.getRoleId()).getRoleName();
                sysUserVo.setUserType(roleName);
            }
            String deptTree = concatDeptName(sysUserVo.getDeptId());
            sysUserVo.setDeptNameAll(deptTree);
        }*/
        return TableDataInfo.build(page);
    }

    /**
     * 得到游客角色ids Tool
     */
    public List<Long> getVisitorIds() {
        // 获取游客角色的角色ID
        LambdaQueryWrapper<SysRole> sysRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysRoleLambdaQueryWrapper.eq(SysRole::getDelFlag, 0).eq(SysRole::getRoleName, "游客角色");
        SysRole sysRole = roleMapper.selectOne(sysRoleLambdaQueryWrapper);
        if (sysRole == null) {
            return null;
        }

        // 获取所有具有游客角色的用户ID
        LambdaQueryWrapper<SysUserRole> sysUserRoleLambdaQueryWrapper = new LambdaQueryWrapper<>();
        sysUserRoleLambdaQueryWrapper.eq(sysRole != null, SysUserRole::getRoleId, sysRole.getRoleId());
        List<Long> userIds = userRoleMapper.selectList(sysUserRoleLambdaQueryWrapper).stream().map(SysUserRole::getUserId).collect(Collectors.toList());
        return userIds;
    }

    /**
     * 计算并设置年龄
     */
    private void calculateAndSetAge(List<SysUserVo> records) {
        if (!CollectionUtils.isEmpty(records)) {
            for (SysUserVo sysUserVo : records) {
                sysUserVo.setAge(DateUtils.calculateAge(sysUserVo.getBirthDate()));
            }
        }
    }


    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserExportVo> selectUserExportList(SysUserBo user) {
        return baseMapper.selectUserExportList(this.buildQueryWrapper(user));
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUserBo user) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)

                .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId()).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus()).eq(StringUtils.isNotBlank(user.getSex()), "u.sex", user.getSex()).like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber()).between(params.get("beginTime") != null && params.get("endTime") != null, "u.create_time", params.get("beginTime"), params.get("endTime")).and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                    List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                    List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                    ids.add(user.getDeptId());
                    w.in("u.dept_id", ids);
                }).orderByAsc("u.user_id");
        if (StringUtils.isNotBlank(user.getExcludeUserIds())) {
            wrapper.notIn("u.user_id", StringUtils.splitList(user.getExcludeUserIds()));
        }
        return wrapper;
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUserBo user,String roleKey) {
        Map<String, Object> params = user.getParams();
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL)

                .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId()).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus()).eq(StringUtils.isNotBlank(user.getSex()), "u.sex", user.getSex()).like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber()).between(params.get("beginTime") != null && params.get("endTime") != null, "u.create_time", params.get("beginTime"), params.get("endTime")).and(ObjectUtil.isNotNull(user.getDeptId()), w -> {
                    List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).apply(DataBaseHelper.findInSet(user.getDeptId(), "ancestors")));
                    List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                    ids.add(user.getDeptId());
                    w.in("u.dept_id", ids);
                }).orderByAsc("u.user_id");
        if (StringUtils.isNotBlank(user.getExcludeUserIds())) {
            wrapper.notIn("u.user_id", StringUtils.splitList(user.getExcludeUserIds()));
        }
        wrapper.eq(StringUtils.isNotBlank(roleKey), "r.role_key", roleKey);
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectAllocatedList(SysUserBo user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId()).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus()).like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber()).orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectAllocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectUnallocatedList(SysUserBo user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", UserConstants.USER_NORMAL).and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id")).notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds).like(StringUtils.isNotBlank(user.getUserName()), "u.user_name", user.getUserName()).like(StringUtils.isNotBlank(user.getPhonenumber()), "u.phonenumber", user.getPhonenumber()).orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectUnallocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByUserName(String userName) {
        return baseMapper.checkUserNameUnique(userName);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhonenumber, phonenumber));
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    //@InterceptorIgnore(tenantLine = "1")
    @Override
    public SysUserVo selectUserById(Long userId) {
        SysUserVo user = baseMapper.selectVoById(userId);
        if (ObjectUtil.isNull(user)) {
            return user;
        }
        user.setRoles(roleMapper.selectRolesByUserId(user.getUserId()));
        return user;
    }

    /**
     * 通过用户ID串查询用户
     *
     * @param userIds 用户ID串
     * @param deptId  部门id
     * @return 用户列表信息
     */
    @Override
    public List<SysUserVo> selectUserByIds(List<Long> userIds, Long deptId) {
        return baseMapper.selectUserList(new LambdaQueryWrapper<SysUser>().select(SysUser::getUserId, SysUser::getUserName, SysUser::getNickName).eq(SysUser::getStatus, UserConstants.USER_NORMAL).eq(ObjectUtil.isNotNull(deptId), SysUser::getDeptId, deptId).in(CollUtil.isNotEmpty(userIds), SysUser::getUserId, userIds));
    }

    /**
     * 查询用户所属角色组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(Long userId) {
        List<SysRoleVo> list = roleMapper.selectRolesByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(Long userId) {
        List<SysPostVo> list = postMapper.selectPostsByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPostVo::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUserBo user) {
        /*SysUserVo sysUserVo = baseMapper.checkUserNameUnique(user.getUserName());
        log.info("sysUserVo:{},user:{}", JsonUtils.toJsonString(sysUserVo),JsonUtils.toJsonString(user));
        if (sysUserVo == null) {
            return true;
        }
        return sysUserVo.getUserId().equals(user.getUserId());*/
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, user.getUserName()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhonenumber, user.getPhonenumber()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>().eq(SysUser::getEmail, user.getEmail()).ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户ID
     */
    @Override
    public void checkUserAllowed(Long userId) {
        if (ObjectUtil.isNotNull(userId) && LoginHelper.isSuperAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        if (baseMapper.countUserById(userId) == 0) {
            throw new ServiceException("没有权限访问用户数据！");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUserBo user) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 新增用户信息
        int rows = baseMapper.insert(sysUser);
        user.setUserId(sysUser.getUserId());
        // 新增用户岗位关联
        insertUserPost(user, false);
        // 新增用户与角色管理
        insertUserRole(user, false);
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUserBo user, String tenantId) {
        user.setCreateBy(0L);
        user.setUpdateBy(0L);
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        sysUser.setTenantId(tenantId);
        int insert = baseMapper.insert(sysUser);
        // 新增用户与角色管理
        insertUserRole(sysUser.getUserId(), user.getRoleIds(), false, false);
        return insert > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUserBo user) {
        // 新增用户与角色管理
        insertUserRole(user, true);
        // 新增用户与岗位管理
        insertUserPost(user, true);
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        // 防止错误更新后导致的数据误删除
        int flag = baseMapper.updateById(sysUser);
        if (flag < 1) {
            throw new ServiceException("修改用户" + user.getUserName() + "信息失败");
        }
        return flag;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        insertUserRole(userId, roleIds, true);
    }

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 帐号状态
     * @return 结果
     */
    @Override
    public int updateUserStatus(Long userId, String status) {
        return baseMapper.update(null, new LambdaUpdateWrapper<SysUser>().set(SysUser::getStatus, status).eq(SysUser::getUserId, userId));
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public int updateUserProfile(SysUserBo user) {
        LambdaUpdateWrapper<SysUser> eq = new LambdaUpdateWrapper<SysUser>()
                .set(StringUtils.isNotBlank(user.getNickName()), SysUser::getNickName, user.getNickName())
                .set(StringUtils.isNotBlank(user.getPhonenumber()), SysUser::getPhonenumber, user.getPhonenumber())
                .set(StringUtils.isNotBlank(user.getEmail()),SysUser::getEmail, user.getEmail())
                .set(StringUtils.isNotBlank(user.getSex()),SysUser::getSex, user.getSex())
                .set(user.getBirthDate() != null,SysUser::getBirthDate, user.getBirthDate())
                .eq(SysUser::getUserId, user.getUserId());
        return baseMapper.update(null, eq);
    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(Long userId, Long avatar) {
        return baseMapper.update(null,
                new LambdaUpdateWrapper<SysUser>()
                        .set(SysUser::getAvatar, avatar)
                        .eq(SysUser::getUserId, userId)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(Long userId, String password) {
        return baseMapper.update(null, new LambdaUpdateWrapper<SysUser>().set(SysUser::getPassword, password).eq(SysUser::getUserId, userId));
    }

    /**
     * 新增用户角色信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserRole(SysUserBo user, boolean clear) {
        this.insertUserRole(user.getUserId(), user.getRoleIds(), clear);
    }

    /**
     * 新增用户岗位信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserPost(SysUserBo user, boolean clear) {
        Long[] posts = user.getPostIds();
        if (ArrayUtil.isNotEmpty(posts)) {
            if (clear) {
                // 删除用户与岗位关联
                userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, user.getUserId()));
            }
            // 新增用户与岗位管理
            List<SysUserPost> list = StreamUtils.toList(List.of(posts), postId -> {
                SysUserPost up = new SysUserPost();
                up.setUserId(user.getUserId());
                up.setPostId(postId);
                return up;
            });
            userPostMapper.insertBatch(list);
        }
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     * @param clear   清除已存在的关联数据
     */
    private void insertUserRole(Long userId, Long[] roleIds, boolean clear) {
        this.insertUserRole(userId, roleIds, clear, true);
    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     * @param clear   清除已存在的关联数据
     */
    private void insertUserRole(Long userId, Long[] roleIds, boolean clear, boolean checkPermission) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            List<Long> canDoRoleList = null;
            if (checkPermission) {
                // 判断是否具有此角色的操作权限
                List<SysRoleVo> roles = roleMapper.selectRoleList(new LambdaQueryWrapper<>());
                if (CollUtil.isEmpty(roles)) {
                    throw new ServiceException("没有权限访问角色的数据");
                }
                List<Long> roleList = StreamUtils.toList(roles, SysRoleVo::getRoleId);
                if (!LoginHelper.isSuperAdmin(userId)) {
                    roleList.remove(UserConstants.SUPER_ADMIN_ID);
                }
                canDoRoleList = StreamUtils.filter(List.of(roleIds), roleList::contains);
                if (CollUtil.isEmpty(canDoRoleList)) {
                    throw new ServiceException("没有权限访问角色的数据");
                }
            }
            if (clear) {
                // 删除用户与角色关联
                userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
            }
            // 新增用户与角色管理
            List<SysUserRole> list = StreamUtils.toList(checkPermission ? canDoRoleList : List.of(roleIds), roleId -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                return ur;
            });
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteById(userId);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId);
        }
        List<Long> ids = List.of(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteBatchIds(ids);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 通过部门id查询当前部门所有用户
     *
     * @param deptId 部门ID
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserListByDept(Long deptId) {
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUser::getDeptId, deptId);
        lqw.orderByAsc(SysUser::getUserId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 根据部门ID和类型选择用户列表
     *
     * @param deptId 部门ID，用于筛选用户所属的部门
     * @param type   类型，决定查询范围：0表示仅查询指定部门，1和2表示查询指定部门及其子部门
     * @return 返回一个SysUserVo对象的列表，表示查询到的用户列表
     * @throws IllegalArgumentException 如果类型值不在预期范围内（0到2），抛出此异常
     */
    @Override
    public List<SysUserVo> selectUserListByDeptId(Long deptId, int type) {
        // 校验type参数的有效性，确保其值在预期范围内
        if (type < 0 || type > 2) {
            throw new IllegalArgumentException("Invalid type: " + type);
        }

        List<SysUserVo> sysUsers = null;

        try {
            // 根据type值的不同，执行不同的查询逻辑
            if (type == 0) {
                // 当type为0时，仅查询指定部门的用户
                sysUsers = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>().eq(SysUser::getDeptId, deptId).eq(SysUser::getDelFlag, DictDataEnum.DENY_DIGIT.getValue()).orderByAsc(SysUser::getUserId));
            } else {
                // 当type为1或2时，查询指定部门及其子部门的用户
                List<SysDeptVo> subDepts = getSubDepts(deptId, type);
                if (!CollectionUtils.isEmpty(subDepts)) {
                    // 获取所有子部门的ID
                    Set<Long> deptIdList = subDepts.stream().map(SysDeptVo::getDeptId).collect(Collectors.toSet());
                    // 查询属于子部门的用户
                    sysUsers = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, deptIdList).eq(SysUser::getDelFlag, DictDataEnum.DENY_DIGIT.getValue()).orderByAsc(SysUser::getUserId));
                }
            }
        } catch (Exception e) {
            // 记录异常日志
            log.error(" 按deptId选择用户列表时出错: {}", deptId, e);
            return List.of(); // 返回空列表作为默认值
        }

        // 如果没有查询到任何用户，返回一个空列表
        if (sysUsers == null) {
            sysUsers = List.of();
        }
        return sysUsers;
    }

    /**
     * 根据类型获取子部门列表
     * 此方法根据传入的部门ID和类型，返回相应的子部门列表
     * 如果类型为1，将迭代地获取所有子部门；如果类型为2，将包括根部门在内获取所有部门
     * 其他类型将返回空列表
     *
     * @param deptId 部门ID，用于获取子部门列表
     * @param type   类型代码，决定获取子部门列表的方式
     * @return 根据类型返回相应的子部门列表，如果无对应类型则返回空列表
     */
    private List<SysDeptVo> getSubDepts(Long deptId, int type) {
        // 根据类型迭代地获取所有子部门
        if (type == 1) {
            return Optional.ofNullable(deptService.getAllSubDeptsIteratively(deptId)).orElse(List.of());
        } else if (type == 2) {
            // 包括根部门在内获取所有部门
            return Optional.ofNullable(deptService.getAllDeptsIncludingRoot(deptId)).orElse(List.of());
        }
        // 对于未知类型，返回空列表
        return List.of();
    }


    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getUserName();
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Override
    @Cacheable(cacheNames = CacheNames.SYS_NICKNAME, key = "#userId")
    public String selectNicknameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getNickName).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getNickName();
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userIds 用户ID 多个用逗号隔开
     * @return 用户账户
     */
    @Override
    public String selectNicknameByIds(String userIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(userIds, Convert::toLong)) {
            String nickname = SpringUtils.getAopProxy(this).selectNicknameById(id);
            if (StringUtils.isNotBlank(nickname)) {
                list.add(nickname);
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    /**
     * 通过用户ID查询用户手机号
     *
     * @param userId 用户id
     * @return 用户手机号
     */
    @Override
    public String selectPhonenumberById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getPhonenumber).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getPhonenumber();
    }

    /**
     * 通过用户ID查询用户邮箱
     *
     * @param userId 用户id
     * @return 用户邮箱
     */
    @Override
    public String selectEmailById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>().select(SysUser::getEmail).eq(SysUser::getUserId, userId));
        return ObjectUtil.isNull(sysUser) ? null : sysUser.getEmail();
    }

    @Override
    public List<UserDTO> selectListByIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return List.of();
        }
        List<SysUserVo> list = this.selectUserByIds(userIds, null);
        return BeanUtil.copyToList(list, UserDTO.class);
    }

    @Override
    public List<Long> selectUserIdsByRoleIds(List<Long> roleIds) {
        List<SysUserRole> userRoles = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getRoleId, roleIds));
        return StreamUtils.toList(userRoles, SysUserRole::getUserId);
    }

    /**
     * 递归 拼接组织结构
     */
    public String concatDeptName(Long deptId) {
        SysDept currentDept = deptMapper.selectById(deptId);
        if (currentDept == null) {
            return "";
        }
        if (currentDept.getParentId() == 0) {
            return currentDept.getDeptName();
        }
        String parentDeptName = concatDeptName(currentDept.getParentId());
        return parentDeptName + "/" + currentDept.getDeptName(); // 拼接父部门名称和当前部门名称
    }


    /**
     * 通过部门id查询当前部门所有用户
     *
     * @param roleKey 角色键
     * @param type    0查询入参传输的角色，1查询入参以外的角色
     * @return 用户信息集合
     */
    @Override
    public List<SysUserVo> selectLoginUserListByDeptAndRole(String roleKey, int type) {
        List<String> roleKeyList = new ArrayList<>();
        // 校验输入参数是否为空
        if (StringUtils.isBlank(roleKey)) {
            return Collections.emptyList();
        } else {
            String[] split = roleKey.split(StringUtils.SEPARATOR);
            roleKeyList = Arrays.asList(split);
        }

        try {
            // 查询角色信息
            List<SysRole> sysRoles = null;
            if (type == 0) {
                sysRoles = roleMapper.selectList(new LambdaQueryWrapper<SysRole>().in(SysRole::getRoleKey, roleKeyList).eq(SysRole::getDelFlag, DictDataEnum.DENY_DIGIT.getValue()));
            } else {
                sysRoles = roleMapper.selectList(new LambdaQueryWrapper<SysRole>().notIn(SysRole::getRoleKey, roleKeyList).eq(SysRole::getDelFlag, DictDataEnum.DENY_DIGIT.getValue()));
            }

            if (CollectionUtils.isEmpty(sysRoles)) {
                return Collections.emptyList();
            }
            List<Long> roleIdList = sysRoles.stream().map(sysRole -> sysRole.getRoleId()).collect(Collectors.toList());
            // 查询用户ID列表
            List<Long> userIdLists = selectUserIdsByRoleIds(roleIdList);
            if (CollectionUtils.isEmpty(userIdLists)) {
                return Collections.emptyList();
            }

            // 构建查询条件
            LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();

            if (LoginHelper.isLogin()) {
                // 获取当前登录用户
                LoginUser loginUser = LoginHelper.getLoginUser();
                if (loginUser != null && loginUser.getDeptId() != null) {

                    lqw.eq(SysUser::getDeptId, loginUser.getDeptId());
                }
            }
            lqw.in(SysUser::getUserId, userIdLists);
            lqw.orderByAsc(SysUser::getUserId);

            // 执行查询并返回结果
            return baseMapper.selectVoList(lqw);

        } catch (Exception e) {
            // 捕获异常并记录日志
            log.error("查询用户信息失败，roleKey: {}", roleKey, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取部门以及子部门下的所有用户信息
     *
     * @param deptId 组织id
     * @return 用户信息集合
     */
    @Override
    public List<SysUserVo> selectUserListByDeptAndChild(Long deptId) {
        List<SysUserVo> result = null;
        if (deptId != null) {
            // 获取当前部门及其所有子部门，包括根部门
            List<SysDeptVo> allDeptsIncludingRoot = deptService.getAllDeptsIncludingRoot(deptId);
            if (!CollectionUtils.isEmpty(allDeptsIncludingRoot)) {
                // 提取部门ID，用于查询条件
                Set<Long> deptIds = allDeptsIncludingRoot.stream().map(SysDeptVo::getDeptId).collect(Collectors.toSet());
                result = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>().in(SysUser::getDeptId, deptIds).eq(SysUser::getDelFlag, DictDataEnum.DENY_DIGIT.getValue()));
            }
        }
        return result;
    }

    /**
     * 修改用户属性
     * */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateUserByField(SysUserBo bo) {
        Long userId = LoginHelper.getUserId();
        bo.setUserId(userId);

//        Long planId = bo.getPlanId();
//        Map<String, String> planInfo = baseMapper.getPlanInfo(planId, LoginHelper.getTenantId());
//        String selfOther = planInfo.get("selfOther");
//        String selfUserIds = planInfo.get("selfUserIds");
//        String[] userIdLists = selfUserIds.split(",");
//        if("T".equals(selfOther)) {
//            if(StringUtils.isEmpty(selfUserIds)) {
//                throw new ServiceException("他评对象不存在");
//            }
//
//            bo.setUserId(Long.valueOf(userIdLists[0]));
//        }

        List<SysFieldVo> fieldList = bo.getFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return true; // 没有字段需要更新
        }
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, String> extensionFields = new HashMap<>();
        for (SysFieldVo field : fieldList) {
            if ("0".equals(field.getStatus())) {//原始字段
                fieldMap.put(field.getFieldFlag(), field.getRealValue());
            } else if ("1".equals(field.getStatus())) {//扩展字段
                extensionFields.put(field.getFieldFlag(), field.getRealValue());
            }
        }
        String jsonStr = JsonUtils.toJsonString(extensionFields);
        // 动态更新
        int rows = baseMapper.updateUserFields(bo.getUserId(), fieldMap, jsonStr);
        return rows > 0;
    }

    /**
     * 批量转移用户部门
     *
     * @param bo 批量转移参数
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int batchTransferDept(BatchTransferBo bo) {
        int rows = 0;
        for (Long userId : bo.getUserIds()) {
            SysUser user = new SysUser();
            user.setUserId(userId);
            user.setDeptId(bo.getDeptId());
            rows += baseMapper.updateById(user);
        }
        return rows;
    }

}
