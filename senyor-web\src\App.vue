<template>
    <el-config-provider :locale="appStore.locale" :size="appStore.size">
        <router-view />
        <div v-if="showAiHelper">
            <!-- 悬浮聊天按钮 -->
            <div
                class="ai-float-button float-animate"
                ref="floatButtonRef"
                :class="{ dragging: isDraggingFloatButton }"
                :style="{ left: floatButtonPosition.x + 'px', top: floatButtonPosition.y + 'px' }"
                @mousedown="startDragFloatButton"
                v-show="!showChatWindow"
            >
                <img :src="aiFloatIcon" alt="AI助手" class="ai-float-icon" />
            </div>
            <!-- 聊天窗口 -->
            <div 
                class="ai-chat-modal" 
                :class="{ 'fullscreen': isFullscreen, 'dragging': isDragging, 'showing-history': showingHistory }" 
                v-show="showChatWindow"
                :style="modalStyle"
                ref="chatModalRef"
            >
                <div class="ai-chat-header" @mousedown="startDrag">
                    <div class="ai-chat-title">
                        <img :src="aiAssistantAvatar" alt="AI助手" class="ai-header-avatar" />
                        <span>心理健康科技云平台AI助手</span>
                    </div>
                    <div class="ai-chat-controls">
                        <el-icon @click="toggleHistory" :class="{ 'active': showingHistory }"><Clock /></el-icon>
                        <el-icon @click="toggleFullscreen" style="margin-left: 4px;">
                          <FullScreen />
                        </el-icon>
                        <el-icon @click="toggleChatWindow"><Close /></el-icon>
                    </div>
                </div>
                <div class="ai-chat-body">
                    <!-- 小窗模式下的会话列表 - 仅在点击历史按钮后显示 -->
                    <div class="ai-chat-tabs" v-if="!isFullscreen && showingHistory">
                        <div class="tab-header">
                            <div class="tab-title">会话列表</div>
                            <el-button type="text" class="clear-history-btn" @click="clearAllHistory">清空所有记录</el-button>
                        </div>
                        <div class="chat-list">
                            <div 
                                v-for="(chat, index) in chatList.slice(0, 30)" 
                                :key="index" 
                                class="chat-item"
                                :class="{ 'active': currentChatIndex === index }"
                                @click="switchChat(index)"
                            >
                                <span class="chat-title">{{ chat.title }}</span>
                                <div class="chat-actions">
                                    <el-button
                                        type="text"
                                        size="small"
                                        class="edit-btn"
                                        @click.stop="editChat(index, $event)"
                                    >
                                        <el-icon><Edit /></el-icon>
                                    </el-button>
                                <el-button
                                    type="text"
                                    size="small"
                                    class="delete-btn"
                                    @click.stop="deleteChat(index, $event)"
                                >
                                    <el-icon><Delete /></el-icon>
                                </el-button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- 移除全屏模式下的常见问题区域 -->
                    <!-- 聊天内容区域 -->
                    <div class="ai-chat-container" :class="{ 'with-tabs': !isFullscreen && showingHistory }">
                        <div v-if="chatList.length === 0" class="ai-empty-chat">
                          <div class="ai-empty-title">欢迎使用AI助手</div>
                          <div class="ai-empty-desc">创建新会话后即可开始聊天</div>
                          <el-button type="primary" @click="createNewChat">新建对话</el-button>
                        </div>
                        <AIChat 
                            v-if="showChatWindow && showAiHelper"
                            :customAvatar="aiAssistantAvatar" 
                            :isFullscreen="isFullscreen"
                            :showHistory="showingHistory"
                            :currentChatId="chatList[currentChatIndex]?.id"
                            :thinkingMode="thinkingMode"
                            @switchConversation="handleSwitchConversation"
                            ref="aiChatRef"
                            @reasoning-update="onReasoningUpdate"
                        />
                        <!-- 聊天内容区域下方展示思考过程 -->
                        <transition name="fade">
                          <div v-if="reasoningContent" class="ai-reasoning-block">
                            <div class="ai-reasoning-header">
                              <div class="ai-reasoning-title">
                                <el-icon><Cpu /></el-icon> AI思考过程
                              </div>
                              <el-button type="text" @click="reasoningContent = ''" size="small">
                                <el-icon><Close /></el-icon>
                              </el-button>
                            </div>
                            <div class="ai-reasoning-content">{{ reasoningContent }}</div>
                          </div>
                        </transition>
                    </div>
                    <!-- 移除底部操作区 -->
                </div>
                <!-- 删除以下底部深度思考按钮区域 -->
                <!-- <template v-if="showChatWindow && showAiHelper">
                  <div class="ai-chat-toolbar flat-footer-actions" style="padding: 8px 16px; display: flex; align-items: center; gap: 12px;">
                    <el-tooltip content="开启后AI会展示思考过程，帮助理解回答的推理逻辑" placement="top">
                      <el-button 
                        :type="thinkingMode ? 'primary' : 'default'" 
                        @click="toggleThinkingMode" 
                        size="small"
                      >
                        <el-icon><Cpu /></el-icon> 深度思考
                      </el-button>
                    </el-tooltip>
                  </div>
                </template> -->
            </div>
        </div>
    </el-config-provider>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import useSettingsStore from '@/store/modules/settings';
import { handleThemeStyle } from '@/utils/theme';
import useAppStore from '@/store/modules/app';
import { FullScreen, Close, Clock, Plus, Menu, Delete, Cpu, Edit, Top, Bottom } from '@element-plus/icons-vue';
import AIChat from '@/components/AI/AIChat.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { useRoute } from 'vue-router';
import useUserStore from '@/store/modules/user'
import { checkPermi } from '@/utils/permission';

const appStore = useAppStore();

// AI聊天相关
// 引入资源
const aiFloatIcon1 = new URL('@/assets/images/Ai/ai-1.png', import.meta.url).href;
const aiFloatIcon2 = new URL('@/assets/images/Ai/ai-2.png', import.meta.url).href;
const aiFloatIcon = ref(aiFloatIcon1);

let floatIconTimer = null;
onMounted(() => {
  let toggle = false;
  floatIconTimer = setInterval(() => {
    toggle = !toggle;
    aiFloatIcon.value = toggle ? aiFloatIcon2 : aiFloatIcon1;
  }, 1250); // 1.25秒切换一次
});
onBeforeUnmount(() => {
  if (floatIconTimer) clearInterval(floatIconTimer);
});
const aiAssistantAvatar = new URL('@/assets/images/Ai/ai-1.png', import.meta.url).href;

// 悬浮球拖动相关
const floatButtonPosition = ref({ x: window.innerWidth - 90, y: window.innerHeight - 90 }); // 初始右下角
const isDraggingFloatButton = ref(false);
const dragOffsetFloatButton = ref({ x: 0, y: 0 });
const floatButtonRef = ref<HTMLElement | null>(null);
let dragStartPos = { x: 0, y: 0 };

function startDragFloatButton(e: MouseEvent) {
    if (e.button !== 0) return; // 只允许左键
    isDraggingFloatButton.value = false;
    dragStartPos = { x: e.clientX, y: e.clientY };
    const rect = (floatButtonRef.value as HTMLElement).getBoundingClientRect();
    dragOffsetFloatButton.value = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
    };
    document.addEventListener('mousemove', onDragFloatButton, { passive: false });
    document.addEventListener('mouseup', stopDragFloatButton);
}

function onDragFloatButton(e: MouseEvent) {
    const moveX = Math.abs(e.clientX - dragStartPos.x);
    const moveY = Math.abs(e.clientY - dragStartPos.y);
    if (moveX > 3 || moveY > 3) {
        isDraggingFloatButton.value = true;
    }
    if (!isDraggingFloatButton.value) return;
    e.preventDefault();
    const btnW = 60, btnH = 60;
    const newX = Math.max(0, Math.min(e.clientX - dragOffsetFloatButton.value.x, window.innerWidth - btnW));
    const newY = Math.max(0, Math.min(e.clientY - dragOffsetFloatButton.value.y, window.innerHeight - btnH));
    floatButtonPosition.value = { x: newX, y: newY };
}

function stopDragFloatButton(e: MouseEvent) {
    document.removeEventListener('mousemove', onDragFloatButton);
    document.removeEventListener('mouseup', stopDragFloatButton);
    // 只有没有发生拖动时才算点击
    if (!isDraggingFloatButton.value) {
        toggleChatWindow();
    }
}

// 控制状态
const showChatWindow = ref(false);
const isFullscreen = ref(false);
const showingHistory = ref(false);
const chatModalRef = ref<HTMLElement | null>(null);
const aiChatRef = ref<InstanceType<typeof AIChat> | null>(null);

// 拖动相关
const isDragging = ref(false);
const modalPosition = ref({ x: 0, y: 0 }); // 初始位置设为0，在toggleChatWindow中计算右下角位置
const dragOffset = ref({ x: 0, y: 0 });

// 会话相关
const currentChatIndex = ref(0);
const chatList = ref<{ id: string; title: string }[]>([]);
const isLoadingChatList = ref(false); // 添加加载状态

// 常见问题
const faqs = ref([
    '您怎么称呼我?',
    '人工电话咨询时间是几点?',
    '系统可以自主学习吗?'
]);

// 深度思考
const thinkingMode = ref(false); // 深度思考默认关闭
const reasoningContent = ref("");
// AIChat组件通过自定义事件@reasoning-update传递思考过程
function onReasoningUpdate(content: string) {
  reasoningContent.value = content;
}

// 切换深度思考模式
function toggleThinkingMode() {
  thinkingMode.value = !thinkingMode.value;
  if (!thinkingMode.value) {
    // 关闭深度思考时清空思考过程
    reasoningContent.value = '';
  }
}

// 点赞点踩
const likeStatus = ref<'up' | 'down' | ''>('')
function setLike(type: 'up' | 'down') {
  likeStatus.value = likeStatus.value === type ? '' : type
}

// 计算样式
const modalStyle = computed(() => {
    if (isFullscreen.value) {
        return {
            top: '0',
            left: '0',
            width: '100%',
            height: '100%'
        };
    }
    
    return {
        top: `${modalPosition.value.y}px`,
        left: `${modalPosition.value.x}px`,
        width: '425px', 
        height: '650px' 
    };
});

// 获取会话列表
const fetchChatList = async () => {
    // 如果当前页面不显示AI助手，直接返回
    if (!showAiHelper.value) return;
    // 防止频繁调用，添加加载状态检查
    if (aiChatRef.value && !isLoadingChatList.value) {
        isLoadingChatList.value = true;
        try {
            const conversations = await aiChatRef.value.getConversations();
            if (conversations && conversations.length > 0) {
                // 最多显示20条会话记录
                chatList.value = conversations.slice(0, 50).map(conv => ({
                    id: conv.conversationId,
                    title: conv.title
                }));
                
                // 如果当前索引超出范围，重置为0
                if (currentChatIndex.value >= chatList.value.length) {
                    currentChatIndex.value = 0;
                }
            } else {
                chatList.value = [];
            }
        } catch (error) {
            console.error('获取会话列表失败:', error);
        } finally {
            isLoadingChatList.value = false;
        }
    }
};

// 切换历史记录显示
const toggleHistory = () => {
    showingHistory.value = !showingHistory.value;
    
    // 如果显示历史记录，刷新会话列表
    if (showingHistory.value) {
        fetchChatList();
    }
};

// 创建新会话
const createNewChat = async () => {
  console.log('点击新建对话', aiChatRef.value);
  if (aiChatRef.value) {
    const title = '新会话 ' + new Date().toLocaleTimeString();
    const success = await aiChatRef.value.createAndSwitchConversation(title);
    console.log('新建对话结果', success);
    if (success) {
      showingHistory.value = false;
      await fetchChatList();
      if (chatList.value.length > 0) {
        currentChatIndex.value = 0;
      }
      console.log('已创建并切换到新会话');
    } else {
      console.error('创建新会话失败');
      ElMessage.error('创建新会话失败');
    }
  } else {
    // AIChat 组件未加载，延迟重试
    console.warn('AIChat组件未加载，延迟重试');
    setTimeout(createNewChat, 300);
  }
};

// 切换会话
const switchChat = async (index: number) => {
    if (currentChatIndex.value === index || !chatList.value[index]) {
        console.log('跳过切换：当前索引相同或索引无效');
        return;
    }
    
    console.log(`切换会话：从索引 ${currentChatIndex.value} 到 ${index}`);
    currentChatIndex.value = index;
    
    // 通知AIChat组件切换会话
    if (aiChatRef.value) {
        const chatId = chatList.value[index].id;
        console.log('准备切换到会话ID:', chatId);
        
        try {
            const success = await aiChatRef.value.switchToConversation(chatId);
            
            if (success) {
                console.log('成功切换到会话');
                // 切换会话后自动隐藏历史列表
                showingHistory.value = false;
            } else {
                console.error('切换会话失败');
                ElMessage.error('切换会话失败');
                
                // 刷新会话列表
                await fetchChatList();
                
                // 尝试修正索引
                if (chatList.value.length > 0) {
                    if (currentChatIndex.value >= chatList.value.length) {
                        currentChatIndex.value = 0;
                    }
                }
            }
        } catch (error) {
            console.error('切换会话过程中发生异常:', error);
            ElMessage.error('切换会话失败，请重试');
        }
    }
};

// 删除会话
const deleteChat = async (index: number, event: Event) => {
    // 阻止事件冒泡，避免触发switchChat
    event.stopPropagation();
    
    if (!chatList.value[index]) return;
    
    const chatId = chatList.value[index].id;
    const chatTitle = chatList.value[index].title;
    
    ElMessageBox.confirm(`确定要删除会话"${chatTitle}"吗？`, '删除会话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        // 将确认框定位在AI窗口上方
        appendTo: '.ai-chat-modal',
        customClass: 'ai-delete-confirm-dialog'
    }).then(async () => {
        if (aiChatRef.value) {
            const success = await aiChatRef.value.deleteConversation(chatId);
            
            if (success) {
                ElMessage({
                    message: '删除会话成功',
                    type: 'success',
                    customClass: 'ai-message-in-chat'
                });
                
                // 刷新会话列表
                await fetchChatList();
                
                // 如果删除的是当前会话，切换到第一个会话
                if (currentChatIndex.value === index) {
                    if (chatList.value.length > 0) {
                        switchChat(0);
                    }
                } else if (currentChatIndex.value > index) {
                    // 如果删除的是前面的会话，当前索引需要减1
                    currentChatIndex.value--;
                }
            } else {
                ElMessage({
                    message: '删除会话失败',
                    type: 'error',
                    customClass: 'ai-message-in-chat'
                });
            }
        }
    }).catch(() => {});
};

// 显示上下文菜单
const showContextMenu = (index: number, event: MouseEvent) => {
    // 简化处理，直接调用删除方法
    deleteChat(index, event);
};

// 编辑会话
const editChat = (index: number, event: MouseEvent) => {
    event.stopPropagation();
    
    if (!chatList.value[index]) return;
    
    const chatId = chatList.value[index].id;
    const chatTitle = chatList.value[index].title;
    
    ElMessageBox.prompt(`请输入新的会话标题`, '重命名会话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: chatTitle,
        // 将确认框定位在AI窗口上方
        appendTo: '.ai-chat-modal',
        customClass: 'ai-delete-confirm-dialog'
    }).then(async ({ value }) => {
        if (aiChatRef.value) {
            try {
                // 调用AIChat组件的方法更新会话标题
                await aiChatRef.value.updateConversationTitle(chatId, value);
                
                ElMessage({
                    message: '重命名会话成功',
                    type: 'success',
                    customClass: 'ai-message-in-chat'
                });
                
                // 更新本地会话列表
                chatList.value[index].title = value;
            } catch (error) {
                console.error('重命名会话失败:', error);
                ElMessage({
                    message: '重命名会话失败',
                    type: 'error',
                    customClass: 'ai-message-in-chat'
                });
            }
        }
    }).catch(() => {});
};

// 清空所有历史记录
const clearAllHistory = () => {
    if (chatList.value.length === 0) {
        ElMessage({
            message: '没有可清空的历史记录',
            type: 'info',
            customClass: 'ai-message-in-chat'
        });
        return;
    }
    
    ElMessageBox.confirm('确定要清空所有历史记录吗？此操作不可恢复。', '清空历史记录', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        appendTo: '.ai-chat-modal',
        customClass: 'ai-delete-confirm-dialog'
    }).then(async () => {
        if (aiChatRef.value) {
            try {
                // 逐个删除会话
                for (const chat of chatList.value) {
                    await aiChatRef.value.deleteConversation(chat.id);
                }
                
                // 清空本地会话列表
                chatList.value = [];
                currentChatIndex.value = 0;
                
                ElMessage({
                    message: '已清空所有历史记录',
                    type: 'success',
                    customClass: 'ai-message-in-chat'
                });
                
                // 关闭历史记录面板
                showingHistory.value = false;
            } catch (error) {
                console.error('清空历史记录失败:', error);
                ElMessage({
                    message: '清空历史记录失败',
                    type: 'error',
                    customClass: 'ai-message-in-chat'
                });
            }
        }
    }).catch(() => {});
};

// 处理AIChat组件发出的切换会话事件
const handleSwitchConversation = async (conversationId: string) => {
    // 查找对应的会话索引，如果没找到才刷新列表
    let index = chatList.value.findIndex(chat => chat.id === conversationId);
    
    // 如果没找到且列表不为空，刷新一次会话列表
    if (index === -1 && chatList.value.length > 0) {
        await fetchChatList();
        index = chatList.value.findIndex(chat => chat.id === conversationId);
    }
    
    if (index !== -1) {
        currentChatIndex.value = index;
    }
};

// 提问常见问题
const askFaq = (question: string) => {
    if (aiChatRef.value) {
        aiChatRef.value.askQuestion(question);
    }
};

// 开始拖动
const startDrag = (e: MouseEvent) => {
    if (isFullscreen.value) return;
    // 只允许鼠标左键拖动
    if (e.button !== 0) return;
    // 只有在点击标题栏区域时才允许拖动
    const target = e.target as HTMLElement;
    if (target.closest('.ai-chat-controls')) {
        return;
    }
    
    e.preventDefault();
    isDragging.value = true;
    
    const rect = (chatModalRef.value as HTMLElement).getBoundingClientRect();
    dragOffset.value = {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
    };
    
    document.addEventListener('mousemove', onDrag, { passive: false });
    document.addEventListener('mouseup', stopDrag);
};

// 拖动中
const onDrag = (e: MouseEvent) => {
    if (!isDragging.value) return;
    
    e.preventDefault();
    
    // 计算新位置，但限制在视窗范围内
    const newX = Math.max(0, Math.min(e.clientX - dragOffset.value.x, window.innerWidth - 400));
    const newY = Math.max(0, Math.min(e.clientY - dragOffset.value.y, window.innerHeight - 100));
    
    // 使用requestAnimationFrame优化性能
    requestAnimationFrame(() => {
        modalPosition.value = {
            x: newX,
            y: newY
        };
    });
};

// 停止拖动
const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
};

// 切换聊天窗口显示
const toggleChatWindow = () => {
    showChatWindow.value = !showChatWindow.value;
    
    // 打开窗口时重置历史记录显示状态
    if (showChatWindow.value) {
        showingHistory.value = false;
        
        // 只有在首次打开或列表为空时获取会话列表
        if (chatList.value.length === 0) {
            fetchChatList();
        }
        
        // 设置窗口位置到右下角
        nextTick(() => {
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const modalWidth = 400;
            const modalHeight = 600;
            
            modalPosition.value = {
                x: windowWidth - modalWidth - 30, // 距离右边缘30px
                y: windowHeight - modalHeight - 30 // 距离下边缘30px
            };
        });
    }
};

// 切换全屏
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value;
    
    // 全屏时重置历史记录显示状态
    if (isFullscreen.value) {
        showingHistory.value = false;
    } else {
        // 从全屏恢复时，设置到右下角
        nextTick(() => {
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;
            const modalWidth = 400;
            const modalHeight = 600;
            
            modalPosition.value = {
                x: windowWidth - modalWidth - 30, // 距离右边缘30px
                y: windowHeight - modalHeight - 30 // 距离下边缘30px
            };
        });
    }
};

// ESC键监听 - 用于关闭窗口或退出全屏
const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
        if (isFullscreen.value) {
            isFullscreen.value = false;
        } else if (showChatWindow.value) {
            showChatWindow.value = false;
        }
    }
};

// 监听全屏状态变化
watch(isFullscreen, (newValue) => {
    if (aiChatRef.value) {
        aiChatRef.value.updateLayout(newValue);
    }
});

// 监听聊天列表变化
watch(chatList, (newList) => {
    // 如果没有会话，显示创建会话界面
    if (newList.length === 0) {
        // 重置当前索引
        currentChatIndex.value = 0;
    }
}, { deep: true });

const route = useRoute();
const userStore = useUserStore();

const showAiHelper = computed(() => {
    const hiddenPaths = ['/login', '/register', '/401', '/404'];
    // 检查路由和权限
    return !hiddenPaths.includes(route.path) && checkPermi(['ai:assistant:view']);
});

onMounted(() => {
    nextTick(() => {
        // 初始化主题样式
        handleThemeStyle(useSettingsStore().theme);
    });
    
    // 添加ESC键监听
    document.addEventListener('keydown', handleKeyDown);
    
    // 监听窗口大小变化，确保聊天窗口始终在可见区域
    window.addEventListener('resize', handleWindowResize);
    
    // 只在初始显示聊天窗口时获取会话列表
    if (showChatWindow.value) {
        fetchChatList();
    }
});

// 处理窗口大小变化
const handleWindowResize = () => {
    if (showChatWindow.value && !isFullscreen.value) {
        const windowWidth = window.innerWidth;
        const windowHeight = window.innerHeight;
        const modalWidth = 400;
        const modalHeight = 600;
        
        // 确保窗口始终在右下角
        modalPosition.value = {
            x: windowWidth - modalWidth - 30,
            y: windowHeight - modalHeight - 30
        };
    }
};

onBeforeUnmount(() => {
    document.removeEventListener('keydown', handleKeyDown);
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
    window.removeEventListener('resize', handleWindowResize);
});
</script>

<style scoped>
/* 悬浮按钮样式 */
.ai-float-button {
    position: fixed;
    left: 0;
    top: 0;
    /* 不设置宽高、圆角、背景色、阴影等 */
    background: none;
    box-shadow: none;
    border: none;
    padding: 0;
    margin: 0;
    z-index: 9999;
    cursor: pointer;
    user-select: none;
    display: inline-block;
    overflow: visible;
}
.float-animate {
    animation: floatY 2.4s ease-in-out infinite;
}
@keyframes floatY {
    0% { transform: translateY(0); }
    50% { transform: translateY(-16px); }
    100% { transform: translateY(0); }
}
.ai-float-button.dragging {
    transition: none;
}

.ai-float-icon {
    width: 75px;
    height: 75px;
    object-fit: contain;
    display: block;
    background: none;
    border: none;
    box-shadow: none;
    border-radius: 0;
    pointer-events: none;
}

/* 聊天窗口样式 */
.ai-chat-modal {
    position: fixed;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    z-index: 10000;
    transition: all 0.2s ease;
    will-change: transform;
}

/* 拖动时禁用过渡效果，提高流畅度 */
.ai-chat-modal.dragging {
    transition: none;
}

/* 全屏模式 */
.ai-chat-modal.fullscreen {
    border-radius: 0;
}

/* 聊天窗口头部 */
.ai-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 16px;
    background-color: #1976D2;
    color: white;
    cursor: move;
    user-select: none;
}

.ai-chat-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
}

.ai-header-avatar {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    object-fit: cover;
}

.ai-chat-controls {
    display: flex;
    gap: 12px;
}

.ai-chat-controls .el-icon {
    font-size: 18px;
    cursor: pointer;
    color: white;
    transition: all 0.2s ease;
    padding: 6px;
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.ai-chat-controls .el-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.ai-chat-controls .el-icon.active {
    color: #ffeb3b;
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 0.2);
}

/* 点赞点踩按钮样式 */
.ai-chat-controls .thumbs-up,
.ai-chat-controls .thumbs-down {
    font-size: 20px;
    padding: 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.ai-chat-controls .thumbs-up.active {
    color: #67c23a;
    background-color: rgba(103, 194, 58, 0.1);
}

.ai-chat-controls .thumbs-down.active {
    color: #f56c6c;
    background-color: rgba(245, 108, 108, 0.1);
}

.ai-chat-controls .thumbs-up:hover {
    background-color: rgba(103, 194, 58, 0.1);
}

.ai-chat-controls .thumbs-down:hover {
    background-color: rgba(245, 108, 108, 0.1);
}

/* 聊天主体区域 */
.ai-chat-body {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: relative;
    background-color: #f5f7fa;
}

/* 会话选项卡 */
.ai-chat-tabs {
    padding: 16px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #ffffff;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eaeaea;
}

.tab-title {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.clear-history-btn {
    font-size: 13px;
    color: #909399;
}

.clear-history-btn:hover {
    color: #f56c6c;
}

.chat-list {
    display: flex;
    flex-direction: column;
    gap: 0;
    max-height: 300px;
    overflow-y: auto;
    padding: 0 4px;
}

.chat-list::-webkit-scrollbar {
    width: 6px;
}

.chat-list::-webkit-scrollbar-thumb {
    background: #e0e0e0;
    border-radius: 3px;
}

.chat-list::-webkit-scrollbar-thumb:hover {
    background: #c0c0c0;
}

.chat-item {
    padding: 10px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    background-color: #f7f7f7;
    color: #333;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    border: 1px solid transparent;
}

.chat-item.active {
    background-color: #edf5ff;
    color: #1976D2;
    border: 1px solid #c6e2ff;
}

.chat-title {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.chat-actions {
    display: flex;
    align-items: center;
    visibility: hidden;
}

.chat-item:hover .chat-actions {
    visibility: visible;
}

.edit-btn {
    padding: 0 4px;
    color: #409EFF;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
}

.edit-btn:hover {
    color: #66b1ff;
}

.delete-btn {
    padding: 0 4px;
    color: #f56c6c;
    font-size: 14px;
    cursor: pointer;
    transition: color 0.2s;
}

.delete-btn:hover {
    color: #f78989;
}

/* 常见问题区域 */
.ai-faq-section {
    padding: 16px;
    border-bottom: 1px solid #e6e6e6;
    background-color: #f9f9f9;
}

.faq-header {
    font-size: 14px;
    color: #333;
    margin-bottom: 12px;
}

.faq-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.faq-item {
    padding: 10px;
    background-color: #fff;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
}

.faq-item:hover {
    background-color: #f5f5f5;
}

/* 聊天内容区域 */
.ai-chat-container {
    flex: 1;
    overflow: hidden;
}

.ai-chat-container.with-tabs {
    height: calc(100% - 370px);
}

/* 全屏模式下侧边栏的样式调整 */
:deep(.ai-chat-container .sidebar) {
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
}

.ai-chat-container.with-faq {
    height: calc(100% - 180px);
}

/* 确保AIChat组件占满容器 */
.ai-chat-container :deep(.ai-chat-container) {
    height: 100%;
    border-radius: 0;
}

/* 底部操作区域 */
.ai-chat-footer {
    border-top: 1px solid #e6e6e6;
    background-color: #f9f9f9;
    padding: 10px;
}

.footer-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.history-toggle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #666;
    transition: all 0.2s;
}

.history-toggle:hover {
    background-color: #e6e6e6;
}

.history-toggle .el-icon {
    font-size: 20px;
}

.new-chat-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background-color: #1976D2;
    color: white;
    border-radius: 18px;
    padding: 8px 16px;
    cursor: pointer;
    transition: all 0.2s;
}

.new-chat-btn:hover {
    background-color: #1565C0;
    transform: scale(1.02);
}

.new-chat-btn .el-icon {
    font-size: 16px;
}

.new-chat-btn span {
    font-size: 14px;
}

/* 自定义确认框和消息样式 */
:deep(.ai-delete-confirm-dialog) {
    z-index: 10001; /* 确保在聊天窗口之上 */
}

:deep(.ai-message-in-chat) {
    z-index: 10001;
    position: fixed;
    top: 60px; /* 定位在聊天窗口顶部下方 */
}

.footer-actions.flat-footer-actions {
    background: none;
    box-shadow: none;
    border: none;
    padding: 0 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.flat-new-chat-btn {
    display: flex;
    align-items: center;
    color: #1976D2;
    font-size: 15px;
    cursor: pointer;
    font-weight: 500;
    background: none;
    border: none;
    padding: 0;
    gap: 4px;
    transition: color 0.2s;
}
.flat-new-chat-btn:hover {
    color: #1565C0;
}
.flat-new-chat-btn .el-icon {
    font-size: 18px;
}
.flat-history-toggle {
    color: #1976D2;
    font-size: 18px;
    cursor: pointer;
    background: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    transition: color 0.2s;
}
.flat-history-toggle:hover {
    color: #1565C0;
}
.ai-chat-toolbar.flat-footer-actions {
    background: none;
    box-shadow: none;
    border: none;
    padding: 0 8px 8px 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.ai-empty-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #888;
  gap: 16px;
}
.ai-empty-title {
  font-size: 20px;
  font-weight: bold;
}
.ai-empty-desc {
  font-size: 14px;
  color: #aaa;
}
.ai-reasoning-block {
  background: #f8f5ff;
  border-left: 4px solid #7e57c2;
  margin: 12px 16px;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.ai-reasoning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.ai-reasoning-title {
  font-weight: 600;
  color: #7e57c2;
  margin-bottom: 0;
  display: flex;
  align-items: center;
  gap: 6px;
}

.ai-reasoning-content {
  color: #333;
  font-size: 14px;
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  line-height: 1.6;
  padding-right: 8px;
}

/* 思考过程滚动条样式 */
.ai-reasoning-content::-webkit-scrollbar {
  width: 6px;
}

.ai-reasoning-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.ai-reasoning-content::-webkit-scrollbar-thumb {
  background: #c4b5e9;
  border-radius: 3px;
}

.ai-reasoning-content::-webkit-scrollbar-thumb:hover {
  background: #7e57c2;
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 小窗模式下会话列表风格与全屏一致 */
.ai-chat-tabs {
  background-color: #fff;
  border-right: 1px solid #e5e7eb;
  padding-bottom: 20px;
}
.chat-list {
  max-height: 650px;
  overflow-y: auto;
  padding: 0 16px;
}
.chat-list::-webkit-scrollbar {
  width: 6px;
}
.chat-list::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 3px;
}
.chat-item {
  padding: 14px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  background-color: #f7f7f7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.chat-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.08);
}
.chat-item.active {
  background-color: #edf5ff;
  border: 1px solid #c6e2ff;
}
.chat-title {
  font-size: 15px;
  color: #4b5563;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  padding-left: 4px;
}
.chat-actions {
  display: flex;
  align-items: center;
  visibility: hidden;
}
.chat-item:hover .chat-actions,
.chat-item.active .chat-actions {
  visibility: visible;
}
</style>
