<template>
  <div class="dashboard-white">
    <div class="header">
      <div class="title">心理健康数据中心</div>
      <div class="digital-clock">{{ currentTime }}</div>
    </div>
    <div class="main-content">

      <div class="modules-container">
        <div class="module-item">
          <PlanInfoCarousel />
        </div>
        <div class="module-item" v-hasPermi="['system:data:dashboard']">
          <GradeInfoCarousel />
        </div>
        <div class="module-item" v-hasPermi="['system:data:dashboard']">
          <UserOverview />
        </div>
        <div class="module-item warning-module" v-hasPermi="['system:data:dashboard']">
          <WarningAnalysis />
        </div>
        <div class="module-item assessment-module" v-hasPermi="['system:data:dashboard']">
          <AssessmentEvaluation />
        </div>
        <div class="module-item" v-hasPermi="['system:data:dashboard']">
          <DeviceStatusBoard />
        </div>
        <div class="module-item" v-hasPermi="['system:data:dashboard']">
          <ScienceReliefRadar />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount, defineAsyncComponent } from 'vue';

// 使用异步组件定义的方式导入组件
const GradeInfoCarousel = defineAsyncComponent(() => import('@/views/home/<USER>/GradeInfoCarousel.vue'));
const UserOverview = defineAsyncComponent(() => import('@/views/home/<USER>/UserOverview.vue'));
const WarningAnalysis = defineAsyncComponent(() => import('@/views/home/<USER>/WarningAnalysis.vue'));
const AssessmentEvaluation = defineAsyncComponent(() => import('@/views/home/<USER>/AssessmentEvaluation.vue'));
const DeviceStatusBoard = defineAsyncComponent(() => import('@/views/home/<USER>/DeviceStatusBoard.vue'));
const ScienceReliefRadar = defineAsyncComponent(() => import('@/views/home/<USER>/ScienceReliefRadar.vue'));
const PlanInfoCarousel = defineAsyncComponent(() => import('@/views/home/<USER>/PlanInfoCarousel.vue'));

// 数字时钟
const currentTime = ref('00:00:00');
let clockTimer: number | null = null;

const updateClock = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const date = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  currentTime.value = `${year}-${month}-${date} ${hours}:${minutes}:${seconds}`;
};

onMounted(() => {
  // 初始化时钟
  updateClock();
  clockTimer = window.setInterval(updateClock, 1000);
});

onBeforeUnmount(() => {
  if (clockTimer) {
    clearInterval(clockTimer);
  }
});
</script>

<style lang="scss" scoped>
.dashboard-white {
  width: calc(100% - 50px);
  /* 使用百分比而不是vw，减去侧边栏预留空间 */
  height: 100vh;
  margin-left: 50px;
  /* 固定左侧边距 */
  padding: 0;
  background: white;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(230, 240, 255, 0.5), transparent),
    radial-gradient(circle at 80% 20%, rgba(235, 245, 255, 0.5), transparent);
  overflow: auto;
  /* 允许页面滚动 */
  display: flex;
  flex-direction: column;
}

.header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  background: rgba(255, 255, 255, 0.85);
  position: relative;
  overflow: hidden;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title {
  font-size: 24px;
  font-weight: bold;
  color: #1890FF;
  letter-spacing: 2px;
}

.digital-clock {
  position: absolute;
  right: 20px;
  font-size: 16px;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  color: #1890FF;
}

.main-content {
  flex: 1;
  padding: 16px;
  box-sizing: border-box;
  overflow: visible;
  /* 确保内容可见 */
}

.modules-container {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 使用网格布局，2列 */
  grid-template-rows: repeat(3, minmax(300px, auto));
  /* 3行，最小高度300px */
  gap: 16px;
  padding-bottom: 20px;
  /* 底部添加些许空间 */
}

.module-item {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(232, 232, 232, 0.8);
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 让测试计划轮播占据整行 */
.module-item:first-child {
  grid-column: 1 / span 2;
}

/* 预警分析和心理测评看板模块高度调整 */
.warning-module, .assessment-module {
  min-height: 350px;
}

/* 特别增加心理测评看板的高度 */
.assessment-module {
  min-height: 400px;
}

.module-item:hover {
  box-shadow: 0 5px 15px rgba(24, 144, 255, 0.15);
  transform: translateY(-2px);
}

/* 修改子组件中的样式，使其适应白色主题 */
:deep(.module-title) {
  color: #1890FF !important;
}

:deep(.module-title::before) {
  background: linear-gradient(to bottom, #1890FF, rgba(24, 144, 255, 0.3)) !important;
}

:deep(.warning-analysis),
:deep(.assessment-evaluation),
:deep(.grade-carousel),
:deep(.user-overview),
:deep(.device-status-board),
:deep(.science-relief-radar),
:deep(.plan-carousel) {
  background: #fff !important;
  color: #333 !important;
}

:deep(.chart-section),
:deep(.main-chart),
:deep(.aux-chart),
:deep(.left-column),
:deep(.right-column),
:deep(.carousel-card) {
  background: rgba(247, 250, 255, 0.6) !important;
}

:deep(.device-table-container) {
  background: rgba(247, 250, 255, 0.6) !important;
}

:deep(.table-header) {
  background: rgba(235, 245, 255, 0.8) !important;
  color: #333 !important;
}

:deep(.table-row) {
  color: #333 !important;
}

:deep(.table-row.stripe) {
  background: rgba(247, 250, 255, 0.6) !important;
}

/* 调整图表文字颜色 */
:deep(.echarts text) {
  fill: #333 !important;
}

/* 媒体查询，适应不同屏幕 */
@media screen and (max-width: 1200px) {
  .modules-container {
    grid-template-rows: repeat(3, minmax(250px, auto));
  }

  .module-item:first-child {
    grid-column: 1 / span 2;
  }
  
  /* 在小屏幕上也保持足够的高度 */
  .warning-module, .assessment-module {
    min-height: 320px;
  }
}

@media screen and (min-width: 1600px) {
  .modules-container {
    grid-template-rows: repeat(3, minmax(350px, auto));
  }

  .module-item:first-child {
    grid-column: 1 / span 2;
  }
  
  /* 在大屏幕上增加更多高度 */
  .warning-module, .assessment-module {
    min-height: 400px;
  }
}
</style>
