export interface FileDownloadVO {
    id: number;
    /**
     * 文件名
     */
    fileName: string;

    /**
     * 文件下载URL
     */
    url: string;

    /**
     * 文件类型
     */
    fileType: string;

    /**
     * 文件大小
     */
    fileSize: number;

    /**
     * 下载时间
     */
    downloadTime: string;

    /**
     * 状态
     */
    status: number;

    /**
     * 失败原因
     */
    failReason: string;

    /**
     * 下载类型
     */
    downloadType: number;
}

export interface FileDownloadForm extends BaseEntity {
    id: number;
    /**
     * 文件名
     */
    fileName?: string;

    /**
     * 文件下载URL
     */
    url?: string;

    /**
     * 文件类型
     */
    fileType?: string;

    /**
     * 文件大小
     */
    fileSize?: number;

    /**
     * 失败原因
     */
    failReason?: string;

    /**
     * 下载类型
     */
    downloadType?: number;
}

export interface FileDownloadQuery extends PageQuery {
    id: number;
    /**
     * 文件名
     */
    fileName?: string;

    /**
     * 文件下载URL
     */
    url?: string;

    /**
     * 文件类型
     */
    fileType?: string;

    /**
     * 状态
     */
    status?: number;

    /**
     * 下载类型
     */
    downloadType?: number;

    /**
     * 日期范围参数
     */
    params?: any;
}
