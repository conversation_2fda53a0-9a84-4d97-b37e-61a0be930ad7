<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="知识库名称" prop="knowledgeName">
        <el-input
          v-model="queryParams.knowledgeName"
          placeholder="请输入知识库名称"
          clearable
          @keyup.enter="handleQuery"
        />
      </el-form-item>
      <el-form-item label="知识库类型" prop="knowledgeType">
        <el-select v-model="queryParams.knowledgeType" placeholder="请选择知识库类型" clearable>
          <el-option label="文档库" value="document" />
          <el-option label="问答库" value="qa" />
          <el-option label="自定义" value="custom" />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option label="启用" value="0" />
          <el-option label="禁用" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="Plus"
          @click="handleAdd"
          v-hasPermi="['ai:knowledge:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="Edit"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['ai:knowledge:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="Delete"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['ai:knowledge:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="Document"
          :disabled="single"
          @click="handleDocuments"
          v-hasPermi="['ai:knowledge:query']"
        >文档管理</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="knowledgeList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="知识库ID" align="center" prop="knowledgeId" />
      <el-table-column label="知识库名称" align="center" prop="knowledgeName" :show-overflow-tooltip="true" />
      <el-table-column label="知识库描述" align="center" prop="knowledgeDesc" :show-overflow-tooltip="true" />
      <el-table-column label="知识库类型" align="center" prop="knowledgeType">
        <template #default="scope">
          <dict-tag :options="knowledgeTypeOptions" :value="scope.row.knowledgeType" />
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status">
        <template #default="scope">
          <dict-tag :options="statusOptions" :value="scope.row.status" />
        </template>
      </el-table-column>
      <el-table-column label="文档数量" align="center" prop="documentCount" />
      <el-table-column label="向量维度" align="center" prop="vectorDimension" />
      <el-table-column label="创建者" align="center" prop="userName" />
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button
            type="text"
            icon="Edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['ai:knowledge:edit']"
          >修改</el-button>
          <el-button
            type="text"
            icon="Document"
            @click="handleDocuments(scope.row)"
            v-hasPermi="['ai:knowledge:query']"
          >文档</el-button>
          <el-button
            type="text"
            icon="Delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['ai:knowledge:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total > 0"
      :total="total"
      v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改知识库对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="knowledgeRef" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="知识库名称" prop="knowledgeName">
          <el-input v-model="form.knowledgeName" placeholder="请输入知识库名称" />
        </el-form-item>
        <el-form-item label="知识库描述" prop="knowledgeDesc">
          <el-input v-model="form.knowledgeDesc" type="textarea" placeholder="请输入知识库描述" />
        </el-form-item>
        <el-form-item label="知识库类型" prop="knowledgeType">
          <el-select v-model="form.knowledgeType" placeholder="请选择知识库类型">
            <el-option label="文档库" value="document" />
            <el-option label="问答库" value="qa" />
            <el-option label="自定义" value="custom" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="向量维度" prop="vectorDimension">
          <el-input-number v-model="form.vectorDimension" :min="1" :max="4096" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Knowledge">
import { listKnowledge, getKnowledge, delKnowledge, addKnowledge, updateKnowledge } from "@/api/ai/knowledge";

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const knowledgeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const knowledgeTypeOptions = ref([
  { label: "文档库", value: "document" },
  { label: "问答库", value: "qa" },
  { label: "自定义", value: "custom" }
]);

const statusOptions = ref([
  { label: "启用", value: "0" },
  { label: "禁用", value: "1" }
]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    knowledgeName: null,
    knowledgeType: null,
    status: null
  },
  rules: {
    knowledgeName: [
      { required: true, message: "知识库名称不能为空", trigger: "blur" }
    ],
    knowledgeType: [
      { required: true, message: "知识库类型不能为空", trigger: "change" }
    ]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询知识库列表 */
function getList() {
  loading.value = true;
  listKnowledge(queryParams.value).then(response => {
    knowledgeList.value = response.data || [];
    total.value = response.total || 0;
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    knowledgeId: null,
    knowledgeName: null,
    knowledgeDesc: null,
    knowledgeType: "document",
    status: "0",
    vectorDimension: 1536,
    remark: null
  };
  proxy.$refs["knowledgeRef"]?.resetFields();
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.$refs["queryRef"]?.resetFields();
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.knowledgeId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加知识库";
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const knowledgeId = row.knowledgeId || ids.value[0];
  getKnowledge(knowledgeId).then(response => {
    form.value = response.data || response;
    open.value = true;
    title.value = "修改知识库";
  }).catch(() => {
    proxy.$modal.msgError("获取知识库信息失败");
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["knowledgeRef"].validate(valid => {
    if (valid) {
      if (form.value.knowledgeId != null) {
        updateKnowledge(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addKnowledge(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const knowledgeIds = row.knowledgeId || ids.value;
  proxy.$modal.confirm('是否确认删除知识库编号为"' + knowledgeIds + '"的数据项？').then(function() {
    return delKnowledge(knowledgeIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 文档管理按钮操作 */
function handleDocuments(row) {
  const knowledgeId = row.knowledgeId;
  proxy.$router.push(`/ai/knowledge/documents/${knowledgeId}`);
}

getList();
</script> 