package org.senyor.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.senyor.common.sensitive.annotation.Sensitive;
import org.senyor.common.sensitive.core.SensitiveStrategy;
import org.senyor.common.translation.annotation.Translation;
import org.senyor.common.translation.constant.TransConstant;
import org.senyor.system.domain.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;


/**
 * 用户信息视图对象 sys_user
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUser.class)
public class SysUserVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户角色
     */
    private String roleKey;
    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 机构ID
     */
    private String tenantId;

    /**
     * 部门ID
     */
    private Long deptId;

    /**
     * 用户账号
     */
    private String userName;

    /**
     * 用户昵称
     */
    private String nickName;

    /**
     * 用户类型（sys_user系统用户）
     */
    private String userType;

    /**
     * 用户邮箱
     */
    @Sensitive(strategy = SensitiveStrategy.EMAIL)
    private String email;

    /**
     * 手机号码
     */
    @Sensitive(strategy = SensitiveStrategy.PHONE)
    private String phonenumber;
    /**
     * 出生日期
     */
    private LocalDateTime birthDate;
    /**
     * 年龄
     */
    private Integer age;

    /**
     * 用户性别（0男 1女 2未知）
     */
    private String sex;

    /**
     * 头像地址
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL)
    private Long avatar;

    /**
     * 用户人脸信息
     */
    @Translation(type = TransConstant.OSS_ID_TO_URL)
    private Long faceImg;

    /**
     * 密码
     */
    @JsonIgnore
    @JsonProperty
    private String password;

    /**
     * 帐号状态（0正常 1停用）
     */
    private String status;

    /**
     * 最后登录IP
     */
    private String loginIp;

    /**
     * 最后登录时间
     */
    private Date loginDate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 部门名
     */
    @Translation(type = TransConstant.DEPT_ID_TO_NAME, mapper = "deptId")
    private String deptName;

    /**
     * 部门树
     */
    private String deptNameAll;

    /**
     * 角色对象
     */
    private List<SysRoleVo> roles;

    /**
     * 角色组
     */
    private Long[] roleIds;

    /**
     * 岗位组
     */
    private Long[] postIds;

    /**
     * 数据权限 当前角色ID
     */
    private Long roleId;

    /**
     * 祖级账号id列表
     */
    private String ancestors;

    /**
     * 父级账号
     */
    private Long parentUserId;

    // =================== 附加返回机构部分信息 =================
    /**
     * 机构logo
     */
    private String logo;

    /**
     * 机构名称
     */
    private String companyName;

    /**
     * 机构简称
     */
    private String companyShortName;

    // =================== 附加返回图片访问前缀 ============
    /**
     * 图片访问前缀
     */
    private String ossPrefix;

    /**
     * 拓展字段
     * */
    private String moreFields;

}
