package org.senyor.scale.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.domain.R;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.core.utils.StringUtils;
import org.senyor.common.core.validate.AddGroup;
import org.senyor.common.core.validate.EditGroup;
import org.senyor.common.excel.utils.ExcelUtil;
import org.senyor.common.idempotent.annotation.RepeatSubmit;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.log.annotation.Log;
import org.senyor.common.log.enums.BusinessType;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.senyor.scale.domain.ExportRequest;
import org.senyor.scale.domain.bo.ScaleInfoBo;
import org.senyor.scale.domain.bo.ScaleInfoRefreshBo;
import org.senyor.scale.domain.vo.ScaleInfoReportVo;
import org.senyor.scale.domain.vo.ScaleInfoVo;
import org.senyor.scale.service.IGenericExportService;
import org.senyor.scale.service.IScaleInfoService;
import org.senyor.system.service.ISysFileDownloadService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 量表信息
 *
 * <AUTHOR>
 * @date 2024-12-27
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/scale/scaleInfo")
public class ScaleInfoController extends BaseController {

    private final IScaleInfoService scaleInfoService;

    private final IGenericExportService exportService;
    private final ISysFileDownloadService fileDownloadService;
    private final ObjectMapper objectMapper = new ObjectMapper();
    /**
     * 查询量表信息列表
     */
    @SaCheckPermission("scale:scaleInfo:list")
    @GetMapping("/list")
    public TableDataInfo<ScaleInfoVo> list(ScaleInfoBo bo, PageQuery pageQuery) {
        return scaleInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出量表信息列表
     */
    @SaCheckPermission("scale:scaleInfo:export")
    @Log(title = "量表信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ScaleInfoBo bo, HttpServletResponse response) {
        List<ScaleInfoVo> list = scaleInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "量表信息", ScaleInfoVo.class, response);
    }

    /**
     * 获取量表信息详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("scale:scaleInfo:query")
    @GetMapping("/{id}")
    public R<ScaleInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(scaleInfoService.queryById(id));
    }

    /**
     * 获取量表ids信息详细信息
     *
     * @param scaleIds 量表ids
     */
    @SaCheckPermission("scale:scaleInfo:query")
    @GetMapping("/batch/{scaleIds}")
    public R<List<ScaleInfoVo>> getInfo(@NotNull(message = "量表id不能为空")
                                  @PathVariable String scaleIds) {
        return R.ok(scaleInfoService.queryByScaleIds(scaleIds));
    }

    /**
     * 新增量表信息
     */
    @SaCheckPermission("scale:scaleInfo:add")
    @Log(title = "量表信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ScaleInfoBo bo) {
        return toAjax(scaleInfoService.insertByBo(bo));
    }

    /**
     * 量表配置缓存刷新
     */
    @SaCheckPermission("scale:scaleInfo:refresh")
    @Log(title = "量表信息刷新", businessType = BusinessType.REFRESH)
    @GetMapping("/refresh")
    public R<Void> refreshCache(ScaleInfoRefreshBo bo) {
        if (bo != null && StringUtils.isBlank(bo.getTenantId())) {
            bo.setTenantId(LoginHelper.getTenantId());
        }
        scaleInfoService.loadAndInitScaleList(bo);
        return R.ok();
    }

    /**
     * 量表配置缓存刷新
     */
    @SaCheckPermission("scale:scaleInfo:published")
    @Log(title = "量表发布", businessType = BusinessType.UPDATE)
    @GetMapping("/published")
    public R<Void> published(ScaleInfoRefreshBo bo) {
        scaleInfoService.published(bo);
        return R.ok();
    }


    /**
     * 量表配置缓存刷新
     */
    @SaCheckPermission("scale:scaleInfo:unpublish")
    @Log(title = "量表取消发布", businessType = BusinessType.UPDATE)
    @GetMapping("/unpublish")
    public R<Void> unpublish(ScaleInfoRefreshBo bo) {
        scaleInfoService.unpublish(bo);
        return R.ok();
    }

    /**
     * 修改量表信息
     */
    @SaCheckPermission("scale:scaleInfo:edit")
    @Log(title = "量表信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ScaleInfoBo bo) {
        return toAjax(scaleInfoService.updateByBo(bo));
    }

    /**
     * 删除量表信息
     *
     * @param ids 主键串
     */
    @SaCheckPermission("scale:scaleInfo:remove")
    @Log(title = "量表信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(scaleInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 量表信息展示
     * @param scaleId
     * @return
     */
    @SaCheckPermission("scale:scaleInfo:showScaleInfo")
    @GetMapping("/showScaleInfoReport/{scaleId}")
    public R<ScaleInfoReportVo> showScaleInfoReport(@PathVariable Long scaleId){
        ScaleInfoReportVo scaleInfoReportVo = scaleInfoService.showScaleInfoReport(scaleId);
        return R.ok(scaleInfoReportVo);
    }

    /**
     * 导出Word
     * @param exportRequest
     * @return
     */
    @SaCheckPermission("scale:scaleInfo:exportWord")
    @PostMapping("/exportWord")
    public R<Void> exportWordReport(@RequestBody ExportRequest exportRequest) {
        ExportRequest request = new ExportRequest();
        request.setDictLabel(exportRequest.getDictLabel());
        request.setDictType(exportRequest.getDictType());
        request.setFileNamePrefix(exportRequest.getFileNamePrefix());
        Object templateData = exportRequest.getTemplateData();
        if (templateData == null) {
            log.error("templateData 为 null");
            return R.fail("请求数据不能为空");
        }
        if (templateData instanceof Map) {
            try {
                ObjectMapper mapper = new ObjectMapper();
                ScaleInfoReportVo scaleInfoReportVo = mapper.convertValue(
                    templateData,
                    ScaleInfoReportVo.class
                );
                Map<String, Object> preparedData = prepareData(scaleInfoReportVo);
//                log.info("预处理的数据：{}", JSON.toJSONString(preparedData));
                request.setTemplateData(preparedData);
            } catch (IllegalArgumentException e) {
                log.error("转换 ScaleInfoReportVo 失败", e);
                return R.fail("数据格式错误");
            }
        }
        else if (templateData instanceof ScaleInfoReportVo) {
            ScaleInfoReportVo scaleInfoReportVo = (ScaleInfoReportVo) templateData;
            Map<String, Object> preparedData = prepareData(scaleInfoReportVo);
//            log.info("预处理的数据：{}", JSON.toJSONString(preparedData));
            request.setTemplateData(preparedData);
        }
        else {
            log.error("未知的 templateData 类型: {}", templateData.getClass());
            return R.fail("不支持的数据类型");
        }
        exportService.exportWord(request);
        return R.ok("Word导出任务已提交");
    }

    private Map<String,Object> prepareData(ScaleInfoReportVo scaleInfoReportVo){
        Map<String,Object> datas = new HashMap<>();
        datas.put("scaleName",scaleInfoReportVo.getScaleInfoVo().getScaleName());
        datas.put("introduction",scaleInfoReportVo.getScaleInfoVo().getIntroduction());
        datas.put("description",scaleInfoReportVo.getScaleInfoVo().getDescription());

        if(scaleInfoReportVo.getScaleQuestionVosList() != null && !scaleInfoReportVo.getScaleQuestionVosList().isEmpty()){
            List<Map<String,Object>> scaleQuestions = scaleInfoReportVo.getScaleQuestionVosList().stream()
                .map(scaleQuestion -> {
                    Map<String,Object> map = new HashMap<>();
                    map.put("questionId", scaleQuestion.getQuestionId());
                    map.put("questionTitle",scaleQuestion.getQuestionTitle());
                    List<Map<String,String>> options = parseQuestionOptions(scaleQuestion.getQuestionOption());
                    map.put("questionOptions", options);
                    return map;
                }).collect(Collectors.toList());
            datas.put("scaleQuestionVosList",scaleQuestions);
        }
        return datas;
    }
    // 解析questionOption JSON字符串
    private List<Map<String,String>> parseQuestionOptions(String questionOptionJson) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.readValue(questionOptionJson,
                new TypeReference<List<Map<String,String>>>(){});
        } catch (Exception e) {
            log.error("解析questionOption失败", e);
            return Collections.emptyList();
        }
    }

    /**
     * 导出PDF
     * @param exportRequest
     * @return
     */
    @SaCheckPermission("scale:scaleInfo:exportPdf")
    @PostMapping("/exportPdf")
    public R<Void> exportPdfReport(@RequestBody ExportRequest exportRequest) {
        ExportRequest request = new ExportRequest();
        request.setDictLabel(exportRequest.getDictLabel());
        request.setDictType(exportRequest.getDictType());
        request.setFileNamePrefix(exportRequest.getFileNamePrefix());
        Object templateData = exportRequest.getTemplateData();
        if (templateData == null) {
            log.error("templateData 为 null");
            return R.fail("请求数据不能为空");
        }
        try {
            ScaleInfoReportVo scaleInfoReportVo;
            if (templateData instanceof Map) {
                scaleInfoReportVo = new ObjectMapper().convertValue(templateData, ScaleInfoReportVo.class);
            } else if (templateData instanceof ScaleInfoReportVo) {
                scaleInfoReportVo = (ScaleInfoReportVo) templateData;
            } else {
                log.error("未知的 templateData 类型: {}", templateData.getClass());
                return R.fail("不支持的数据类型");
            }
            Map<String, Object> preparedData = prepareData(scaleInfoReportVo);
            log.info("PDF预处理数据：{}", JsonUtils.toJsonString(preparedData));
            request.setTemplateData(preparedData);
            // 调用PDF导出服务
            exportService.exportPdf(request);
            return R.ok("PDF导出任务已提交，请到下载列表查看");
        } catch (IllegalArgumentException e) {
            log.error("数据转换失败", e);
            return R.fail("数据格式错误");
        }
    }

    /**
     * 批量导出Word
     * @param exportRequests
     * @return
     */
    @SaCheckPermission("scale:scaleInfo:exportWordMulti")
    @PostMapping("/exportBatchWord")
    public R<Void> exportBatchWord(@RequestBody List<ExportRequest> exportRequests) {
        log.info("接收到前端传递的数据为:{}",exportRequests);
        try {
            List<ExportRequest> processedRequests = exportRequests.stream()
                .map(req -> {
                    Map<String, Object> processedData = processTemplateData(req.getTemplateData());
                    req.setTemplateData(processedData);
                    return req;
                })
                .collect(Collectors.toList());
            exportService.exportBatchWord(processedRequests);
            return R.ok("批量导出任务已提交");
        } catch (Exception e) {
            log.error("批量导出失败", e);
            return R.fail("批量导出失败: " + e.getMessage());
        }
    }

    /**
     * 批量导出PDF
     * @param exportRequests
     * @return
     */
    @SaCheckPermission("scale:scaleInfo:exportPdfMulti")
    @PostMapping("/exportBatchPdf")
    public R<Void> exportBatchPdf(@RequestBody List<ExportRequest> exportRequests){
        try {
            List<ExportRequest> processedRequests = exportRequests.stream()
                .map(req -> {
                    Map<String, Object> processedData = processTemplateData(req.getTemplateData());
                    req.setTemplateData(processedData);
                    return req;
                })
                .collect(Collectors.toList());
            exportService.exportBatchPdf(processedRequests);
            return R.ok("批量导出任务已提交");
        } catch (Exception e) {
            log.error("批量导出失败", e);
            return R.fail("批量导出失败: " + e.getMessage());
        }
    }

    private Map<String, Object> processTemplateData(Object rawData) {
        try {
            if (rawData == null) {
                throw new ServiceException("模板数据不能为空");
            }
            if (rawData instanceof Map) {
                ObjectMapper mapper = new ObjectMapper();
                ScaleInfoReportVo reportVo = mapper.convertValue(rawData, ScaleInfoReportVo.class);
                return prepareData(reportVo);
            }
            else if (rawData instanceof ScaleInfoReportVo) {
                return prepareData((ScaleInfoReportVo) rawData);
            }
            else {
                throw new ServiceException("不支持的模板数据类型: " + rawData.getClass().getName());
            }
        } catch (Exception e) {
            throw new ServiceException("数据处理失败: " + e.getMessage());
        }
    }

}

