<template>
  <div class="basic-stats">
    <div class="module-title">基础数据实时看板</div>
    <div class="stats-container">
      <!-- 左侧数据展示 -->
      <div class="stats-cards left-stats">
        <div class="stat-card" v-for="(stat, index) in leftStatData" :key="index">
          <div class="stat-info">
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-value">
              <animated-number :value="stat.value" :duration="1000" />
              <span class="stat-unit">{{ stat.unit }}</span>
            </div>
              <div class="stat-change"
                   :class="stat.trend >= 0 ? 'positive' : 'negative'"
                   v-if="stat.showGrowth">
                  {{ stat.trend >= 0 ? '+' : '' }}{{ stat.trend }}%
              </div>
          </div>
        </div>
      </div>

      <!-- 右侧数据展示 -->
      <div class="stats-cards right-stats">
        <div class="stat-card" v-for="(stat, index) in rightStatData" :key="index">
          <div class="stat-info">
            <div class="stat-title">{{ stat.title }}</div>
            <div class="stat-value">
              <animated-number :value="stat.value" :duration="1000" />
              <span class="stat-unit">{{ stat.unit }}</span>
              <span class="stat-unit" v-if="stat.suffix">{{ stat.suffix }}</span>
            </div>
              <div class="stat-change"
                   :class="stat.trend >= 0 ? 'positive' : 'negative'"
                   v-if="stat.showGrowth">
                  {{ stat.trend >= 0 ? '+' : '' }}{{ stat.trend }}%
              </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, defineComponent, inject, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import { handleChartsInitialization } from '@/utils/chart-helpers';
import {getRelaxationStatistics, getUserBaseStats, getUserNumOnLevelCode} from "@/api/statistics/userStatistics";
const props = defineProps({
    refreshInterval: {
        type: Number,
        default: 60000
    }
});
// 定义区域类型
interface Region {
    id: string;
    name: string;
}

// 注入事件总线
const regionChangeEvent = inject('regionChangeEvent');

// 当前选择的区域
const currentRegion = ref<Region>({ id: '0', name: 'nation' });

// 定时器引用
const intervalTimer = ref<number | null>(null);

// 定义动画数字组件
const AnimatedNumber = defineComponent({
    name: 'AnimatedNumber',
    props: {
        value: {
            type: Number,
            required: true
        },
        duration: {
            type: Number,
            default: 1000
        }
    },
    data() {
        return {
            displayValue: 0
        }
    },
    watch: {
        value(newValue) {
            this.animateValue(this.displayValue, newValue);
        }
    },
    mounted() {
        this.displayValue = this.value;
    },
    methods: {
        animateValue(start: number, end: number) {
            const startTime = performance.now();
            const updateValue = (timestamp: number) => {
                const runtime = timestamp - startTime;
                const progress = Math.min(runtime / this.duration, 1);

                this.displayValue = Math.floor(start + (end - start) * progress);

                if (runtime < this.duration) {
                    requestAnimationFrame(updateValue);
                }
            };

            requestAnimationFrame(updateValue);
        }
    },
    render() {
        return this.displayValue;
    }
});

// 左侧基础统计数据
const leftStatData = ref([
    {
        title: '系统用户数',
        value: 0,
        unit: '人',
        trend: 0,
        trendData: [0.5, 0.6, 0.4, 0.7, 0.8, 0.9],
        showGrowth: true
    },
    {
        title: '本月访问量',
        value: 0,
        unit: '次',
        trend: 0,
        trendData: [0.3, 0.5, 0.4, 0.7, 0.6, 0.8],
        showGrowth: true
    },
    {
        title: '咨询师总数',
        value: 0,
        unit: '人',
        trend: 0,
        trendData: [0.7, 0.6, 0.8, 0.7, 0.8, 0.9],
        showGrowth: true
    },
    {
        title: '测评完成量',
        value: 0,
        unit: '份',
        trend: 0,
        trendData: [0.8, 0.7, 0.6, 0.5, 0.4, 0.3],
        showGrowth: true
    }
]);

// 右侧统计数据
const rightStatData = ref([
    {
        title: '咨询师占比',
        value: 0,
        unit: '',
        suffix: '%',
        trend: 0,
        trendData: [0.6, 0.7, 0.6, 0.8, 0.9, 0.8],
        showGrowth: true
    },
    {
        title: '人员档案',
        value: 0,
        unit: '份',
        trend: 0,
        trendData: [0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
        showGrowth: false
    },
    {
        title: '普通用户数',
        value: 0,
        unit: '人',
        trend: 0,
        trendData: [0.5, 0.6, 0.7, 0.8, 0.7, 0.9],
        showGrowth: true
    },
    {
        title: '附属机构数量',
        value: 0,
        unit: '个',
        trend: 0,
        trendData: [0.4, 0.5, 0.6, 0.7, 0.8, 0.9],
        showGrowth: true
    }
]);

// 更新统计数据
const updateStatisticsData = (data: any) => {
    // 更新左侧数据
    leftStatData.value[0].value = data.systemUserNum;
    leftStatData.value[0].trend = data.systemUserNumGrowth;

    leftStatData.value[1].value = data.visitsNum;
    leftStatData.value[1].trend = data.visitsNumGrowth;

    leftStatData.value[2].value = data.consultantNum;
    leftStatData.value[2].trend = data.consultantNumGrowth;

    leftStatData.value[3].value = data.scaleNum;
    leftStatData.value[3].trend = data.scaleNumGrowth;

    // 更新右侧数据
    rightStatData.value[0].value = data.consultantOnPercentage;
    rightStatData.value[0].trend = data.consultantOnPercentageGrowth;

    rightStatData.value[1].value = data.userProfileNum;
    rightStatData.value[1].trend = data.userProfileNumGrowth;

    rightStatData.value[2].value = data.userNum;
    rightStatData.value[2].trend = data.userNumGrowth;

    rightStatData.value[3].value = data.childTenantNum;
    rightStatData.value[3].trend = data.childTenantNumGrowth;
};

const initData = async(level: string, levelId: string | number) => {
    try {
        const res = await getUserBaseStats(level, levelId);
        if (res.code === 200) {
            updateStatisticsData(res.data);
        } else {
            console.error('获取用户统计数据失败:', res.msg);
        }
    } catch (error) {
        console.error('请求用户统计数据异常:', error);
    }
};


// 当前层级
const currentLevel = ref('nation');

// 监听区域变化事件
if (regionChangeEvent) {
    regionChangeEvent.on('region-change', (data: any) => {
        // 更新当前区域
        currentRegion.value = data.region;
        currentLevel.value = data.level || 'nation';
        console.log(`基础数据实时看板：接收到区域变化事件，区域: ${currentRegion.value.name}，层级: ${currentLevel.value}`);
        initData(currentLevel.value, currentRegion.value.id);
        stopRefreshTimer()
        startRefreshTimer()
    });
}
let refreshTimer: number | null = null;

// 启动定时刷新
const startRefreshTimer = () => {
    stopRefreshTimer(); // 先停止现有定时器
    if (props.refreshInterval > 0) {
        refreshTimer = window.setInterval(() => {
            initData(currentLevel.value, currentRegion.value.id);
        }, props.refreshInterval);
    }
};
watch(() => props.refreshInterval, (newVal) => {
    if (newVal > 0) {
        startRefreshTimer();
    } else {
        stopRefreshTimer();
    }
});
// 停止定时刷新
const stopRefreshTimer = () => {
    if (refreshTimer) {
        clearInterval(refreshTimer);
        refreshTimer = null;
    }
};
// 组件挂载时初始化数据
onMounted(() => {
    // initData(currentLevel.value, currentRegion.value.id);
    startRefreshTimer();

    /*
    // 初始化左侧数据
    leftStatData.value = [
      {
        title: '系统用户数',
        value: 7143,
        unit: '人',
        trend: 0,
        trendData: [0.5, 0.6, 0.4, 0.7, 0.8, 0.9]
      },
      {
        title: '今日访问量',
        value: 1925,
        unit: '次',
        trend: 0,
        trendData: [0.3, 0.5, 0.4, 0.7, 0.6, 0.8]
      },
      {
        title: '咨询师总数',
        value: 367,
        unit: '人',
        trend: 0,
        trendData: [0.7, 0.6, 0.8, 0.7, 0.8, 0.9]
      },
      {
        title: '测评完成量',
        value: 5435,
        unit: '份',
        trend: 0,
        trendData: [0.8, 0.7, 0.6, 0.5, 0.4, 0.3]
      }
    ];

    // 初始化右侧数据
    rightStatData.value = [
      {
        title: '咨询师占比',
        value: 5,
        unit: '',
        suffix: ':1',
        trend: 3,
        trendData: [0.6, 0.7, 0.6, 0.8, 0.9, 0.8]
      },
      {
        title: '人员档案',
        value: 2414,
        unit: '份',
        trend: 6,
        trendData: [0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
      },
      {
        title: '人员总数',
        value: 6304,
        unit: '人',
        trend: 3,
        trendData: [0.5, 0.6, 0.7, 0.8, 0.7, 0.9]
      },
      {
        title: '附属机构数量',
        value: 984,
        unit: '个',
        trend: 0,
        trendData: [0.4, 0.5, 0.6, 0.7, 0.8, 0.9]
      }
    ];
    */
});

// 清除定时器
onBeforeUnmount(() => {
    stopRefreshTimer()
});
</script>

<style scoped>
.basic-stats {
  width: 100%;
  height: 100%;
  padding: 6px 8px;
  color: #fff;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
  z-index: 2;
  background-image:
    linear-gradient(to right, rgba(78, 205, 196, 0.01) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(78, 205, 196, 0.01) 1px, transparent 1px);
  background-size: 20px 20px;
}

.basic-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 25%;
  height: 1px;
  background: linear-gradient(to right, transparent, rgba(78, 205, 196, 0.5), transparent);
  z-index: 1;
}

.module-title {
  font-size: 15px;
  font-weight: bold;
  margin-bottom: 5px;
  color: #4ECDC4;
  text-shadow: 0 0 8px rgba(78, 205, 196, 0.6);
  text-align: center;
  flex-shrink: 0;
}

.stats-container {
  display: flex;
  flex: 1;
  gap: 8px;
  min-height: 0;
  overflow: hidden;
}

.stats-cards {
  display: flex;
  flex-direction: column;
  gap: 5px;
  min-height: 0;
  overflow: hidden;
}

.left-stats,
.right-stats {
  flex: 1;
  width: 50%;
}

.stat-card {
  background: rgba(16, 35, 75, 0.3);
  border-radius: 6px;
  padding: 3px 6px;
  display: flex;
  flex-direction: column;
  position: relative;
  border: none;
  transition: all 0.3s ease;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  flex: 1;
  height: calc(25% - 4px);
  box-sizing: border-box;
}

.stat-card::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 20px;
  border-top: 1px solid rgba(78, 205, 196, 0.3);
  border-left: 1px solid rgba(78, 205, 196, 0.3);
  border-top-left-radius: 6px;
  opacity: 0.7;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  background: rgba(16, 35, 75, 0.4);
}

.stat-info {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.stat-title {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-bottom: 1px;
  text-align: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 1px;
  display: flex;
  align-items: baseline;
  justify-content: center;
  text-shadow: 0 0 10px rgba(78, 205, 196, 0.4);
}

.stat-unit {
  font-size: 11px;
  margin-left: 2px;
  color: rgba(255, 255, 255, 0.7);
}

.stat-change {
  font-size: 10px;
  text-align: center;
  height: 14px;
  line-height: 14px;
}

.positive {
  color: #7BE495;
}

.negative {
  color: #FF6B6B;
}

.trend-point {
  width: 2px;
  background: #4ECDC4;
  position: absolute;
  bottom: 0;
  border-radius: 1px;
  box-shadow: 0 0 3px rgba(78, 205, 196, 0.5);
}

.trend-point::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -1px;
  width: 4px;
  height: 4px;
  background: #4ECDC4;
  border-radius: 50%;
  box-shadow: 0 0 3px rgba(78, 205, 196, 0.7);
}

@media screen and (max-width: 1600px) {
  .basic-stats {
    padding: 5px 6px;
  }

  .module-title {
    font-size: 14px;
    margin-bottom: 4px;
  }

  .stats-container {
    gap: 6px;
  }

  .stats-cards {
    gap: 4px;
  }

  .stat-card {
    padding: 2px 5px;
  }

  .stat-title {
    font-size: 10px;
  }

  .stat-value {
    font-size: 18px;
  }

  .stat-unit {
    font-size: 10px;
  }

  .stat-change {
    font-size: 9px;
  }
}

@media screen and (max-height: 900px) {
  .basic-stats {
    padding: 4px 5px;
  }

  .module-title {
    margin-bottom: 3px;
  }

  .stats-container,
  .stats-cards {
    gap: 3px;
  }
}
</style>
