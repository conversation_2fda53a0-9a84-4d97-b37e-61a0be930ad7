# AI模块使用说明

本模块基于阿里云DashScope SDK实现，提供了AI对话功能。

## 功能特点

- 文本对话：基于通义千问大语言模型的文本对话功能
- 会话管理：支持多轮对话历史记录保存与管理
- 权限控制：基于系统权限模块，对AI功能进行权限管理

## 配置说明

### 基础配置

在`application.yml`或`application-dev.yml`中添加以下配置：

```yaml
# AI模块配置
ai:
  # 是否启用AI功能
  enabled: true
  # DashScope相关配置
  dashscope:
    # API密钥（推荐通过环境变量配置）
    api-key: your_dashscope_api_key
    # 默认使用的模型
    model: qwen-max
    # 温度参数(0-1之间)
    temperature: 0.7
    # 最大生成令牌数
    max-tokens: 1500
```

### 环境变量配置

出于安全考虑，推荐通过环境变量配置API密钥：

- Windows: `set DASHSCOPE_API_KEY=your_dashscope_api_key`
- Linux/Mac: `export DASHSCOPE_API_KEY=your_dashscope_api_key`

## API接口说明

### 1. 发送消息并获取AI回复

```http
POST /ai/chat/send
```

请求参数：
```json
{
  "conversationId": "会话ID（新会话不传）",
  "message": "用户消息内容",
  "systemPrompt": "系统提示词（可选）",
  "model": "模型名称（可选）",
  "maxTokens": 1500,
  "temperature": 0.7
}
```

### 2. 获取历史消息

```http
GET /ai/chat/history/{conversationId}
```

### 3. 获取会话列表

```http
GET /ai/chat/conversations
```

### 4. 删除会话

```http
POST /ai/chat/delete/{conversationId}
```

### 5. 更新会话标题

```http
POST /ai/chat/update/title/{conversationId}/{title}
```

### 6. 直接调用DashScope API进行文本生成

```http
POST /ai/dashscope/text
```

请求参数同`/ai/chat/send`。

### 7. 多模态对话（图文混合输入）

```http
POST /ai/dashscope/multimodal
```

请求参数：
- `prompt`: 文本描述
- `images`: 图片文件（支持多个）

## 数据库表

AI模块使用以下数据库表：

1. `ai_conversation`: 存储会话信息
2. `ai_message`: 存储消息历史

需要先执行`script/sql/update/ai_tables.sql`创建相关表。

## 常见问题

1. 提示"API密钥未配置"
   - 检查环境变量`DASHSCOPE_API_KEY`或配置文件中的`ai.dashscope.api-key`是否正确设置

2. 接口调用失败
   - 检查网络连接是否正常
   - 确认API密钥是否有效
   - 查看服务器日志获取详细错误信息

## 相关链接

- [DashScope官方文档](https://help.aliyun.com/document_detail/2400395.html)
- [通义千问API文档](https://help.aliyun.com/document_detail/2400243.html) 