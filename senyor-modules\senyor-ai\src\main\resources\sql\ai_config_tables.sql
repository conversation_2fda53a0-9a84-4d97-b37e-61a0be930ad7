-- AI配置相关表结构

-- AI配置表
CREATE TABLE IF NOT EXISTS `ai_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `model` varchar(50) NOT NULL COMMENT 'AI模型名称',
  `temperature` float DEFAULT 0.7 COMMENT '温度参数（0-1）',
  `max_tokens` int DEFAULT 1500 COMMENT '最大令牌数',
  `system_prompt` text COMMENT '系统提示词',
  `knowledge_id` bigint DEFAULT NULL COMMENT '绑定知识库ID',
  `is_default` char(1) DEFAULT '0' COMMENT '是否默认配置（0:否 1:是）',
  `status` char(1) DEFAULT '0' COMMENT '配置状态（0:启用 1:禁用）',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_default` (`is_default`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  CONSTRAINT `fk_config_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge_base` (`knowledge_id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI配置表';

-- AI会话表
CREATE TABLE IF NOT EXISTS `ai_conversation` (
  `conversation_id` varchar(50) NOT NULL COMMENT '会话ID',
  `title` varchar(200) DEFAULT NULL COMMENT '会话标题',
  `model` varchar(50) DEFAULT NULL COMMENT '使用的模型',
  `system_prompt` text COMMENT '系统提示词',
  `message_count` int DEFAULT 0 COMMENT '消息数量',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI会话表';

-- AI消息表
CREATE TABLE IF NOT EXISTS `ai_message` (
  `message_id` bigint NOT NULL AUTO_INCREMENT COMMENT '消息ID',
  `conversation_id` varchar(50) NOT NULL COMMENT '会话ID',
  `role` varchar(20) NOT NULL COMMENT '角色（user:用户 assistant:助手 system:系统）',
  `content` text NOT NULL COMMENT '消息内容',
  `model` varchar(50) DEFAULT NULL COMMENT '使用的模型',
  `tokens` int DEFAULT NULL COMMENT '令牌数',
  `finish_reason` varchar(50) DEFAULT NULL COMMENT '结束原因',
  `like_status` int DEFAULT NULL COMMENT '点赞状态（1:点赞 -1:点踩 0:无）',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`message_id`),
  KEY `idx_conversation_id` (`conversation_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_role` (`role`),
  CONSTRAINT `fk_message_conversation` FOREIGN KEY (`conversation_id`) REFERENCES `ai_conversation` (`conversation_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI消息表';

-- 插入默认AI配置
INSERT INTO `ai_config` (`config_name`, `model`, `temperature`, `max_tokens`, `system_prompt`, `is_default`, `status`, `user_id`, `user_name`, `tenant_id`, `create_by`, `create_time`, `remark`) VALUES
('默认配置', 'deepseek-r1', 0.7, 1500, '你是一个智能助手，请根据用户的问题提供准确、有用的回答。', '1', '0', 1, 'admin', '000000', 1, NOW(), '系统默认AI配置'); 