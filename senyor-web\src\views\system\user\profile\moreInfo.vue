<template>
    <div>
        <template v-if="loading">
            <el-skeleton :rows="5" animated />
        </template>
        <template v-else>
            <el-empty v-if="!fieldsArray.length" description="暂无拓展资料" />

            <el-form v-else label-width="120px">
                <div v-for="(item, index) in fieldsArray" :key="index">
                    <el-form-item
                        v-if="item.status == '1'"
                        :label="item.title + '：'"
                        :rules="[{ required: item.required, message: item.title + '不能为空', trigger: 'blur' }]"
                    >
                        <el-input
                            v-if="item.type === 'input'"
                            v-model="item.realValue"
                            :placeholder="'请输入' + item.title"
                            :clearable="true"
                        />
                        <el-select
                            v-else-if="item.type === 'select'"
                            v-model="item.realValueIndex"
                            :placeholder="'请选择' "
                            :clearable="true"
                        >
                            <el-option
                                v-for="(option, optionIndex) in parseOptions(item.options)"
                                :key="optionIndex"
                                :label="option"
                                :value="optionIndex"
                            />
                        </el-select>
                        <el-radio-group
                            v-else-if="item.type === 'radio'"
                            v-model="item.realValueIndex"
                            :placeholder="'请选择' + item.title"
                        >
                            <el-radio
                                v-for="(option, optionIndex) in parseOptions(item.options)"
                                :key="optionIndex"
                                :label="optionIndex"
                            >
                                {{ option }}
                            </el-radio>
                        </el-radio-group>
                        <el-date-picker
                            v-else-if="item.type === 'date'"
                            v-model="item.realValue"
                            type="date"
                            value-format="YYYY-MM-DD"
                            :placeholder="'请选择' + item.title"
                            :clearable="true"
                        />
                        <el-date-picker
                            v-else-if="item.type === 'dateTime'"
                            v-model="item.realValue"
                            type="datetime"
                            value-format="YYYY-MM-DD"
                            :placeholder="'请选择' + item.title"
                            :clearable="true"
                        />
                    </el-form-item>
                </div>

                <el-form-item v-if="fieldsArray.length">
                    <el-button type="primary" @click="submit">保存</el-button>
                    <el-button type="danger" @click="close">关闭</el-button>
                </el-form-item>
            </el-form>
        </template>
    </div>
</template>

<script setup lang="ts">
import {updateUserByField, updateUserProfile} from '@/api/system/user';
import { propTypes } from '@/utils/propTypes';
import useUserStore from "@/store/modules/user";
import {
    getAllFieldData,
    getAllFieldDataByApi,
    getTemplateFieldDataByUserId,
    updateUserByFieldByApi
} from "@/api/app/template";
import { ref } from 'vue';

const props = defineProps({
    user: propTypes.any.isRequired
});
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const userStore = useUserStore();
const fieldsArray = ref([]);
const loading = ref(true);

const parseOptions = (options: string | any[]) => {
    try {
        if (Array.isArray(options)) {
            return options;
        }
        if (typeof options === 'string') {
            return JSON.parse(options);
        }
        return [];
    } catch (e) {
        console.error('Error parsing options:', e);
        return [];
    }
};

const initData = async() => {
    try {
        loading.value = true;
        // const res = await getAllFieldData(userStore.userId);
        const res = await getAllFieldDataByApi();
        fieldsArray.value = res.data.fieldList || [];

        // 处理每个字段，将realValue转换为索引
        fieldsArray.value.forEach(item => {
            if (item.type === 'radio' || item.type === 'select') {
                const options = parseOptions(item.options);

                // 添加realValueIndex属性用于存储索引
                item.realValueIndex = undefined;

                if (item.realValue !== null && item.realValue !== undefined && item.realValue !== '') {
                    // 尝试将realValue解析为索引
                    const parsedIndex = parseInt(item.realValue);
                    if (!isNaN(parsedIndex) && parsedIndex >= 0 && parsedIndex < options.length) {
                        item.realValueIndex = parsedIndex;
                    } else {
                        // 如果不是索引，尝试在选项中查找值
                        const foundIndex = options.indexOf(item.realValue);
                        if (foundIndex !== -1) {
                            item.realValueIndex = foundIndex;
                        }
                    }
                }
            } else if (item.type === 'date' && item.realValue) {
                // 处理日期格式
                if (!/^\d{4}-\d{2}-\d{2}$/.test(item.realValue)) {
                    const date = new Date(item.realValue);
                    if (!isNaN(date.getTime())) {
                        item.realValue = date.toISOString().split('T')[0];
                    }
                }
            }
        });
    } catch (error) {
        console.error('初始化数据失败:', error);
    } finally {
        loading.value = false;
    }
};

/** 提交按钮 */
const submit = async () => {
    try {
        const fieldList = fieldsArray.value.map(item => {
            let realValue = item.realValue;
            // 处理radio和select类型的值，提交索引
            if (item.type === 'radio' || item.type === 'select') {
                realValue = item.realValueIndex !== -1 ? item.realValueIndex : '';
            }
            // 处理日期类型的值
            else if (item.type === 'date' && realValue) {
                if (realValue instanceof Date) {
                    realValue = realValue.toISOString().split('T')[0];
                }
            }
            return {
                fieldId: item.id,
                realValue: realValue,
                status: item.status,
                fieldFlag: item.fieldFlag
            };
        });
        const param = {
            fieldList: fieldList
        };

        // await updateUserByField(param);
        await updateUserByFieldByApi(param);
        proxy?.$modal.msgSuccess('修改成功');
    } catch (error) {
        proxy?.$modal.msgError('修改失败');
    }
};

/** 关闭按钮 */
const close = () => {
    proxy?.$tab.closePage();
};

onMounted(() => {
    initData();
});
</script>

<style scoped>
.user-profile-container {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.form-row {
    margin-bottom: 18px;
}

.form-item {
    display: flex;
    align-items: center;
}

.field-input,
.el-select,
.el-date-picker {
    width: 240px;
}

.action-buttons {
    margin-top: 24px;
    padding-left: 120px; /* 与label宽度一致 */
}
</style>
