<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.statistics.mapper.UserDataStatisticsMapper">

    <!-- 获取所有机构ID -->
    <select id="getTenantIdsByLevel" resultType="java.lang.String">
        SELECT tenant_id
        FROM sys_tenant
        <where>
            del_flag = 0
            <choose>
                <when test="level == 'province'">
                    AND province = #{levelId}
                </when>
                <when test="level == 'city'">
                    AND city = #{levelId}
                </when>
                <when test="level == 'district'">
                    AND area = #{levelId}
                </when>
            </choose>
        </where>
    </select>

    <!-- 获取用户当前机构地区信息 -->
    <select id="getTenantBaseCode" resultType="org.senyor.statistics.domain.vo.TenantBaseCodeVo">
        SELECT tenant_level as tenantLevel,
               province,
               city,
               area
        FROM sys_tenant
        <where>
            del_flag = 0
            AND tenant_id = #{tenantId}
        </where>
    </select>

    <!-- 获取当前地区级别内的所有租户及其用户数量 -->
    <select id="getUserNumOnLevelCode" resultType="org.senyor.statistics.domain.vo.TenantBaseCodeVo">
        SELECT
        t.tenant_level as tenantLevel,
        t.province,
        t.city,
        t.area,
        COUNT(u.user_id) AS userNum
        FROM
        (SELECT
        tenant_id,
        tenant_level,
        province,
        city,
        area
        FROM
        sys_tenant
        WHERE
        del_flag = 0
        <choose>
            <when test="tenantLevel == 1">
                AND province = #{adCode}
            </when>
            <when test="tenantLevel == 2">
                AND city = #{adCode}
            </when>
            <when test="tenantLevel == 3">
                AND area = #{adCode}
            </when>
        </choose>
        ) AS t
        JOIN
        sys_user AS u
        ON
        u.tenant_id = t.tenant_id
        WHERE
        u.del_flag = 0
        GROUP BY
        <choose>
            <when test="tenantLevel == 0">
                t.province
            </when>
            <when test="tenantLevel == 1">
                t.city
            </when>
            <when test="tenantLevel == 2">
                t.area
            </when>
            <when test="tenantLevel == 3">
                t.tenant_id
            </when>
            <otherwise>
                t.tenant_id
            </otherwise>
        </choose>
    </select>

    <!-- 获取地区内所有机构以及具体信息 -->
    <select id="getTenantsInArea" resultType="org.senyor.statistics.domain.vo.TenantsResultObjVo">
        SELECT
        tenant_id as tenantId,
        company_name as companyName,
        coordinate
        FROM sys_tenant
        <where>
            del_flag = 0
            AND tenant_level = 4
            <choose>
                <when test="adCode != null">
                    AND area = #{adCode}
                </when>
            </choose>
        </where>
    </select>

    <!-- 获取用户总数及上个月数据 -->
    <select id="getUserCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user
        <where>
            del_flag = 0
            AND tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!-- 获取用户总数及上个月数据 -->
    <select id="getHomeUserCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user
        <where>
            del_flag = 0
        </where>
    </select>

    <!-- 获取人员总数上个月数据 -->
    <select id="getStudentUserCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN u.create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user_role ur
        JOIN sys_user u ON ur.user_id = u.user_id
        JOIN sys_role r ON ur.role_id = r.role_id
        <where>
            r.del_flag = 0
            AND r.data_scope = 6
            AND r.role_key != 'visitor'
            AND r.tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!-- 获取访问量上个月数据 -->
    <select id="getLoginCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN l.login_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_logininfor l
        <where>
            l.tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!-- 获取咨询师总数及上个月数据 -->
    <select id="getConsultantCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN u.create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user_role ur
        JOIN sys_user u ON ur.user_id = u.user_id
        JOIN sys_role r ON ur.role_id = r.role_id
        <where>
            u.del_flag = 0
            AND r.del_flag = 0
            AND u.tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            AND r.role_name LIKE '%咨询师%'
        </where>
    </select>


    <!-- 获取咨询师总数及上个月数据 -->
    <select id="getHomeConsultantCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN u.create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user_role ur
        JOIN sys_user u ON ur.user_id = u.user_id
        JOIN sys_role r ON ur.role_id = r.role_id
        <where>
            u.del_flag = 0
            AND r.del_flag = 0
            AND r.role_name LIKE '%咨询师%'
        </where>
    </select>

    <!-- 获取机构总数及上个月数据 -->
    <select id="getTenantCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_tenant
        <where>
            del_flag = 0
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
    </select>

    <!-- 获取完善档案数及上个月数据 -->
    <select id="getProfileCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user
        <where>
            del_flag = 0
            AND profile_flag = 2
            AND tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!-- 获取完善档案数及上个月数据 -->
    <select id="getHomeProfileCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM sys_user
        <where>
            del_flag = 0
            AND profile_flag = 2
        </where>
    </select>

    <!-- 获取测评记录数及上个月数据 -->
    <select id="getScaleCount" resultType="java.util.Map">
        SELECT
        COUNT(*) AS currentTotal,
        COUNT(CASE WHEN create_time &lt; DATE_FORMAT(CURDATE(), '%Y-%m-01') THEN 1 END) AS lastMonthEndTotal
        FROM scale_answer
        <where>
            tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
    </select>

    <!-- 获取性别分布 -->
    <select id="getGenderDistribution" resultType="java.util.Map">
        SELECT
        sex AS gender,
        COUNT(*) AS count
        FROM sys_user
        <where>
            del_flag = 0
            AND tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY sex
    </select>

    <!-- 获取档案统计完成情况 -->
    <select id="getProfileStatus" resultType="java.util.Map">
        SELECT
        profile_flag AS flag,
        COUNT(*) AS count
        FROM sys_user
        <where>
            del_flag = 0
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY profile_flag
    </select>

    <!-- 科普减压统计 -->
    <select id="getRelaxationStatus" resultType="java.util.Map">
        SELECT
        ar.title AS name,
        COUNT(*) AS count
        FROM app_relaxation_raining ar
        INNER JOIN app_page_view apv ON ar.id = apv.resource_id
        AND apv.del_flag = 0
        AND apv.status = 0
        AND apv.resource_flag = #{flagStr}
        <where>
            ar.del_flag = 0
            AND ar.category_type = 0
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND ar.tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    AND apv.tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY ar.title
    </select>

    <!-- 科普减压 统计资源类型发布 -->
    <select id="getRelaxationTypes" resultType="java.util.Map">
        SELECT
        source_type AS type,
        COUNT(*) AS count
        FROM app_relaxation_raining
        <where>
            del_flag = 0
            AND category_type = 1
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY source_type
    </select>

    <!-- 获取用户总数及上个月数据 -->
    <select id="getUserNumOnTenant" resultType="org.senyor.statistics.domain.vo.TenantWarningStatisticsVo">
        SELECT
        t.company_name as tenantName,
        COUNT(DISTINCT u.user_id) AS tenantUserNum,
        COUNT(DISTINCT CASE
        WHEN r.user_id IS NOT NULL AND r.status = 0 AND c.warning = 'Y'
        THEN u.user_id
        END) AS warningNum
        FROM sys_tenant t
        JOIN sys_user u ON t.tenant_id = u.tenant_id
        LEFT JOIN scale_early_warning_record r ON u.user_id = r.user_id
        LEFT JOIN scale_early_warning_level_config c ON r.warning_level_id = c.warning_level_id
        WHERE t.del_flag = 0
        AND t.tenant_id IN
        <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY t.tenant_id, t.company_name
    </select>

    <!-- 心理资讯 每月咨询量统计 -->
    <select id="getConsultationRecordsNumOnMonth" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(CREATE_TIME, '%c') AS month,
        COUNT(*) AS count
        FROM cp_consultation_records
        <where>
            IF_DELETE = 0
            AND CREATE_TIME  &gt;= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH), '%Y-%m-01')
            AND CREATE_TIME &lt;= LAST_DAY(CURRENT_DATE)
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND TENANT_ID IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY DATE_FORMAT(CREATE_TIME, '%Y-%m'), DATE_FORMAT(CREATE_TIME, '%c')
        ORDER BY month
    </select>

    <!-- 心理资讯 预约日期分布统计 -->
    <select id="getConsultationTimeOnMonth" resultType="java.util.Map">
        SELECT
        DATE_FORMAT(CONSULTATION_TIME, '%c') AS month,
        COUNT(*) AS count
        FROM cp_consultation_records
        <where>
            IF_DELETE = 0
            AND CONSULTATION_TIME  &gt;= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH), '%Y-%m-01')
            AND CONSULTATION_TIME &lt;= LAST_DAY(CURRENT_DATE)
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND TENANT_ID IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY DATE_FORMAT(CONSULTATION_TIME, '%Y-%m'), DATE_FORMAT(CONSULTATION_TIME, '%c')
        ORDER BY month
    </select>

    <!--  心理资讯 问题类型统计 -->
    <select id="getConsultationRecordsNumOnQuestionType" resultType="java.lang.String">
        SELECT
        QUESTION_TYPE_CODE as code
        FROM cp_consultation_records
        <where>
            IF_DELETE = 0
            AND YEAR(CREATE_TIME) = YEAR(CURDATE())
            AND MONTH(CREATE_TIME) &lt; MONTH(CURDATE())
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND TENANT_ID IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
    </select>

    <!-- 测评活动 测评趋势统计 -->
    <select id="getAssesPlanByType" resultType="org.senyor.statistics.domain.vo.ScaleWarningResultVo">
        SELECT
        assess_type,
        COUNT(*) AS count,
        DATE_FORMAT(create_time, '%c') AS month
        FROM scale_assess_plan
        <where>
            status = 1
            AND create_time &gt;= DATE_FORMAT(DATE_SUB(CURRENT_DATE, INTERVAL 5 MONTH), '%Y-%m-01')
            AND create_time &lt;= LAST_DAY(CURRENT_DATE)
            <choose>
                <when test="tenantIds != null and tenantIds.size() > 0">
                    AND tenant_id IN
                    <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </when>
                <otherwise>
                    AND 1 = 0
                </otherwise>
            </choose>
        </where>
        GROUP BY assess_type,
        DATE_FORMAT(create_time, '%Y-%m'),
        DATE_FORMAT(create_time, '%c')
        ORDER BY
        assess_type,
        DATE_FORMAT(create_time, '%Y-%m');
    </select>

    <!-- 测评活动 预警用户统计 -->
    <select id="getWarningUserCountByLevel" resultType="java.util.Map">
        SELECT
        max_level AS type,
        COUNT(DISTINCT user_id) AS count
        FROM (
        SELECT
        user_id,
        MAX(warning_level_id) AS max_level
        FROM risk_warning_record
        <where>
            tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY user_id
        ) AS user_max_level
        GROUP BY max_level
    </select>


    <!-- 测评活动 干预统计 -->
    <select id="getWarningUserByStatus" resultType="java.util.Map">
        SELECT
        status  AS type,
        COUNT(*) AS count
        FROM risk_warning_record
        <where>
            tenant_id IN
            <foreach collection="tenantIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        GROUP BY status
    </select>



</mapper>
