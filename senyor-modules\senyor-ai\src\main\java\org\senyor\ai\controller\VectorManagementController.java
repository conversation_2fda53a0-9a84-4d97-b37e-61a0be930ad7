package org.senyor.ai.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.domain.AiKnowledgeChunk;
import org.senyor.ai.mapper.AiKnowledgeChunkMapper;
import org.senyor.ai.service.IEmbeddingService;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.common.core.domain.R;
import org.senyor.common.log.annotation.Log;
import org.senyor.common.log.enums.BusinessType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 向量化管理控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/vector")
@Tag(name = "向量化管理", description = "向量化管理接口")
@Slf4j
public class VectorManagementController {

    private final AiKnowledgeChunkMapper aiKnowledgeChunkMapper;
    private final IEmbeddingService embeddingService;
    private final IVectorStoreService vectorStoreService;
    private final ObjectMapper objectMapper;

    /**
     * 获取向量化统计信息
     */
    @Operation(summary = "获取向量化统计信息")
    @GetMapping("/stats")
    public R<Map<String, Object>> getVectorStats() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 统计各状态的分块数量
            stats.put("totalChunks", aiKnowledgeChunkMapper.selectCount(null));
            stats.put("pendingChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>().eq(AiKnowledgeChunk::getVectorStatus, "0")
            ));
            stats.put("processingChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>().eq(AiKnowledgeChunk::getVectorStatus, "1")
            ));
            stats.put("completedChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>().eq(AiKnowledgeChunk::getVectorStatus, "2")
            ));
            stats.put("failedChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>().eq(AiKnowledgeChunk::getVectorStatus, "3")
            ));

            // 向量维度信息
            stats.put("vectorDimension", embeddingService.getVectorDimension());

            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取向量化统计信息失败", e);
            return R.fail("获取向量化统计信息失败");
        }
    }

    /**
     * 获取知识库的向量化状态
     */
    @Operation(summary = "获取知识库的向量化状态")
    @GetMapping("/knowledge/{knowledgeId}/status")
    public R<Map<String, Object>> getKnowledgeVectorStatus(
        @Parameter(description = "知识库ID") @PathVariable Long knowledgeId
    ) {
        try {
            Map<String, Object> status = new HashMap<>();

            // 统计该知识库各状态的分块数量
            status.put("totalChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>().eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
            ));
            status.put("pendingChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "0")
            ));
            status.put("processingChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "1")
            ));
            status.put("completedChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "2")
            ));
            status.put("failedChunks", aiKnowledgeChunkMapper.selectCount(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "3")
            ));

            return R.ok(status);
        } catch (Exception e) {
            log.error("获取知识库向量化状态失败，knowledgeId: {}", knowledgeId, e);
            return R.fail("获取知识库向量化状态失败");
        }
    }

    /**
     * 手动触发知识库向量化
     */
    @Operation(summary = "手动触发知识库向量化")
    @PostMapping("/knowledge/{knowledgeId}/vectorize")
    @Log(title = "手动触发知识库向量化", businessType = BusinessType.UPDATE)
    public R<String> triggerKnowledgeVectorization(
        @Parameter(description = "知识库ID") @PathVariable Long knowledgeId
    ) {
        try {
            // 查找该知识库中未向量化的分块
            List<AiKnowledgeChunk> pendingChunks = aiKnowledgeChunkMapper.selectList(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "0")
            );

            if (pendingChunks.isEmpty()) {
                return R.ok("该知识库没有待向量化的分块");
            }

            // 异步处理向量化
            processVectorizationAsync(pendingChunks);

            return R.ok("已触发向量化处理，共 " + pendingChunks.size() + " 个分块");
        } catch (Exception e) {
            log.error("触发知识库向量化失败，knowledgeId: {}", knowledgeId, e);
            return R.fail("触发知识库向量化失败");
        }
    }

    /**
     * 重新向量化失败的分块
     */
    @Operation(summary = "重新向量化失败的分块")
    @PostMapping("/knowledge/{knowledgeId}/retry")
    @Log(title = "重新向量化失败的分块", businessType = BusinessType.UPDATE)
    public R<String> retryFailedVectorization(
        @Parameter(description = "知识库ID") @PathVariable Long knowledgeId
    ) {
        try {
            // 查找该知识库中向量化失败的分块
            List<AiKnowledgeChunk> failedChunks = aiKnowledgeChunkMapper.selectList(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
                    .eq(AiKnowledgeChunk::getVectorStatus, "3")
            );

            if (failedChunks.isEmpty()) {
                return R.ok("该知识库没有向量化失败的分块");
            }

            // 重置状态为待向量化
            for (AiKnowledgeChunk chunk : failedChunks) {
                chunk.setVectorStatus("0");
                aiKnowledgeChunkMapper.updateById(chunk);
            }

            // 异步处理向量化
            processVectorizationAsync(failedChunks);

            return R.ok("已重新触发向量化处理，共 " + failedChunks.size() + " 个分块");
        } catch (Exception e) {
            log.error("重新向量化失败，knowledgeId: {}", knowledgeId, e);
            return R.fail("重新向量化失败");
        }
    }

    /**
     * 删除知识库的所有向量
     */
    @Operation(summary = "删除知识库的所有向量")
    @DeleteMapping("/knowledge/{knowledgeId}")
    @Log(title = "删除知识库的所有向量", businessType = BusinessType.DELETE)
    public R<String> deleteKnowledgeVectors(
        @Parameter(description = "知识库ID") @PathVariable Long knowledgeId
    ) {
        try {
            // 删除Elasticsearch中的向量
            int deletedCount = vectorStoreService.deleteVectorsByKnowledgeId("ai_knowledge_vectors", knowledgeId);

            // 重置数据库中的向量状态
            aiKnowledgeChunkMapper.update(null, new LambdaUpdateWrapper<AiKnowledgeChunk>()
                .set(AiKnowledgeChunk::getVectorStatus, "0")
                .set(AiKnowledgeChunk::getEmbedding, null)
                .eq(AiKnowledgeChunk::getKnowledgeId, knowledgeId)
            );

            return R.ok("成功删除知识库向量，共 " + deletedCount + " 个向量");
        } catch (Exception e) {
            log.error("删除知识库向量失败，knowledgeId: {}", knowledgeId, e);
            return R.fail("删除知识库向量失败");
        }
    }

    /**
     * 异步处理向量化
     */
    private void processVectorizationAsync(List<AiKnowledgeChunk> chunks) {
        Thread vectorizationThread = new Thread(() -> {
            try {
                log.info("开始异步向量化处理，分块数量: {}", chunks.size());

                // 更新状态为向量化中
                for (AiKnowledgeChunk chunk : chunks) {
                    chunk.setVectorStatus("1");
                    aiKnowledgeChunkMapper.updateById(chunk);
                }

                // 批量向量化
                List<String> contents = new ArrayList<>();
                for (AiKnowledgeChunk chunk : chunks) {
                    contents.add(chunk.getContent());
                }

                List<List<Float>> embeddings = embeddingService.getEmbeddings(contents);

                // 准备向量存储数据
                List<IVectorStoreService.VectorData> vectorDataList = new ArrayList<>();
                String indexName = "ai_knowledge_vectors";

                for (int i = 0; i < chunks.size(); i++) {
                    AiKnowledgeChunk chunk = chunks.get(i);
                    List<Float> embedding = embeddings.get(i);

                    // 更新数据库中的向量状态和向量数据
                    chunk.setVectorStatus("2"); // 已完成
                    chunk.setEmbedding(convertEmbeddingToString(embedding));
                    aiKnowledgeChunkMapper.updateById(chunk);

                    // 准备向量存储数据
                    Map<String, Object> metadata = new HashMap<>();
                    metadata.put("documentId", chunk.getDocumentId());
                    metadata.put("knowledgeId", chunk.getKnowledgeId());
                    metadata.put("chunkIndex", chunk.getChunkIndex());
                    metadata.put("chunkSize", chunk.getChunkSize());

                    String metadataJson = objectMapper.writeValueAsString(metadata);

                    IVectorStoreService.VectorData vectorData = new IVectorStoreService.VectorData(
                        chunk.getChunkId(), chunk.getContent(), embedding, metadataJson
                    );
                    vectorDataList.add(vectorData);
                }

                // 批量存储向量到Elasticsearch
                int successCount = vectorStoreService.batchStoreVectors(indexName, vectorDataList);
                log.info("向量化处理完成，成功存储向量: {}/{}", successCount, chunks.size());

            } catch (Exception e) {
                log.error("异步向量化处理失败", e);
                // 更新失败状态
                for (AiKnowledgeChunk chunk : chunks) {
                    chunk.setVectorStatus("3"); // 失败
                    aiKnowledgeChunkMapper.updateById(chunk);
                }
            }
        });

        vectorizationThread.setName("ManualVectorizationThread-" + System.currentTimeMillis());
        vectorizationThread.start();
    }

    /**
     * 将向量转换为字符串存储
     */
    private String convertEmbeddingToString(List<Float> embedding) {
        try {
            return objectMapper.writeValueAsString(embedding);
        } catch (Exception e) {
            log.error("向量转字符串失败", e);
            return "[]";
        }
    }
}
