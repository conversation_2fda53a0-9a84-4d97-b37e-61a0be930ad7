package org.senyor.ai.service;

import java.util.List;

/**
 * 知识库检索服务接口
 * 用于实现RAG（检索增强生成）功能
 *
 * <AUTHOR>
 */
public interface IKnowledgeRetrievalService {

    /**
     * 根据用户查询检索相关知识
     *
     * @param query 用户查询
     * @param knowledgeId 知识库ID（可选，如果为null则检索所有知识库）
     * @param topK 返回结果数量
     * @return 相关知识片段列表
     */
    List<KnowledgeChunk> retrieveKnowledge(String query, Long knowledgeId, int topK);

    /**
     * 根据用户查询构建增强的系统提示
     *
     * @param query 用户查询
     * @param knowledgeId 知识库ID（可选）
     * @param originalPrompt 原始系统提示
     * @return 增强后的系统提示
     */
    String buildEnhancedPrompt(String query, Long knowledgeId, String originalPrompt);

    /**
     * 知识片段类
     */
    class KnowledgeChunk {
        private String content;
        private String source;
        private Double score;
        private Long chunkId;
        private Long documentId;
        private Long knowledgeId;

        public KnowledgeChunk() {}

        public KnowledgeChunk(String content, String source, Double score) {
            this.content = content;
            this.source = source;
            this.score = score;
        }

        // Getters and Setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public String getSource() { return source; }
        public void setSource(String source) { this.source = source; }

        public Double getScore() { return score; }
        public void setScore(Double score) { this.score = score; }

        public Long getChunkId() { return chunkId; }
        public void setChunkId(Long chunkId) { this.chunkId = chunkId; }

        public Long getDocumentId() { return documentId; }
        public void setDocumentId(Long documentId) { this.documentId = documentId; }

        public Long getKnowledgeId() { return knowledgeId; }
        public void setKnowledgeId(Long knowledgeId) { this.knowledgeId = knowledgeId; }
    }
} 