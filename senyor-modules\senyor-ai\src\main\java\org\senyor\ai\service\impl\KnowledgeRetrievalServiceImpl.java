package org.senyor.ai.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.domain.AiKnowledgeChunk;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.mapper.AiKnowledgeChunkMapper;
import org.senyor.ai.mapper.AiKnowledgeDocumentMapper;
import org.senyor.ai.service.IEmbeddingService;
import org.senyor.ai.service.IKnowledgeRetrievalService;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.HashMap;
import java.util.stream.Collectors;
import java.util.Arrays;
import java.util.regex.Pattern;

/**
 * 知识库检索服务实现类
 * 实现RAG（检索增强生成）功能
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class KnowledgeRetrievalServiceImpl implements IKnowledgeRetrievalService {

    private final AiKnowledgeChunkMapper aiKnowledgeChunkMapper;
    private final AiKnowledgeDocumentMapper aiKnowledgeDocumentMapper;
    private final IEmbeddingService embeddingService;
    private final IVectorStoreService vectorStoreService;

    // 向量索引名称
    private static final String VECTOR_INDEX_NAME = "ai_knowledge_vectors";
    // 最小相似度分数（进一步降低阈值，提高检索覆盖率）
    private static final double MIN_SIMILARITY_SCORE = 0.3;

    // 检索策略配置
    private static final int MAX_RETRIEVAL_ATTEMPTS = 3;  // 最大检索尝试次数
    private static final double[] SIMILARITY_THRESHOLDS = {0.6, 0.4, 0.3};  // 多级相似度阈值
    private static final int[] TOP_K_VALUES = {10, 20, 30};  // 多级返回数量

    @Override
    public List<KnowledgeChunk> retrieveKnowledge(String query, Long knowledgeId, int topK) {
        try {
            log.info("开始检索知识库，查询: {}, 知识库ID: {}, 返回数量: {}", query, knowledgeId, topK);

            // 多策略检索：向量化检索 + 关键词检索 + 模糊匹配
            List<KnowledgeChunk> allResults = new ArrayList<>();

            // 1. 向量化检索（多级阈值）
            List<KnowledgeChunk> vectorResults = retrieveKnowledgeByVectorMultiLevel(query, knowledgeId, topK);
            allResults.addAll(vectorResults);

            // 2. 关键词检索
            List<KnowledgeChunk> keywordResults = retrieveKnowledgeByKeywords(query, knowledgeId, topK);
            allResults.addAll(keywordResults);

            // 3. 模糊匹配检索
            List<KnowledgeChunk> fuzzyResults = retrieveKnowledgeByFuzzyMatch(query, knowledgeId, topK);
            allResults.addAll(fuzzyResults);

            // 4. 去重和排序
            List<KnowledgeChunk> finalResults = deduplicateAndSortResults(query, allResults, topK);

            log.info("多策略检索完成，向量化: {}, 关键词: {}, 模糊匹配: {}, 最终结果: {}",
                vectorResults.size(), keywordResults.size(), fuzzyResults.size(), finalResults.size());

            return finalResults;

        } catch (Exception e) {
            log.error("知识库检索失败，回退到关键词检索", e);
            return retrieveKnowledgeByKeywords(query, knowledgeId, topK);
        }
    }

    /**
     * 使用向量化检索知识（多级阈值策略）
     */
    private List<KnowledgeChunk> retrieveKnowledgeByVectorMultiLevel(String query, Long knowledgeId, int topK) {
        List<KnowledgeChunk> allResults = new ArrayList<>();

        try {
            // 优化查询：如果查询包含文档名称相关词汇，增强查询
            String enhancedQuery = enhanceQueryForDocumentName(query);

            // 将查询文本向量化
            List<Float> queryEmbedding = embeddingService.getEmbedding(enhancedQuery);

            // 多级阈值检索
            for (int i = 0; i < SIMILARITY_THRESHOLDS.length && allResults.size() < topK; i++) {
                double threshold = SIMILARITY_THRESHOLDS[i];
                int currentTopK = Math.min(TOP_K_VALUES[i], topK - allResults.size());

                List<KnowledgeChunk> results = vectorStoreService.searchSimilar(
                    VECTOR_INDEX_NAME, queryEmbedding, currentTopK, threshold
                );

                // 如果指定了知识库ID，过滤结果
                if (knowledgeId != null) {
                    results = results.stream()
                        .filter(chunk -> knowledgeId.equals(chunk.getKnowledgeId()))
                        .collect(Collectors.toList());
                }

                // 去重添加结果
                for (KnowledgeChunk result : results) {
                    if (!containsChunk(allResults, result)) {
                        allResults.add(result);
                    }
                }

                log.debug("向量化检索第{}级完成，阈值: {}, 找到: {} 个结果", i + 1, threshold, results.size());
            }

            // 对结果进行重新排序，优先考虑文档名称匹配
            allResults = reorderResultsByDocumentName(query, allResults);

            log.debug("多级向量化检索完成，总共找到 {} 个结果", allResults.size());
            return allResults;

        } catch (Exception e) {
            log.error("多级向量化检索失败", e);
            return allResults;
        }
    }

    /**
     * 检查结果列表中是否已包含指定分块
     */
    private boolean containsChunk(List<KnowledgeChunk> results, KnowledgeChunk chunk) {
        return results.stream().anyMatch(r -> r.getChunkId().equals(chunk.getChunkId()));
    }

    /**
     * 增强查询以更好地匹配文档名称
     *
     * @param query 原始查询
     * @return 增强后的查询
     */
    private String enhanceQueryForDocumentName(String query) {
        if (query == null || query.trim().isEmpty()) {
            return query;
        }

        // 如果查询看起来像文档名称，添加相关上下文
        String enhancedQuery = query;

        // 检查是否包含文档名称相关的关键词
        String lowerQuery = query.toLowerCase();
        if (lowerQuery.contains("文档") || lowerQuery.contains("文件") ||
            lowerQuery.contains("doc") || lowerQuery.contains("pdf") ||
            lowerQuery.contains("word") || lowerQuery.contains("excel")) {
            enhancedQuery = "文档名称：" + query + "\n" + query;
        }

        log.debug("查询增强 - 原始查询: {}, 增强查询: {}", query, enhancedQuery);
        return enhancedQuery;
    }

    /**
     * 根据文档名称匹配度重新排序结果
     *
     * @param query 查询文本
     * @param results 检索结果
     * @return 重新排序后的结果
     */
    private List<KnowledgeChunk> reorderResultsByDocumentName(String query, List<KnowledgeChunk> results) {
        if (query == null || query.trim().isEmpty() || results.isEmpty()) {
            return results;
        }

        // 计算每个结果的文档名称匹配分数
        List<KnowledgeChunk> scoredResults = results.stream()
            .map(chunk -> {
                double documentNameScore = calculateDocumentNameScore(query, chunk.getSource());
                // 将文档名称分数与原有分数结合
                double combinedScore = chunk.getScore() != null ?
                    (chunk.getScore() * 0.7 + documentNameScore * 0.3) : documentNameScore;

                KnowledgeChunk scoredChunk = new KnowledgeChunk();
                scoredChunk.setContent(chunk.getContent());
                scoredChunk.setScore(combinedScore);
                scoredChunk.setChunkId(chunk.getChunkId());
                scoredChunk.setDocumentId(chunk.getDocumentId());
                scoredChunk.setKnowledgeId(chunk.getKnowledgeId());
                scoredChunk.setSource(chunk.getSource());

                return scoredChunk;
            })
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .collect(Collectors.toList());

        log.debug("结果重新排序完成，查询: {}, 结果数量: {}", query, scoredResults.size());
        return scoredResults;
    }

    /**
     * 计算文档名称匹配分数
     *
     * @param query 查询文本
     * @param documentName 文档名称
     * @return 匹配分数
     */
    private double calculateDocumentNameScore(String query, String documentName) {
        if (query == null || documentName == null) {
            return 0.0;
        }

        String lowerQuery = query.toLowerCase();
        String lowerDocumentName = documentName.toLowerCase();

        // 完全匹配
        if (lowerQuery.equals(lowerDocumentName)) {
            return 1.0;
        }

        // 包含匹配
        if (lowerDocumentName.contains(lowerQuery) || lowerQuery.contains(lowerDocumentName)) {
            return 0.8;
        }

        // 部分词汇匹配
        String[] queryWords = lowerQuery.split("\\s+");
        String[] documentWords = lowerDocumentName.split("\\s+");

        int matchCount = 0;
        for (String queryWord : queryWords) {
            for (String documentWord : documentWords) {
                if (documentWord.contains(queryWord) || queryWord.contains(documentWord)) {
                    matchCount++;
                    break;
                }
            }
        }

        if (matchCount > 0) {
            return 0.5 * (double) matchCount / Math.max(queryWords.length, documentWords.length);
        }

        return 0.0;
    }

    /**
     * 计算关键词匹配分数
     */
    private double calculateKeywordMatchScore(String query, String content, String[] keywords) {
        if (query == null || content == null || keywords == null) {
            return 0.0;
        }

        String lowerContent = content.toLowerCase();
        String lowerQuery = query.toLowerCase();

        double score = 0.0;
        int totalKeywords = keywords.length;
        int matchedKeywords = 0;

        for (String keyword : keywords) {
            if (lowerContent.contains(keyword.toLowerCase())) {
                matchedKeywords++;
                // 关键词出现次数越多，分数越高
                int occurrences = countOccurrences(lowerContent, keyword.toLowerCase());
                score += Math.min(occurrences * 0.1, 0.5); // 每个关键词最多贡献0.5分
            }
        }

        // 基础分数：匹配关键词比例
        double baseScore = (double) matchedKeywords / totalKeywords;

        // 查询完整匹配加分
        if (lowerContent.contains(lowerQuery)) {
            score += 0.3;
        }

        return Math.min(baseScore + score, 1.0);
    }

    /**
     * 计算模糊匹配分数
     */
    private double calculateFuzzyMatchScore(String query, String content) {
        if (query == null || content == null) {
            return 0.0;
        }

        String lowerQuery = query.toLowerCase();
        String lowerContent = content.toLowerCase();

        // 计算编辑距离相似度
        double editDistanceScore = calculateEditDistanceSimilarity(lowerQuery, lowerContent);

        // 计算词汇重叠度
        double vocabularyOverlapScore = calculateVocabularyOverlap(lowerQuery, lowerContent);

        // 计算子字符串匹配
        double substringScore = calculateSubstringMatch(lowerQuery, lowerContent);

        // 综合分数
        return (editDistanceScore * 0.3 + vocabularyOverlapScore * 0.4 + substringScore * 0.3);
    }

    /**
     * 去重和排序结果
     */
    private List<KnowledgeChunk> deduplicateAndSortResults(String query, List<KnowledgeChunk> allResults, int topK) {
        if (allResults == null || allResults.isEmpty()) {
            return new ArrayList<>();
        }

        // 去重：按chunkId去重
        Map<Long, KnowledgeChunk> uniqueResults = new HashMap<>();
        for (KnowledgeChunk chunk : allResults) {
            if (chunk.getChunkId() != null) {
                uniqueResults.put(chunk.getChunkId(), chunk);
            }
        }

        // 重新计算综合分数
        List<KnowledgeChunk> finalResults = uniqueResults.values().stream()
            .map(chunk -> {
                // 重新计算综合分数
                double finalScore = recalculateFinalScore(query, chunk);
                chunk.setScore(finalScore);
                return chunk;
            })
            .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
            .limit(topK)
            .collect(Collectors.toList());

        return finalResults;
    }

    /**
     * 重新计算最终分数
     */
    private double recalculateFinalScore(String query, KnowledgeChunk chunk) {
        double baseScore = chunk.getScore() != null ? chunk.getScore() : 0.0;

        // 文档名称匹配加分
        if (chunk.getSource() != null) {
            double documentNameScore = calculateDocumentNameScore(query, chunk.getSource());
            baseScore = baseScore * 0.7 + documentNameScore * 0.3;
        }

        // 内容长度加分（适中的长度更好）
        int contentLength = chunk.getContent() != null ? chunk.getContent().length() : 0;
        if (contentLength > 50 && contentLength < 500) {
            baseScore += 0.1;
        }

        return Math.min(baseScore, 1.0);
    }

    /**
     * 计算字符串出现次数
     */
    private int countOccurrences(String text, String target) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(target, index)) != -1) {
            count++;
            index += target.length();
        }
        return count;
    }

    /**
     * 计算编辑距离相似度
     */
    private double calculateEditDistanceSimilarity(String s1, String s2) {
        int distance = calculateEditDistance(s1, s2);
        int maxLength = Math.max(s1.length(), s2.length());
        return maxLength == 0 ? 1.0 : 1.0 - (double) distance / maxLength;
    }

    /**
     * 计算编辑距离
     */
    private int calculateEditDistance(String s1, String s2) {
        int[][] dp = new int[s1.length() + 1][s2.length() + 1];

        for (int i = 0; i <= s1.length(); i++) {
            dp[i][0] = i;
        }
        for (int j = 0; j <= s2.length(); j++) {
            dp[0][j] = j;
        }

        for (int i = 1; i <= s1.length(); i++) {
            for (int j = 1; j <= s2.length(); j++) {
                if (s1.charAt(i - 1) == s2.charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1];
                } else {
                    dp[i][j] = Math.min(dp[i - 1][j - 1] + 1,
                                      Math.min(dp[i - 1][j] + 1, dp[i][j - 1] + 1));
                }
            }
        }

        return dp[s1.length()][s2.length()];
    }

    /**
     * 计算词汇重叠度
     */
    private double calculateVocabularyOverlap(String s1, String s2) {
        Set<String> words1 = new HashSet<>(Arrays.asList(s1.split("\\s+")));
        Set<String> words2 = new HashSet<>(Arrays.asList(s2.split("\\s+")));

        if (words1.isEmpty() && words2.isEmpty()) {
            return 1.0;
        }

        Set<String> intersection = new HashSet<>(words1);
        intersection.retainAll(words2);

        Set<String> union = new HashSet<>(words1);
        union.addAll(words2);

        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }

    /**
     * 计算子字符串匹配分数
     */
    private double calculateSubstringMatch(String query, String content) {
        if (query.length() < 2) {
            return 0.0;
        }

        int matchCount = 0;
        int totalSubstrings = 0;

        // 检查不同长度的子字符串
        for (int len = 2; len <= Math.min(query.length(), 5); len++) {
            for (int i = 0; i <= query.length() - len; i++) {
                String substring = query.substring(i, i + len);
                totalSubstrings++;
                if (content.contains(substring)) {
                    matchCount++;
                }
            }
        }

        return totalSubstrings == 0 ? 0.0 : (double) matchCount / totalSubstrings;
    }

    /**
     * 使用关键词检索知识（增强版）
     */
    private List<KnowledgeChunk> retrieveKnowledgeByKeywords(String query, Long knowledgeId, int topK) {
        try {
            // 增强分词：支持更多分隔符和中文分词
            String[] keywords = Arrays.stream(query.split("[\s,，。！？!?.;；、]+"))
                .filter(s -> s != null && !s.trim().isEmpty())
                .distinct()
                .toArray(String[]::new);

            // 构建查询条件
            List<AiKnowledgeChunk> chunks;
            if (knowledgeId != null) {
                // 检索指定知识库
                chunks = aiKnowledgeChunkMapper.selectRelevantChunksWithKeywords(knowledgeId, query, Arrays.asList(keywords), topK);
            } else {
                // 检索所有知识库
                chunks = aiKnowledgeChunkMapper.selectRelevantChunksFromAllWithKeywords(query, Arrays.asList(keywords), topK);
            }

            // 转换为KnowledgeChunk对象并计算相关性分数
            List<KnowledgeChunk> results = chunks.stream()
                .map(chunk -> {
                    KnowledgeChunk knowledgeChunk = convertToKnowledgeChunk(chunk);
                    // 计算关键词匹配分数
                    double keywordScore = calculateKeywordMatchScore(query, chunk.getContent(), keywords);
                    knowledgeChunk.setScore(keywordScore);
                    return knowledgeChunk;
                })
                .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                .collect(Collectors.toList());

            log.info("关键词检索完成，找到 {} 个相关片段", results.size());
            return results;

        } catch (Exception e) {
            log.error("关键词检索失败", e);
            return new ArrayList<>();
        }
    }

        /**
     * 模糊匹配检索
     */
    private List<KnowledgeChunk> retrieveKnowledgeByFuzzyMatch(String query, Long knowledgeId, int topK) {
        try {
            // 生成关键词列表
            List<String> keywords = generateKeywords(query);
            
            // 调试日志
            log.debug("模糊匹配检索 - 查询: '{}', 知识库ID: {}, 关键词数量: {}, 关键词: {}", 
                query, knowledgeId, keywords.size(), keywords);
            
            // 如果关键词为空，直接返回空结果
            if (keywords.isEmpty()) {
                log.warn("模糊匹配检索 - 关键词列表为空，跳过检索");
                return new ArrayList<>();
            }
            
            // 构建模糊匹配查询条件
            List<AiKnowledgeChunk> chunks;
            if (knowledgeId != null) {
                // 检索指定知识库的模糊匹配
                chunks = aiKnowledgeChunkMapper.selectChunksByFuzzyMatch(knowledgeId, query, keywords, topK);
            } else {
                // 检索所有知识库的模糊匹配
                chunks = aiKnowledgeChunkMapper.selectAllChunksByFuzzyMatch(query, keywords, topK);
            }

            // 转换为KnowledgeChunk对象并计算模糊匹配分数
            List<KnowledgeChunk> results = chunks.stream()
                .map(chunk -> {
                    KnowledgeChunk knowledgeChunk = convertToKnowledgeChunk(chunk);
                    // 安全计算模糊匹配分数
                    String content = chunk.getContent();
                    if (content != null) {
                        double fuzzyScore = calculateFuzzyMatchScore(query, content);
                        knowledgeChunk.setScore(fuzzyScore);
                    } else {
                        log.warn("知识块 {} 的内容为null，跳过模糊匹配分数计算", chunk.getChunkId());
                        knowledgeChunk.setScore(0.0);
                    }
                    return knowledgeChunk;
                })
                .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
                .collect(Collectors.toList());

            log.info("模糊匹配检索完成，找到 {} 个相关片段", results.size());
            return results;

        } catch (Exception e) {
            log.error("模糊匹配检索失败 - 查询: '{}', 知识库ID: {}", query, knowledgeId, e);
            return new ArrayList<>();
        }
    }

    /**
     * 生成关键词列表
     */
    private List<String> generateKeywords(String query) {
        if (query == null || query.trim().isEmpty()) {
            return new ArrayList<>();
        }

        Set<String> keywords = new HashSet<>();

        // 1. 添加完整查询（过滤掉太短的查询）
        if (query.trim().length() >= 2) {
            keywords.add(query.trim());
        }

        // 2. 按空格分词（英文）
        String[] spaceWords = query.split("\\s+");
        for (String word : spaceWords) {
            String trimmedWord = word.trim();
            if (trimmedWord.length() >= 2 && !trimmedWord.isEmpty()) {
                keywords.add(trimmedWord);
            }
        }

        // 3. 按中文标点分词
        String[] chineseWords = query.split("[，。！？!?.;；、]+");
        for (String word : chineseWords) {
            String trimmedWord = word.trim();
            if (trimmedWord.length() >= 2 && !trimmedWord.isEmpty()) {
                keywords.add(trimmedWord);
            }
        }

        // 4. 单字符（仅保留有意义的字符，且长度至少为1）
        for (char c : query.toCharArray()) {
            if (Character.isLetterOrDigit(c) && c != ' ' && c != '\t' && c != '\n') {
                keywords.add(String.valueOf(c));
            }
        }

        // 过滤并返回结果
        List<String> result = keywords.stream()
            .filter(k -> k != null && !k.trim().isEmpty() && k.trim().length() >= 1)
            .distinct()
            .collect(Collectors.toList());

        log.debug("生成关键词 - 原始查询: '{}', 生成关键词数量: {}", query, result.size());
        return result;
    }

    @Override
    public String buildEnhancedPrompt(String query, Long knowledgeId, String originalPrompt) {
        try {
            // 检索相关知识
            List<KnowledgeChunk> chunks = retrieveKnowledge(query, knowledgeId, 5);

            if (chunks.isEmpty()) {
                log.info("未找到相关知识，使用原始提示");
                return originalPrompt;
            }

            // 构建知识上下文
            StringBuilder contextBuilder = new StringBuilder();
            contextBuilder.append("基于以下知识库信息回答问题：\n\n");

            // 按文档名称分组，优先显示文档名称匹配度高的内容
            Map<String, List<KnowledgeChunk>> documentGroups = chunks.stream()
                .collect(Collectors.groupingBy(chunk ->
                    chunk.getSource() != null ? chunk.getSource() : "未知文档"));

            int fragmentIndex = 1;
            for (Map.Entry<String, List<KnowledgeChunk>> entry : documentGroups.entrySet()) {
                String documentName = entry.getKey();
                List<KnowledgeChunk> documentChunks = entry.getValue();

                contextBuilder.append("=== 文档：").append(documentName).append(" ===\n");

                for (KnowledgeChunk chunk : documentChunks) {
                    contextBuilder.append("知识片段 ").append(fragmentIndex++).append("：\n");
                    contextBuilder.append(chunk.getContent()).append("\n");
                    if (chunk.getScore() != null) {
                        contextBuilder.append("相关度：").append(String.format("%.2f", chunk.getScore())).append("\n");
                    }
                    contextBuilder.append("\n");
                }
            }

            contextBuilder.append("请基于以上知识库信息，结合你的专业知识，准确回答用户的问题。");
            contextBuilder.append("如果知识库中没有相关信息，请明确说明。\n\n");
            contextBuilder.append("原始系统提示：\n").append(originalPrompt);

            String enhancedPrompt = contextBuilder.toString();
            log.info("构建增强提示完成，包含 {} 个知识片段，涉及 {} 个文档",
                chunks.size(), documentGroups.size());
            return enhancedPrompt;

        } catch (Exception e) {
            log.error("构建增强提示失败", e);
            return originalPrompt;
        }
    }

    /**
     * 将数据库实体转换为KnowledgeChunk对象
     */
    private KnowledgeChunk convertToKnowledgeChunk(AiKnowledgeChunk chunk) {
        KnowledgeChunk knowledgeChunk = new KnowledgeChunk();
        knowledgeChunk.setContent(chunk.getContent());
        
        // 安全处理相似度分数，避免NullPointerException
        Float similarityScore = chunk.getSimilarityScore();
        if (similarityScore != null) {
            knowledgeChunk.setScore(similarityScore.doubleValue());
        } else {
            // 如果没有相似度分数，设置为默认值0.0
            knowledgeChunk.setScore(0.0);
            log.debug("知识块 {} 的相似度分数为null，设置为默认值0.0", chunk.getChunkId());
        }
        
        knowledgeChunk.setChunkId(chunk.getChunkId());
        knowledgeChunk.setDocumentId(chunk.getDocumentId());
        knowledgeChunk.setKnowledgeId(chunk.getKnowledgeId());

        // 获取文档名称作为来源
        try {
            AiKnowledgeDocument document = aiKnowledgeDocumentMapper.selectById(chunk.getDocumentId());
            if (document != null) {
                knowledgeChunk.setSource(document.getDocumentName());
            }
        } catch (Exception e) {
            log.warn("获取文档信息失败，文档ID: {}", chunk.getDocumentId(), e);
        }

        return knowledgeChunk;
    }
}
