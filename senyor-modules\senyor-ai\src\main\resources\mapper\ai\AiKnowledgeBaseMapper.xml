<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.ai.mapper.AiKnowledgeBaseMapper">

    <resultMap type="org.senyor.ai.domain.vo.AiKnowledgeBaseVo" id="AiKnowledgeBaseResult">
        <id     property="knowledgeId"      column="knowledge_id"      />
        <result property="knowledgeName"    column="knowledge_name"    />
        <result property="knowledgeDesc"    column="knowledge_desc"    />
        <result property="knowledgeType"    column="knowledge_type"    />
        <result property="status"           column="status"            />
        <result property="documentCount"    column="document_count"    />
        <result property="vectorDimension"  column="vector_dimension"  />
        <result property="userId"           column="user_id"           />
        <result property="userName"         column="user_name"         />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <sql id="selectAiKnowledgeBaseVo">
        select knowledge_id, knowledge_name, knowledge_desc, knowledge_type, status, document_count, vector_dimension,
               user_id, user_name, create_by, create_time, update_by, update_time, remark
        from ai_knowledge_base
    </sql>

    <select id="selectAiKnowledgeBaseById" parameterType="Long" resultMap="AiKnowledgeBaseResult">
        <include refid="selectAiKnowledgeBaseVo"/>
        where knowledge_id = #{knowledgeId}
    </select>

    <select id="selectAiKnowledgeBasesByUserId" parameterType="Long" resultMap="AiKnowledgeBaseResult">
        <include refid="selectAiKnowledgeBaseVo"/>
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="selectAiKnowledgeBaseList" parameterType="org.senyor.ai.domain.AiKnowledgeBase" resultMap="AiKnowledgeBaseResult">
        <include refid="selectAiKnowledgeBaseVo"/>
        <where>
            <if test="knowledgeName != null and knowledgeName != ''">
                AND knowledge_name like concat('%', #{knowledgeName}, '%')
            </if>
            <if test="knowledgeType != null and knowledgeType != ''">
                AND knowledge_type = #{knowledgeType}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
        </where>
        order by create_time desc
    </select>

    <select id="selectEnabledKnowledgeBases" resultMap="AiKnowledgeBaseResult">
        <include refid="selectAiKnowledgeBaseVo"/>
        where status = '0'
        order by create_time desc
    </select>

    <select id="selectAiKnowledgeBasesByIds" resultMap="AiKnowledgeBaseResult">
        <include refid="selectAiKnowledgeBaseVo"/>
        where knowledge_id in
        <foreach collection="knowledgeIds" item="knowledgeId" open="(" separator="," close=")">
            #{knowledgeId}
        </foreach>
        order by create_time desc
    </select>

</mapper>
