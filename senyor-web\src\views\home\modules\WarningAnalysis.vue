<template>
  <div class="warning-analysis">
    <div class="module-title">预警分析</div>

    <div class="charts-container">
      <!-- 左侧布局 - 纵向堆叠柱状图 -->
      <div class="chart-column">
        <div class="main-chart" ref="stackedBarChart"></div>
      </div>

      <!-- 右侧布局 - 环形图 -->
      <div class="chart-column">
        <div class="gender-chart" ref="genderRatioChart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import { getGenderRatio, getGradeWarningStats } from '@/api/statistics/riskWarning/index';
import { useWarningLevels } from '@/composables/useWarningLevels';
import { ScaleWarningLevelVO } from '@/api/statistics/riskWarning/types';

// 图表元素引用
const stackedBarChart = ref(null);
const genderRatioChart = ref(null);

// 图表实例
let barChartInstance: echarts.ECharts | null = null;
let ratioChartInstance: echarts.ECharts | null = null;

// 使用预警等级组合函数获取缓存的预警等级数据
const { warningLevels, loading: warningLevelsLoading } = useWarningLevels();

// 年级预警数据
const gradeWarningData = ref<any[]>([]);

// 性别比例数据
const genderRatioData = ref<any[]>([]);

// 获取预警等级数据和年级统计数据
const fetchWarningData = async () => {
  try {
    // 获取年级预警统计数据
    const gradeResponse = await getGradeWarningStats();
    if (gradeResponse.code === 200 && gradeResponse.data) {
      gradeWarningData.value = gradeResponse.data;
      console.log(gradeWarningData.value, "年级预警数据");

      // 数据加载完成后初始化柱状图
      if (warningLevels.value.length > 0) {
        setTimeout(() => {
          initStackedBarChart();
        }, 50);
      }
    }
  } catch (error) {
    console.error('获取预警数据失败:', error);
    // 使用默认数据初始化
    initStackedBarChart();
  }
};

// 初始化堆叠柱状图
const initStackedBarChart = () => {
  if (!stackedBarChart.value) return;

  barChartInstance = echarts.init(stackedBarChart.value);

  // 如果没有API数据，使用默认数据
  if (warningLevels.value.length === 0 || gradeWarningData.value.length === 0) {
    // 默认数据
    const defaultWarningData = [
      { grade: '一年级', attention: 25, light: 20, medium: 15, severe: 8, emergency: 3, total: 71 },
      { grade: '二年级', attention: 28, light: 22, medium: 18, severe: 10, emergency: 5, total: 83 },
      { grade: '三年级', attention: 24, light: 19, medium: 14, severe: 8, emergency: 3, total: 68 },
      { grade: '四年级', attention: 20, light: 18, medium: 12, severe: 5, emergency: 2, total: 57 },
      { grade: '五年级', attention: 22, light: 20, medium: 12, severe: 6, emergency: 4, total: 64 }
    ];

    const gradeNames = defaultWarningData.map(item => item.grade);
    const attentionValues = defaultWarningData.map(item => item.attention);
    const lightValues = defaultWarningData.map(item => item.light);
    const mediumValues = defaultWarningData.map(item => item.medium);
    const severeValues = defaultWarningData.map(item => item.severe);
    const emergencyValues = defaultWarningData.map(item => item.emergency);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow'
        },
        formatter: function (params: any) {
          let result = `${params[0].name}<br/>`;
          params.forEach((param: any) => {
            result += `${param.seriesName}: ${param.value}<br/>`;
          });
          const total = params.reduce((acc: number, curr: any) => acc + curr.value, 0);
          result += `总计: ${total}`;
          return result;
        }
      },
      legend: {
        data: ['关注', '轻度', '中度', '重度', '紧急'],
        textStyle: {
          color: '#666',
          fontSize: 12
        },
        top: '0%',
        right: '5%',
        itemWidth: 10,
        itemHeight: 10,
        itemGap: 8
      },
      grid: {
        left: '5%',
        right: '5%',
        bottom: '10%',
        top: '25%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: gradeNames,
        axisLine: {
          lineStyle: {
            color: '#ddd'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 11,
          interval: 0
        }
      },
      yAxis: {
        type: 'value',
        name: '人数',
        nameTextStyle: {
          color: '#666',
          padding: [0, 0, 0, 10],
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            color: '#eee'
          }
        },
        axisLabel: {
          color: '#666',
          fontSize: 11
        }
      },
      series: [
        {
          name: '关注',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#91D5FF'
          },
          data: attentionValues
        },
        {
          name: '轻度',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#FAAD14'
          },
          data: lightValues
        },
        {
          name: '中度',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#FA8C16'
          },
          data: mediumValues
        },
        {
          name: '重度',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#FF4D4F'
          },
          data: severeValues
        },
        {
          name: '紧急',
          type: 'bar',
          stack: '总量',
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#CF1322'
          },
          data: emergencyValues
        }
      ]
    };

    barChartInstance.setOption(option);
    return;
  }

  // 使用API返回的数据
  // 过滤掉warningLevelId为1的正常等级，只显示预警等级数据
  // const warningLevelData = warningLevels.value.filter(level => level.warningLevelId > 1);

  // 显示所有等级，包括正常等级
  const warningLevelData = warningLevels.value;
  const gradeNames = gradeWarningData.value.map(item => item.grade);

  // 图表配置
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params: any) {
        let result = `${params[0].name}<br/>`;
        params.forEach((param: any) => {
          result += `${param.seriesName}: ${param.value}<br/>`;
        });
        const total = params.reduce((acc: number, curr: any) => acc + curr.value, 0);
        result += `总计: ${total}`;
        return result;
      }
    },
    legend: {
      data: warningLevelData.map(level => level.warningName),
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      top: '0%',
      right: '5%',
      itemWidth: 10,
      itemHeight: 10,
      itemGap: 8
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '10%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: gradeNames,
      axisLine: {
        lineStyle: {
          color: '#ddd'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 11,
        interval: 0,
        rotate: gradeNames.some(name => name.length > 4) ? 30 : 0
      }
    },
    yAxis: {
      type: 'value',
      name: '人数',
      nameTextStyle: {
        color: '#666',
        padding: [0, 0, 0, 10],
        fontSize: 12
      },
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      splitLine: {
        lineStyle: {
          color: '#eee'
        }
      },
      axisLabel: {
        color: '#666',
        fontSize: 11
      }
    },
    series: warningLevelData.map(level => {
      return {
        name: level.warningName,
        type: 'bar',
        stack: '总量',
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: level.warningColor
        },
        // 从gradeWarningData中获取对应等级的数据，使用levelCounts中的数据
        data: gradeWarningData.value.map(grade => {
          // 使用levelCounts中的数据，levelCounts是一个对象，key为预警等级ID
          if (grade.levelCounts && grade.levelCounts[level.warningLevelId]) {
            return grade.levelCounts[level.warningLevelId];
          }
          return 0;
        })
      };
    })
  };

  barChartInstance.setOption(option);
};

// 初始化性别比例环形图
const initGenderRatioChart = () => {
  if (!genderRatioChart.value) return;

  ratioChartInstance = echarts.init(genderRatioChart.value);

  const option = {
    title: {
      text: '预警性别比例',
      textStyle: {
        color: '#333',
        fontSize: 14
      },
      left: 'center',
      top: 5
    },
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: '65%',
      top: 'center',
      itemWidth: 8,
      itemHeight: 8,
      itemGap: 6,
      textStyle: {
        color: '#666',
        fontSize: 12
      },
      formatter: function (name: string) {
        const item = genderRatioData.value.find(d => d.name === name);
        const total = genderRatioData.value.reduce((acc, curr) => acc + curr.value, 0);
        const percentage = ((item?.value || 0) / total * 100).toFixed(1);
        return `${name}: ${percentage}%`;
      }
    },
    graphic: [
      {
        type: 'text',
        left: '30%',
        top: 'center',
        style: {
          text: '总数\n' + genderRatioData.value.reduce((acc, curr) => acc + curr.value, 0),
          textAlign: 'center',
          fill: '#333',
          fontSize: 12,
          fontWeight: 'bold'
        }
      }
    ],
    series: [
      {
        type: 'pie',
        radius: ['35%', '65%'],
        center: ['32%', '55%'],
        avoidLabelOverlap: false,
        label: {
          show: false
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 12,
            fontWeight: 'bold',
            formatter: '{b}: {c}\n({d}%)'
          }
        },
        labelLine: {
          show: false
        },
        data: genderRatioData.value,
        itemStyle: {
          borderRadius: 4,
          borderColor: '#fff',
          borderWidth: 1
        }
      }
    ]
  };

  ratioChartInstance.setOption(option);
};

// 获取性别比例数据
const fetchGenderRatioData = async () => {
  try {
    const response = await getGenderRatio();
    if (response.code === 200 && response.data) {
      genderRatioData.value = response.data;
      console.log(genderRatioData.value, "性别比例数据");

      // 确保DOM已渲染且数据已加载
      setTimeout(() => {
        if (!genderRatioChart.value) return;

        // 如果图表实例不存在，初始化它
        if (!ratioChartInstance) {
          ratioChartInstance = echarts.init(genderRatioChart.value);
        }

        // 使用加载的数据更新图表
        initGenderRatioChart();
      }, 50);
    }
  } catch (error) {
    console.error('获取性别比例数据失败:', error);
    // 设置默认数据以防API调用失败
    genderRatioData.value = [
      { name: '男生', value: 215, itemStyle: { color: '#1890FF' } },
      { name: '女生', value: 179, itemStyle: { color: '#722ED1' } }
    ];

    // 使用默认数据初始化图表
    setTimeout(() => {
      if (genderRatioChart.value && !ratioChartInstance) {
        ratioChartInstance = echarts.init(genderRatioChart.value);
      }
      if (ratioChartInstance) {
        initGenderRatioChart();
      }
    }, 50);
  }
};

// 窗口大小变化时重置图表
const handleResize = () => {
  barChartInstance?.resize();
  ratioChartInstance?.resize();
};

onMounted(() => {
  // 获取所有数据
  fetchWarningData();
  fetchGenderRatioData();

  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize);

  // 添加额外的延时重绘，确保在容器完全渲染后正确显示
  setTimeout(() => {
    handleResize();
  }, 300);
});

onBeforeUnmount(() => {
  // 移除窗口大小变化监听
  window.removeEventListener('resize', handleResize);

  // 销毁图表实例
  barChartInstance?.dispose();
  ratioChartInstance?.dispose();
});
</script>

<style scoped>
.warning-analysis {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  overflow: hidden;
  color: #333;
}

.module-title {
  font-size: 16px;
  color: #1890FF;
  margin-bottom: 10px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
}

.module-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #1890FF, rgba(24, 144, 255, 0.3));
  border-radius: 2px;
}

.charts-container {
  flex: 1;
  display: flex;
  gap: 10px;
  min-height: 0;
}

/* 左右两列布局，各占50%宽度 */
.chart-column {
  width: 50%;
  display: flex;
}

/* 柱形图样式 */
.main-chart {
  flex: 1;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(247, 250, 255, 0.6);
  padding: 5px;
  box-sizing: border-box;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 环形图样式 */
.gender-chart {
  flex: 1;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(247, 250, 255, 0.6);
  padding: 5px;
  box-sizing: border-box;
  position: relative;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

/* 确保图表容器正确显示 */
.main-chart>div,
.gender-chart>div {
  width: 100% !important;
  height: 100% !important;
}
</style>