<template>
    <div class="user-settings">
        <div>
            <div class="user-settings__block">
                <div class="user-settings__header">
                    <div class="user-settings__vertical-line"></div>
                    <div class="user-settings__title">基本信息完善模板</div>
                    <div class="user-settings__action">
                        <el-button type="primary" link size="small" @click="templateSettingsBtn">设置</el-button>
                    </div>
                </div>
                <div class="user-settings__description">
                    用于完善基本信息，开启后您单位的所有人员可在小程序中进行档案完善。系统已给出人员类别对应完善的模板,您可以进行选择。
                </div>
                <br />
            </div>
            <el-divider />
            <div class="user-settings__block">
                <div class="user-settings__header">
                    <div class="user-settings__vertical-line"></div>
                    <div class="user-settings__title">字段管理</div>
                    <div class="user-settings__action">
                        <el-button type="primary" link size="small" @click="fieldSettingsBtn">设置</el-button>
                    </div>
                </div>
                <div class="user-settings__description">管理模板可选字段信息。</div>
                <br />
            </div>
            <el-divider />
            <div class="user-settings__block">
                <div class="user-settings__header">
                    <div class="user-settings__vertical-line"></div>
                    <div class="user-settings__title">游客模块设置</div>
                    <div class="user-settings__action">
                        <el-switch v-model="visitorSettings" @change="modifyVisitorFlag" />
                    </div>
                </div>
                <div class="user-settings__description">游客模块化开启后将会面向社会中所有的人员进行心理测评，同时收集所有人员数据进行统计</div>
                <br />
            </div>
            <el-divider />

            <!-- 模板设置抽屉 -->
            <el-drawer v-model="templateFlag" title="模板设置" size="90%">
                <div class="p-2">
                    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                        <div v-show="showSearch" class="mb-[10px]"></div>
                    </transition>

                    <el-card shadow="never">
                        <template #header>
                            <el-row :gutter="10" class="mb8">
                                <el-col :span="1.5">
                                    <el-button v-hasPermi="['system:user:add']" type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button
                                        v-hasPermi="['system:user:edit']"
                                        type="success"
                                        plain
                                        icon="Edit"
                                        :disabled="single"
                                        @click="handleUpdate()"
                                        >修改</el-button
                                    >
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button
                                        v-hasPermi="['system:user:remove']"
                                        type="danger"
                                        plain
                                        icon="Delete"
                                        :disabled="multiple"
                                        @click="handleDelete()"
                                        >删除</el-button
                                    >
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button v-hasPermi="['system:user:export']" type="warning" plain icon="Download" @click="handleExport"
                                        >导出</el-button
                                    >
                                </el-col>
                                <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
                            </el-row>
                        </template>

                        <el-table v-loading="loading" :data="templateList" @selection-change="handleSelectionChange">
                            <el-table-column type="selection" width="55" align="center" />
                            <el-table-column v-if="false" label="" align="center" prop="id" />
                            <el-table-column label="模板名" align="center" prop="title" />
                            <el-table-column label="模板备注" align="center" prop="desc" />
                            <el-table-column label="字段数" align="center" prop="fieldNum" />
                            <el-table-column label="绑定角色" align="center" prop="roleName" />
                            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                                <template #default="scope">
                                    <el-tooltip content="修改" placement="top">
                                        <el-button
                                            v-hasPermi="['system:user:edit']"
                                            link
                                            type="primary"
                                            icon="Edit"
                                            @click="handleUpdate(scope.row)"
                                        ></el-button>
                                    </el-tooltip>
                                    <el-tooltip content="删除" placement="top">
                                        <el-button
                                            v-hasPermi="['system:user:remove']"
                                            link
                                            type="primary"
                                            icon="Delete"
                                            @click="handleDelete(scope.row)"
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>

                        <pagination
                            v-show="total > 0"
                            v-model:page="queryParams.pageNum"
                            v-model:limit="queryParams.pageSize"
                            :total="total"
                            @pagination="getList"
                        />
                    </el-card>
                    <!-- 添加或修改模板对话框 -->
                    <el-dialog v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body :before-close="handleClose">
                        <el-form ref="templateFormRef" :model="form" :rules="rules" label-width="auto">
                            <el-form-item label="模板名" prop="title">
                                <el-input v-model="form.title" placeholder="请输入模板名" />
                            </el-form-item>
                            <el-form-item label="模板备注" prop="desc">
                                <el-input v-model="form.desc" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                            <el-form-item label="是否置顶" prop="topFlag">
                                <el-switch v-model="form.topFlag" active-value="1" inactive-value="0" />
                            </el-form-item>
                            <el-form-item label="关联字段" prop="fieldIds">
                                <el-select v-model="form.fieldIds" multiple filterable placeholder="请选择关联字段" style="width: 100%">
                                    <el-option v-for="field in fieldListData" :key="field.id" :label="field.title" :value="field.id" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="绑定角色" prop="roleId">
                                <el-select v-model="form.roleId" placeholder="请选择绑定角色" filterable style="width: 100%">
                                    <el-option v-for="role in roleList" :key="role.roleId" :label="role.roleName" :value="role.roleId"> </el-option>
                                </el-select>
                            </el-form-item>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                                <el-button @click="cancel">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>
                </div>
            </el-drawer>

            <!-- 字段设置抽屉 -->
            <el-drawer v-model="fieldFlag" title="字段设置" size="90%">
                <div class="p-2">
                    <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
                        <div v-show="showFieldSearch" class="mb-[10px]"></div>
                    </transition>

                    <el-card shadow="never">
                        <template #header>
                            <el-row :gutter="10" class="mb8">
                                <el-col :span="1.5">
                                    <el-button v-hasPermi="['system:user:add']" type="primary" plain icon="Plus" @click="handleFieldAdd"
                                        >新增</el-button
                                    >
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button
                                        v-hasPermi="['system:user:edit']"
                                        type="success"
                                        plain
                                        icon="Edit"
                                        :disabled="fieldSingle"
                                        @click="handleFieldUpdate()"
                                        >修改</el-button
                                    >
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button
                                        v-hasPermi="['aystem:user:remove']"
                                        type="danger"
                                        plain
                                        icon="Delete"
                                        :disabled="fieldMultiple"
                                        @click="handleFieldDelete()"
                                        >删除</el-button
                                    >
                                </el-col>
                                <el-col :span="1.5">
                                    <el-button v-hasPermi="['system:user:export']" type="warning" plain icon="Download" @click="handleFieldExport"
                                        >导出</el-button
                                    >
                                </el-col>
                                <right-toolbar v-model:showSearch="showFieldSearch" @query-table="getFieldList"></right-toolbar>
                            </el-row>
                        </template>

                        <el-table v-loading="fieldLoading" :data="fieldList" @selection-change="handleFieldSelectionChange">
                            <el-table-column type="selection" width="55" align="center" />
                            <el-table-column v-if="false" label="" align="center" prop="id" />
                            <el-table-column label="字段名" align="center" prop="title" />
                            <el-table-column label="字段备注" align="center" prop="desc" />
                            <el-table-column label="字段标识符" align="center" prop="fieldFlag" />
                            <el-table-column label="字段状态" align="center" prop="status">
                                <template #default="scope">
                                    <el-tag :type="scope.row.status === '0' ? 'info' : 'success'">
                                        {{ scope.row.status === '0' ? '原始' : '拓展' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                                <template #default="scope">
                                    <el-tooltip content="修改" placement="top">
                                        <el-button
                                            v-hasPermi="['system:user:edit']"
                                            link
                                            type="primary"
                                            icon="Edit"
                                            @click="handleFieldUpdate(scope.row)"
                                        ></el-button>
                                    </el-tooltip>
                                    <el-tooltip content="删除" placement="top">
                                        <el-button
                                            v-hasPermi="['system:user:remove']"
                                            link
                                            type="primary"
                                            icon="Delete"
                                            @click="handleFieldDelete(scope.row)"
                                        ></el-button>
                                    </el-tooltip>
                                </template>
                            </el-table-column>
                        </el-table>

                        <pagination
                            v-show="fieldTotal > 0"
                            v-model:page="fieldQueryParams.pageNum"
                            v-model:limit="fieldQueryParams.pageSize"
                            :total="fieldTotal"
                            @pagination="getFieldList"
                        />
                    </el-card>

                    <!-- 添加或修改字段对话框 -->
                    <el-dialog v-model="fieldDialog.visible" :title="fieldDialog.title" width="600px" append-to-body :before-close="handleFieldClose">
                        <el-form ref="fieldFormRef" :model="fieldForm" :rules="fieldRules" label-width="auto">
                            <el-form-item label="字段名" prop="title">
                                <el-input v-model="fieldForm.title" placeholder="请输入字段名" />
                            </el-form-item>
                            <el-form-item label="字段备注" prop="desc">
                                <el-input v-model="fieldForm.desc" type="textarea" placeholder="请输入内容" />
                            </el-form-item>
                            <el-form-item label="字段标识符" prop="fieldFlag">
                                <el-input v-model="fieldForm.fieldFlag" placeholder="请输入字段标识符" />
                            </el-form-item>
                            <el-form-item label="字段类型" prop="type">
                                <el-select v-model="fieldForm.type" placeholder="请选择字段类型">
                                    <el-option v-for="dict in field_type" :key="dict.value" :label="dict.label" :value="dict.value" />
                                </el-select>
                            </el-form-item>
                            <el-form-item label="字段状态" prop="status">
                                <el-select v-model="fieldForm.status" placeholder="请选择字段状态">
                                    <el-option label="原始" value="0" />
                                    <el-option label="拓展" value="1" />
                                </el-select>
                            </el-form-item>
                            <!-- 动态选项区域 -->
                            <el-form-item v-if="fieldForm.type === 'select' || fieldForm.type === 'radio'" label="选项设置">
                                <div v-for="(option, index) in selectOptions" :key="index" class="user-settings__option-item">
                                    <el-input v-model="selectOptions[index]" placeholder="请输入选项内容" style="width: 80%; margin-right: 10px" />
                                    <el-button circle :disabled="selectOptions.length <= 1" @click="removeOption(index)">X</el-button>
                                </div>
                                <el-button
                                    type="primary"
                                    plain
                                    icon="Plus"
                                    :disabled="selectOptions.length >= 10"
                                    style="margin-top: 10px"
                                    @click="addOption"
                                >
                                </el-button>
                                <el-alert
                                    v-if="selectOptions.length >= 10"
                                    title="已达到最大选项数量(10个)"
                                    type="warning"
                                    show-icon
                                    style="margin-top: 10px"
                                />
                            </el-form-item>
                        </el-form>
                        <template #footer>
                            <div class="dialog-footer">
                                <el-button :loading="fieldButtonLoading" type="primary" @click="submitFieldForm">确 定</el-button>
                                <el-button @click="cancelField">取 消</el-button>
                            </div>
                        </template>
                    </el-dialog>
                </div>
            </el-drawer>
            <el-drawer v-model="demoFlag" title="模拟设置" size="50%">
                <div v-for="(item, index) in demoFields" :key="index" class="user-settings__field-item">
                    <el-text class="mx-1">{{ item.title }}：</el-text>
                    <el-input
                        v-if="item.type === 'input'"
                        v-model="item.realValue"
                        style="width: 240px"
                        placeholder="Please input"
                        class="user-settings__field-input"
                    />
                    <el-select v-else-if="item.type === 'select'" v-model="item.realValue" style="width: 240px" :placeholder="'请选择' + item.title">
                        <el-option
                            v-for="(option, optionIndex) in parseOptions(item.options)"
                            :key="optionIndex"
                            :label="option"
                            :value="optionIndex.toString()"
                        />
                    </el-select>
                    <el-input v-else v-model="item.realValue" style="width: 240px" placeholder="Please input" class="user-settings__field-input" />
                </div>
                <el-button type="primary" @click="demoClick">测试保存</el-button>
            </el-drawer>
        </div>
    </div>
</template>

<script setup name="UserSettings" lang="ts">
import { TemplateForm, TemplateQuery, TemplateVO } from '@/api/app/template/types';
import {
    addTemplate,
    delTemplate,
    getTemplate,
    getTemplateByRoleId,
    getTemplateFieldData,
    improve,
    listTemplate,
    updateTemplate
} from '@/api/app/template';
import { FieldForm, FieldQuery, FieldVO } from '@/api/app/field/types';
import { addField, delField, getAllFieldList, getField, listField, updateField } from '@/api/app/field';
import { listRole } from '@/api/system/role';
import { ref } from 'vue';
import useUserStore from '@/store/modules/user';
import { updateUserByField } from '@/api/system/user';
import { initWebSocket } from '@/utils/websocket';
import { getTenantByTenantId, updateTenant } from '@/api/system/tenant';
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { field_type } = toRefs<any>(proxy?.useDict('field_type'));
const tenantObj = ref({});
const demoFlag = ref(false);
const demoFields = ref([]);
const demoClick = async () => {
    const fieldList = demoFields.value.map((item) => {
        return {
            fieldId: item.id,
            realValue: item.realValue,
            status: item.status,
            fieldFlag: item.fieldFlag
        };
    });

    const param = {
        userId: user.userId,
        fieldList: fieldList
    };

    try {
        const res = await updateUserByField(param);
        proxy?.$modal.msgSuccess('保存成功');
    } catch (error) {
        proxy?.$modal.msgError('保存失败');
    }
};
const parseOptions = (options: string | any[]) => {
    try {
        if (Array.isArray(options)) {
            return options;
        }
        if (typeof options === 'string') {
            return JSON.parse(options);
        }
        return [];
    } catch (e) {
        console.error('Error parsing options:', e);
        return [];
    }
};
const selectOptions = ref([]);
// 处理类型变化
const handleTypeChange = (type) => {
    if (type !== 'select' && type != 'radio') {
        selectOptions.value = [''];
    } else {
        if ((fieldForm.value.type === 'select' || fieldForm.value.type === 'radio') && fieldForm.value.options) {
            try {
                selectOptions.value = JSON.parse(fieldForm.value.options);
            } catch (e) {
                selectOptions.value = [''];
            }
        }
    }
};
// 添加选项
const addOption = () => {
    if (selectOptions.value.length < 10) {
        selectOptions.value.push('');
    } else {
        proxy?.$modal.msgWarning('最多只能添加10个选项');
    }
};

// 删除选项
const removeOption = (index) => {
    if (selectOptions.value.length > 1) {
        selectOptions.value.splice(index, 1);
    }
};

// 游客设置
const visitorSettings = ref(false);

let templateFlag = ref(false);
const templateSettingsBtn = () => {
    templateFlag.value = !templateFlag.value;
    getList();
};

let fieldFlag = ref(false);
const fieldSettingsBtn = () => {
    fieldFlag.value = !fieldFlag.value;
    getFieldList();
};
const templateList = ref<TemplateVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);

const queryFormRef = ref<ElFormInstance>();
const templateFormRef = ref<ElFormInstance>();

const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initFormData: TemplateForm = {
    id: undefined,
    title: undefined,
    desc: undefined,
    status: '0',
    topFlag: '0',
    fieldIds: [],
    roleId: undefined
};
const data = reactive<PageData<TemplateForm, TemplateQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        topFlag: undefined,
        params: {}
    },
    rules: {
        id: [{ required: true, message: '不能为空', trigger: 'blur' }],
        title: [{ required: true, message: '模板名不能为空', trigger: 'blur' }],
        roleId: [{ required: true, message: '请绑定角色', trigger: 'blur' }]
    }
});

const { queryParams, form, rules } = toRefs(data);
const roleList = ref();
const user = useUserStore();
const handleDemo = async (row) => {
    demoFlag.value = true;
    const res = await getTemplateFieldData(user.userId, row.id);
    demoFields.value = res.data.fieldList;
};
/** 查询模板列表 */
const getList = async () => {
    loading.value = true;
    const res = await listTemplate(queryParams.value);
    templateList.value = res.rows;
    total.value = res.total;
    loading.value = false;
    await getAllFieldListFunc();
    const result = await listRole(null);
    roleList.value = result.rows;
};

/** 取消按钮 */
const cancel = () => {
    reset();
    dialog.visible = false;
};

/** 关闭dialog前置事件 */
const handleClose = (done) => {
    cancel();
    done();
};

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    templateFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
    queryFormRef.value?.resetFields();
    handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: TemplateVO[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = '添加模板';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: TemplateVO) => {
    reset();
    const _id = row?.id || ids.value[0];
    const res = await getTemplate(_id);
    Object.assign(form.value, {
        ...res.data,
        fieldIds: res.data.fieldIds || []
    });
    dialog.visible = true;
    dialog.title = '修改模板';
    await getAllFieldListFunc();
    // await getTemplateByRoleId(row.roleId);
};

/** 提交按钮 */
const submitForm = () => {
    templateFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true;
            try {
                const payload = {
                    ...form.value,
                    fieldIds: form.value.fieldIds || []
                };

                if (form.value.id) {
                    await updateTemplate(payload);
                } else {
                    await addTemplate(payload);
                }

                proxy?.$modal.msgSuccess('操作成功');
                dialog.visible = false;
                await getList();
            } finally {
                buttonLoading.value = false;
            }
        }
    });
};

/** 删除按钮操作 */
const handleDelete = async (row?: TemplateVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除模板编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
    await delTemplate(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
};

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'app/template/export',
        {
            ...queryParams.value
        },
        `template_${new Date().getTime()}.xlsx`
    );
};
const fieldList = ref<FieldVO[]>([]);
const fieldButtonLoading = ref(false);
const fieldLoading = ref(true);
const showFieldSearch = ref(true);
const fieldIds = ref<Array<string | number>>([]);
const fieldSingle = ref(true);
const fieldMultiple = ref(true);
const fieldTotal = ref(0);

const fieldQueryFormRef = ref<ElFormInstance>();
const fieldFormRef = ref<ElFormInstance>();

const fieldDialog = reactive<DialogOption>({
    visible: false,
    title: ''
});

const initFieldFormData: FieldForm = {
    id: undefined,
    title: undefined,
    desc: undefined,
    fieldFlag: undefined,
    status: '0',
    type: undefined
};

const fieldData = reactive<PageData<FieldForm, FieldQuery>>({
    form: { ...initFieldFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        title: undefined,
        fieldFlag: undefined,
        params: {}
    },
    rules: {
        title: [{ required: true, message: '字段名不能为空', trigger: 'blur' }],
        fieldFlag: [{ required: true, message: '字段标识符不能为空', trigger: 'blur' }],
        type: [{ required: true, message: '字段类型不能为空', trigger: 'blur' }]
    }
});

const { queryParams: fieldQueryParams, form: fieldForm, rules: fieldRules } = toRefs(fieldData);
const fieldListData = ref([]);
/** 查询字段列表 */
const getFieldList = async () => {
    fieldLoading.value = true;
    const res = await listField(fieldQueryParams.value);
    fieldList.value = res.rows;
    fieldTotal.value = res.total;
    fieldLoading.value = false;
};
const getAllFieldListFunc = async () => {
    const res = await getAllFieldList(fieldQueryParams.value);
    fieldListData.value = res.data;
};

/** 取消按钮 */
const cancelField = () => {
    resetField();
    fieldDialog.visible = false;
};

/** 关闭dialog前置事件 */
const handleFieldClose = (done) => {
    cancelField();
    done();
};

/** 表单重置 */
const resetField = () => {
    fieldForm.value = { ...initFieldFormData };
    selectOptions.value = [''];
    fieldFormRef.value?.resetFields();
};

/** 搜索按钮操作 */
const handleFieldQuery = () => {
    fieldQueryParams.value.pageNum = 1;
    getFieldList();
};

/** 重置按钮操作 */
const resetFieldQuery = () => {
    fieldQueryFormRef.value?.resetFields();
    handleFieldQuery();
};

/** 多选框选中数据 */
const handleFieldSelectionChange = (selection: FieldVO[]) => {
    fieldIds.value = selection.map((item) => item.id);
    fieldSingle.value = selection.length != 1;
    fieldMultiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleFieldAdd = () => {
    resetField();
    fieldDialog.visible = true;
    fieldDialog.title = '添加字段';
};

/** 修改按钮操作 */
const handleFieldUpdate = async (row?: FieldVO) => {
    resetField();
    const _id = row?.id || fieldIds.value[0];
    const res = await getField(_id);
    Object.assign(fieldForm.value, res.data);
    console.log(fieldForm.value.options);
    if ((fieldForm.value.type === 'select' || fieldForm.value.type === 'radio') && fieldForm.value.options) {
        try {
            selectOptions.value = JSON.parse(fieldForm.value.options);
        } catch (e) {
            selectOptions.value = [''];
        }
    }

    fieldDialog.visible = true;
    fieldDialog.title = '修改字段';
};

/** 提交按钮 */
const submitFieldForm = () => {
    fieldFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            if (fieldForm.value.type === 'select' || fieldForm.value.type === 'radio') {
                // 过滤空选项
                const validOptions = selectOptions.value.filter((opt) => opt.trim() !== '');
                if (validOptions.length === 0) {
                    proxy?.$modal.msgWarning('请至少添加一个有效选项');
                    return;
                }
                fieldForm.value.options = JSON.stringify(validOptions);
            }

            fieldButtonLoading.value = true;
            try {
                if (fieldForm.value.id) {
                    await updateField(fieldForm.value);
                } else {
                    await addField(fieldForm.value);
                }
                proxy?.$modal.msgSuccess('操作成功');
                fieldDialog.visible = false;
                await getFieldList();
            } finally {
                fieldButtonLoading.value = false;
            }
        }
    });
};

/** 删除按钮操作 */
const handleFieldDelete = async (row?: FieldVO) => {
    const _ids = row?.id || fieldIds.value;
    await proxy?.$modal.confirm('是否确认删除字段编号为"' + _ids + '"的数据项？').finally(() => (fieldLoading.value = false));
    await delField(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getFieldList();
};

/** 导出按钮操作 */
const handleFieldExport = () => {
    proxy?.download(
        'app/field/export',
        {
            ...fieldQueryParams.value
        },
        `field_${new Date().getTime()}.xlsx`
    );
};

/**
 * 一键完善
 * */
const handleImprove = (item) => {
    improveFunc(item.id);
};
const improveFunc = async (templateId: string | number) => {
    await improve(templateId);
};
//修改机构的游客模块状态
const modifyVisitorFlag = async () => {
    tenantObj.value.visitorFlag = visitorSettings.value;
    const res = await updateTenant(tenantObj.value);
};
const initWebSockFunc = () => {
    let protocol = window.location.protocol === 'https:' ? 'wss://' : 'ws://';
    initWebSocket(protocol + window.location.host + import.meta.env.VITE_APP_BASE_API + '/resource/websocket');
};
const initPageData = async () => {
    tenantObj.value = (await getTenantByTenantId(user.tenantId)).data;
    visitorSettings.value = tenantObj.value.visitorFlag ? tenantObj.value : visitorSettings.value;
};
onMounted(() => {
    initWebSockFunc();
    initPageData();
});
</script>

<style scoped lang="scss">
.user-settings {
    padding: 20px;
    background-color: #f5f7fa;
    min-height: 100vh;

    &__block {
        margin-bottom: 20px;
    }

    &__header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    &__vertical-line {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 10px;
    }

    &__title {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        margin-right: 10px;
        background-color: #409eff;
        min-height: 30px;
        line-height: 30px;
        min-width: 150px;
        text-align: center;
        padding: 5px 15px;
        border-radius: 12px 0 12px 0;
    }

    &__description {
        font-size: 14px;
        color: #909399;
        padding-left: 14px;
    }

    &__option-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    &__field-item {
        margin-bottom: 15px;
    }

    &__field-input {
        margin-top: 5px;
    }

    &__action {
        margin-left: auto;
    }
}

.el-divider {
    margin: 20px 0;
}
</style>
