package org.senyor.scale.controller;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;

import cn.dev33.satoken.annotation.SaMode;
import lombok.RequiredArgsConstructor;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.*;
import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.extern.slf4j.Slf4j;
import org.senyor.scale.domain.vo.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import org.senyor.common.idempotent.annotation.RepeatSubmit;
import org.senyor.common.log.annotation.Log;
import org.senyor.common.web.core.BaseController;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.common.core.domain.R;
import org.senyor.common.core.validate.AddGroup;
import org.senyor.common.core.validate.EditGroup;
import org.senyor.common.log.enums.BusinessType;
import org.senyor.common.excel.utils.ExcelUtil;
import org.senyor.scale.domain.bo.ScaleAssessPlanBo;
import org.senyor.scale.service.IScaleAssessPlanService;
import org.senyor.common.mybatis.core.page.TableDataInfo;

/**
 * 普查任务
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/scale/assessPlan")
public class ScaleAssessPlanController extends BaseController {

    private final IScaleAssessPlanService scaleAssessPlanService;

    /**
     * 查询普查任务列表
     */
    @SaCheckPermission("scale:assessPlan:list")
    @GetMapping("/list")
    public TableDataInfo<ScaleAssessPlanVo> list(ScaleAssessPlanBo bo, PageQuery pageQuery) {
        return scaleAssessPlanService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出普查任务列表
     */
    @SaCheckPermission("scale:assessPlan:export")
    @Log(title = "普查任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(ScaleAssessPlanBo bo, HttpServletResponse response) {
        List<ScaleAssessPlanVo> list = scaleAssessPlanService.queryList(bo);
        ExcelUtil.exportExcel(list, "普查任务", ScaleAssessPlanVo.class, response);
    }

    /**
     * 获取普查任务详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("scale:assessPlan:query")
    @GetMapping("/{id}")
    public R<ScaleAssessPlanVo> getInfo(@NotNull(message = "主键不能为空")
                                     @PathVariable Long id) {
        return R.ok(scaleAssessPlanService.queryById(id));
    }

    /**
     * 新增普查任务
     */
    @SaCheckPermission("scale:assessPlan:add")
    @Log(title = "普查任务", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ScaleAssessPlanBo bo) {
        return toAjax(scaleAssessPlanService.insertByBo(bo));
    }

    /**
     * 修改普查任务
     */
    @SaCheckPermission("scale:assessPlan:edit")
    @Log(title = "普查任务", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ScaleAssessPlanBo bo) {
        return toAjax(scaleAssessPlanService.updateByBo(bo));
    }

    /**
     * 删除普查任务
     *
     * @param ids 主键串
     */
    @SaCheckPermission("scale:assessPlan:remove")
    @Log(title = "普查任务", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(scaleAssessPlanService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 查询团体普查活动报告
     */
    @SaCheckPermission("scale:assessPlan:record")
    @GetMapping("/record")
    public R<ScaleAssessPlanRecordVo> getScaleAssessPlanRecord(@RequestParam("id") Long id){
        return R.ok(scaleAssessPlanService.getScaleAssessPlanRecord(id));
    }

    /**
     * 导出Word
     * @param exportDataVo
     * @return
     */
    @SaCheckPermission(value = {"scale:assessPlan:exportWord"}, mode = SaMode.OR)
    @PostMapping("/exportWord")
    public R<Void> exportWordReport(@RequestBody ExportDataVo exportDataVo, String dictLabel, String dictType) {
        return R.ok("功能测试中");
    }

    /**
     * 导出Word
     * @param exportDataVo
     * @return
     */
    @SaCheckPermission(value = {"scale:assessPlan:exportPdf"}, mode = SaMode.OR)
    @PostMapping("/exportPdf")
    public R<Void> exportPdfReport(@RequestBody ExportDataVo exportDataVo, String dictLabel, String dictType) {
        return R.ok("功能测试中");
    }


    /**
     * 查询量表完成情况
     */
    @GetMapping("/completion")
    public R<List<InstitutionScaleStatusVo>> getScaleInstitutionScaleStatus(
        @RequestParam("planId") Long planId,
        @RequestParam("scaleId" )Long scaleId,
        @RequestParam(name = "tenantId",required = false)String tenantId
        )
    {

        return R.ok(scaleAssessPlanService.getScaleInstitutionScaleStatus(planId,scaleId,tenantId));
    }

    /**
     * 查询附属机构的普查活动列表
     *
     * @param planId 计划ID
     * @return 附属机构普查活动列表
     */
    @GetMapping("/subordinate")
    public R<List<ScaleAssessPlanVo>> getSubordinatePlans(@RequestParam("planId") Long planId) {
        return R.ok(scaleAssessPlanService.querySubordinatePlansByPlanId(planId));
    }


}
