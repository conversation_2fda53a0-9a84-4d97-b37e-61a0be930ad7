# 增强检索功能说明

## 功能概述

本功能通过多策略检索算法显著提高知识库检索的覆盖率和准确性，解决"有些内容还是匹配不到"的问题。

## 主要改进

### 1. 多策略检索
- **向量化检索**：多级阈值策略，逐步降低相似度要求
- **关键词检索**：增强分词和匹配算法
- **模糊匹配**：支持编辑距离、词汇重叠、子字符串匹配

### 2. 检索策略配置
```java
// 多级相似度阈值
private static final double[] SIMILARITY_THRESHOLDS = {0.5, 0.3, 0.1};

// 多级返回数量
private static final int[] TOP_K_VALUES = {10, 20, 30};

// 最小相似度分数
private static final double MIN_SIMILARITY_SCORE = 0.3;
```

### 3. 分数计算优化
- **综合分数**：内容相似度70% + 文档名称匹配度30%
- **关键词分数**：基于匹配关键词数量和出现频率
- **模糊匹配分数**：编辑距离30% + 词汇重叠40% + 子字符串30%

## 技术实现

### 1. 多级向量化检索
```java
private List<KnowledgeChunk> retrieveKnowledgeByVectorMultiLevel(String query, Long knowledgeId, int topK) {
    // 多级阈值检索：0.5 -> 0.3 -> 0.1
    for (int i = 0; i < SIMILARITY_THRESHOLDS.length && allResults.size() < topK; i++) {
        double threshold = SIMILARITY_THRESHOLDS[i];
        int currentTopK = Math.min(TOP_K_VALUES[i], topK - allResults.size());
        
        List<KnowledgeChunk> results = vectorStoreService.searchSimilar(
            VECTOR_INDEX_NAME, queryEmbedding, currentTopK, threshold
        );
        // 去重添加结果
    }
}
```

### 2. 增强关键词检索
```java
private List<KnowledgeChunk> retrieveKnowledgeByKeywords(String query, Long knowledgeId, int topK) {
    // 增强分词：支持更多分隔符和中文分词
    String[] keywords = Arrays.stream(query.split("[\s,，。！？!?.;；、]+"))
        .filter(s -> s != null && !s.trim().isEmpty())
        .distinct()
        .toArray(String[]::new);
    
    // 计算关键词匹配分数
    double keywordScore = calculateKeywordMatchScore(query, chunk.getContent(), keywords);
}
```

### 3. 模糊匹配检索
```java
private List<KnowledgeChunk> retrieveKnowledgeByFuzzyMatch(String query, Long knowledgeId, int topK) {
    // 编辑距离相似度
    double editDistanceScore = calculateEditDistanceSimilarity(lowerQuery, lowerContent);
    
    // 词汇重叠度
    double vocabularyOverlapScore = calculateVocabularyOverlap(lowerQuery, lowerContent);
    
    // 子字符串匹配
    double substringScore = calculateSubstringMatch(lowerQuery, lowerContent);
    
    // 综合分数
    return (editDistanceScore * 0.3 + vocabularyOverlapScore * 0.4 + substringScore * 0.3);
}
```

### 4. 结果去重和排序
```java
private List<KnowledgeChunk> deduplicateAndSortResults(String query, List<KnowledgeChunk> allResults, int topK) {
    // 去重：按chunkId去重
    Map<Long, KnowledgeChunk> uniqueResults = new HashMap<>();
    
    // 重新计算综合分数
    List<KnowledgeChunk> finalResults = uniqueResults.values().stream()
        .map(chunk -> {
            double finalScore = recalculateFinalScore(query, chunk);
            chunk.setScore(finalScore);
            return chunk;
        })
        .sorted((a, b) -> Double.compare(b.getScore(), a.getScore()))
        .limit(topK)
        .collect(Collectors.toList());
}
```

## 数据库优化

### 1. 新增模糊匹配查询
```xml
<!-- 模糊匹配检索（指定知识库） -->
<select id="selectChunksByFuzzyMatch" resultMap="AiKnowledgeChunkResult">
    <include refid="selectAiKnowledgeChunkVo"/>
    where knowledge_id = #{knowledgeId}
      and vector_status = '2'
      and content is not null
      and content != ''
    <if test="query != null and query != ''">
      and (
        <!-- 完整查询匹配 -->
        content like concat('%', #{query}, '%')
        <!-- 分词匹配 -->
        <foreach collection="query.split(' ')" item="word" separator=" or ">
          or content like concat('%', #{word}, '%')
        </foreach>
        <!-- 中文分词匹配 -->
        <foreach collection="query.split('')" item="char" separator=" or ">
          <if test="char != ' ' and char != ''">
            or content like concat('%', #{char}, '%')
          </if>
        </foreach>
      )
    </if>
    order by chunk_index asc
    limit #{topK}
</select>
```

## 使用方法

### 1. 自动应用
新的检索功能会自动应用到所有知识库检索中，无需额外配置。

### 2. 测试检索功能
```http
POST /ai/documents/test-retrieval
Content-Type: application/x-www-form-urlencoded

query=测试查询内容&knowledgeId=1&topK=10
```

返回结果示例：
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "query": "测试查询内容",
    "knowledgeId": 1,
    "topK": 10,
    "totalResults": 8,
    "results": [
      {
        "chunkId": 1,
        "content": "相关内容...",
        "score": 0.85,
        "source": "文档名称",
        "documentId": 1,
        "knowledgeId": 1
      }
    ],
    "statistics": {
      "avgScore": 0.72,
      "maxScore": 0.95,
      "minScore": 0.45
    }
  }
}
```

### 3. 检索策略说明
- **第一级**：相似度阈值0.5，返回10个结果
- **第二级**：相似度阈值0.3，返回20个结果
- **第三级**：相似度阈值0.1，返回30个结果
- **关键词检索**：基于分词和关键词匹配
- **模糊匹配**：基于编辑距离和词汇重叠

## 性能优化

### 1. 检索性能
- **多级检索**：避免一次性检索过多数据
- **去重机制**：减少重复计算
- **分数缓存**：避免重复计算相似度

### 2. 内存使用
- **流式处理**：避免一次性加载所有结果
- **分页处理**：控制单次处理的数据量
- **垃圾回收**：及时释放临时对象

### 3. 数据库优化
- **索引优化**：确保查询字段有适当索引
- **查询优化**：使用高效的SQL语句
- **连接池**：合理配置数据库连接池

## 配置参数

### 1. 相似度阈值
```java
// 可根据实际需求调整
private static final double[] SIMILARITY_THRESHOLDS = {0.5, 0.3, 0.1};
```

### 2. 返回数量
```java
// 可根据性能需求调整
private static final int[] TOP_K_VALUES = {10, 20, 30};
```

### 3. 分数权重
```java
// 综合分数权重
double combinedScore = contentScore * 0.7 + documentNameScore * 0.3;

// 模糊匹配权重
double fuzzyScore = editDistanceScore * 0.3 + vocabularyOverlapScore * 0.4 + substringScore * 0.3;
```

## 最佳实践

### 1. 查询优化
- 使用具体的关键词
- 避免过于宽泛的查询
- 利用文档名称进行精确匹配

### 2. 性能监控
- 监控检索响应时间
- 观察内存使用情况
- 定期优化数据库查询

### 3. 结果评估
- 分析检索准确率
- 评估用户满意度
- 根据反馈调整参数

## 故障排除

### 1. 检索结果不准确
- 检查相似度阈值设置
- 验证向量化是否完成
- 确认文档内容质量

### 2. 性能问题
- 检查数据库索引
- 优化查询语句
- 调整返回数量限制

### 3. 内存溢出
- 减少单次检索数量
- 优化分数计算算法
- 增加垃圾回收频率

## 版本兼容性

- 新功能向后兼容
- 现有API接口保持不变
- 新增测试接口为可选功能

## 更新日志

### v1.2.0 (当前版本)
- ✅ 实现多策略检索
- ✅ 添加模糊匹配算法
- ✅ 优化分数计算
- ✅ 增强数据库查询
- ✅ 添加测试接口
- ✅ 完善错误处理 