package org.senyor.ai.service;

import org.senyor.ai.service.IKnowledgeRetrievalService.KnowledgeChunk;

import java.util.List;

/**
 * 向量存储服务接口
 * 用于在Elasticsearch中存储和检索向量
 *
 * <AUTHOR>
 */
public interface IVectorStoreService {

    /**
     * 创建向量索引
     *
     * @param indexName 索引名称
     * @return 是否创建成功
     */
    boolean createIndex(String indexName);

    /**
     * 存储向量
     *
     * @param indexName 索引名称
     * @param chunkId 分块ID
     * @param content 文本内容
     * @param embedding 向量数据
     * @param metadata 元数据
     * @return 是否存储成功
     */
    boolean storeVector(String indexName, Long chunkId, String content, List<Float> embedding, String metadata);

    /**
     * 批量存储向量
     *
     * @param indexName 索引名称
     * @param vectors 向量数据列表
     * @return 成功存储的数量
     */
    int batchStoreVectors(String indexName, List<VectorData> vectors);

    /**
     * 向量相似度搜索
     *
     * @param indexName 索引名称
     * @param queryEmbedding 查询向量
     * @param topK 返回结果数量
     * @param minScore 最小相似度分数
     * @return 相似的知识片段列表
     */
    List<KnowledgeChunk> searchSimilar(String indexName, List<Float> queryEmbedding, int topK, double minScore);

    /**
     * 删除向量
     *
     * @param indexName 索引名称
     * @param chunkId 分块ID
     * @return 是否删除成功
     */
    boolean deleteVector(String indexName, Long chunkId);

    /**
     * 删除知识库的所有向量
     *
     * @param indexName 索引名称
     * @param knowledgeId 知识库ID
     * @return 删除的向量数量
     */
    int deleteVectorsByKnowledgeId(String indexName, Long knowledgeId);

    /**
     * 根据文档ID删除向量
     *
     * @param indexName 索引名称
     * @param documentId 文档ID
     * @return 删除的向量数量
     */
    int deleteVectorsByDocumentId(String indexName, Long documentId);

    /**
     * 向量数据类
     */
    class VectorData {
        private Long chunkId;
        private String content;
        private List<Float> embedding;
        private String metadata;

        public VectorData() {}

        public VectorData(Long chunkId, String content, List<Float> embedding, String metadata) {
            this.chunkId = chunkId;
            this.content = content;
            this.embedding = embedding;
            this.metadata = metadata;
        }

        // Getters and Setters
        public Long getChunkId() { return chunkId; }
        public void setChunkId(Long chunkId) { this.chunkId = chunkId; }

        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }

        public List<Float> getEmbedding() { return embedding; }
        public void setEmbedding(List<Float> embedding) { this.embedding = embedding; }

        public String getMetadata() { return metadata; }
        public void setMetadata(String metadata) { this.metadata = metadata; }
    }
} 