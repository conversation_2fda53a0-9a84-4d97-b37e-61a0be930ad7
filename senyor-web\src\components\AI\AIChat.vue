<template>
    <div class="ai-chat-container">
    <!-- 侧边栏：会话列表 - 仅在全屏模式且显示历史记录时显示 -->
    <div class="sidebar" v-if="props.isFullscreen && props.showHistory">
      <!-- 添加顶部会话标题 -->
      <div class="sidebar-header">
        <h2>会话列表 </h2>
        <el-button type="text" class="clear-history-btn" @click="clearAllConversations">清空所有记录</el-button>
      </div>
      
      <div class="conversation-list" style="max-height: 600px; overflow-y: auto;">
        <div
          v-for="(conv, idx) in conversations.slice(0, 30)"
          :key="conv.conversationId"
          :class="['conversation-item', { active: currentConversation?.conversationId === conv.conversationId }]"
          @click="switchConversation(conv)"
        >
          <div class="conversation-title">{{ conv.title }}</div>
          <div class="conversation-actions">
            <el-button
              type="text"
              size="small"
              @click.stop="handleConvAction('rename', conv)"
              style="color: #409EFF;"
              title="重命名会话"
            >
              <el-icon><Edit /></el-icon>
            </el-button>
            <el-button
              type="text"
              size="small"
              @click.stop="handleConvAction('delete', conv)"
              style="color: #f56c6c;"
              title="删除会话"
            >
              <el-icon><Delete /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天主窗口 -->
    <div class="chat-main" :class="{'with-sidebar': props.isFullscreen && props.showHistory}">
      <div v-if="!currentConversation" class="no-conversation">
        <div class="welcome-content">
          <h2>欢迎使用AI助手</h2>
          <p>创建新会话或从左侧选择已有会话开始聊天</p>
          <!-- 移除新建会话按钮 -->
        </div>
      </div>
      
      <template v-else>
        <!-- 聊天消息区域 -->
        <div ref="messagesRef" class="chat-messages" :class="{'with-header': props.isFullscreen}">
          <div v-if="loading" class="loading-messages">
            <el-skeleton :rows="3" animated />
          </div>
          
          <div v-else-if="messages.length === 0" class="empty-messages">
            <p>开始您的对话</p>
          </div>
          
          <template v-else>
            <div v-for="(msg, idx) in messages" :key="idx" :class="['message', msg.role]">
                <!-- 头像 -->
                <div class="avatar">
                <el-avatar :src="msg.role === 'user' ? userAvatar : aiAvatar" :alt="msg.role === 'user' ? userNickname : 'AI助手'" :size="props.isFullscreen ? 'default' : 'small'" />
                </div>

                <!-- 消息内容 -->
                <div class="content">
                <div class="message-header">
                  <span class="sender">{{ msg.role === 'user' ? userNickname : 'AI助手' }}</span>
                  <span class="time">{{ formatTime(msg.createTime) }}</span>
                    </div>

                    <!-- 思考过程区域 - 当存在reasoningContent且为AI消息时显示 -->
                    <div v-if="msg.role === 'assistant' && msg.reasoningContent" class="reasoning-content">
                      <div class="reasoning-header">
                        <div class="reasoning-title">
                          <el-icon><Cpu /></el-icon> 思考过程
                        </div>
                        <el-tooltip content="AI的思考过程，帮助理解回答的推理逻辑">
                          <el-icon><QuestionFilled /></el-icon>
                        </el-tooltip>
                      </div>
                      <div class="reasoning-text">{{ msg.reasoningContent }}</div>
                    </div>

                    <div class="message-text">
                        <div v-if="msg.role === 'user'">{{ msg.content }}</div>
                  <!-- 使用v-html显示markdown格式的内容 -->
                  <div v-else class="markdown-rendered" v-html="renderMarkdown(msg.content)"></div>
                </div>
                
                <!-- 引用的知识来源 -->
                <div v-if="msg.references && msg.references.length > 0" class="references">
                  <div class="reference-title">
                    <el-icon><info-filled /></el-icon> 引用来源:
                    </div>
                  <ul>
                    <li v-for="(ref, idx) in msg.references" :key="idx">{{ ref }}</li>
                  </ul>
                </div>
                
                <!-- 消息操作按钮 - 仅在AI回答消息下显示 -->
                <div v-if="msg.role === 'assistant' && !isStreaming && !msg._regenerating" class="message-actions compact-actions bubble-actions">
                  <el-tooltip content="复制" placement="top" :show-after="500">
                    <el-button plain size="small" class="action-button" @click="copyMessage(msg.content)">
                      <el-icon><DocumentCopy /></el-icon>
                    </el-button>
                  </el-tooltip>
                  <div class="divider"></div>
                  <el-tooltip content="点赞" placement="top" :show-after="500">
                    <span
                      :class="['thumbs-up', { active: msg.likeStatus === 1 }]"
                      @click="likeAnswer(msg, 'up')"
                      style="width: 28px; height: 28px; display: inline-flex; align-items: center; justify-content: center; cursor: pointer; margin-right: 8px; outline: none; box-shadow: none; border: none;"
                      tabindex="0"
                      @mousedown.prevent
                    >
                      <svg viewBox="0 0 1024 1024" width="20" height="20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M190.193225 471.411583c14.446014 0 26.139334-11.718903 26.139334-26.13831 0-14.44499-11.69332-26.164916-26.139334-26.164916-0.271176 0-0.490164 0.149403-0.73678 0.149403l-62.496379 0.146333c-1.425466-0.195451-2.90005-0.295735-4.373611-0.295735-19.677155 0-35.621289 16.141632-35.621289 36.114522L86.622358 888.550075c0 19.949354 15.96767 35.597753 35.670407 35.597753 1.916653 0 3.808746 0.292666 5.649674 0l61.022819 0.022513c0.099261 0 0.148379 0.048095 0.24764 0.048095 0.097214 0 0.146333-0.048095 0.24457-0.048095l0.73678 0 0-0.148379c13.413498-0.540306 24.174586-11.422144 24.174586-24.960485 0-13.55983-10.760065-24.441669-24.174586-24.981974l0-0.393973-50.949392 0 1.450025-402.275993L190.193225 471.409536z" :fill="msg.likeStatus === 1 ? '#1677ff' : '#bfbfbf'"/>
                        <path d="M926.52241 433.948343c-19.283182-31.445176-47.339168-44.172035-81.289398-45.546336-1.77032-0.246617-3.536546-0.39295-5.380544-0.39295l-205.447139-0.688685c13.462616-39.059598 22.698978-85.58933 22.698978-129.317251 0-28.349675-3.193739-55.962569-9.041934-82.542948l-0.490164 0.049119c-10.638291-46.578852-51.736315-81.31498-100.966553-81.31498-57.264215 0-95.466282 48.15065-95.466282 106.126063 0 3.241834-0.294712 6.387477 0 9.532097-2.996241 108.386546-91.240027 195.548698-196.23636 207.513194l0 54.881958-0.785899 222.227314 0 229.744521 10.709923 0 500.025271 0.222057 8.746198-0.243547c19.35686 0.049119 30.239721-4.817726 47.803749-16.116049 16.682961-10.761088 29.236881-25.50079 37.490869-42.156122 2.260483-3.341095 4.028757-7.075139 5.106298-11.20111l77.018118-344.324116c1.056052-4.053316 1.348718-8.181333 1.056052-12.160971C943.643346 476.446249 938.781618 453.944769 926.52241 433.948343zM893.82573 486.837924l-82.983993 367.783411-0.099261-0.049119c-2.555196 6.141884-6.879688 11.596106-12.872169 15.427364-4.177136 2.727111-8.773827 4.351098-13.414521 4.964058-1.49812-0.195451-3.046383 0-4.620227 0l-477.028511-0.540306-0.171915-407.408897c89.323375-40.266076 154.841577-79.670527 188.596356-173.661202 0.072655 0.024559 0.124843 0.049119 0.195451 0.072655 2.99931-9.137101 6.313799-20.73423 8.697079-33.164331 5.551436-29.185716 5.258771-58.123792 5.258771-58.123792-4.937452-37.98001 25.940812-52.965306 44.364417-52.965306 25.304316 0.860601 50.263777 33.656541 50.263777 52.326762 0 0 5.600555 27.563776 5.649674 57.190537 0.048095 37.366026-4.6673 56.847729-4.6673 56.847729l-0.466628 0c-5.872754 30.879288-16.214287 60.138682-30.464849 86.964654l0.36839 0.342808c-2.358721 4.815679-3.709485 10.220782-3.709485 15.943111 0 19.922748 19.088754 21.742187 38.765909 21.742187l238.761895 0.270153c0 0 14.666024 0.465604 14.690584 0.465604l0 0.100284c12.132318-0.638543 24.221658 5.207605 31.100322 16.409738 5.504364 9.016351 6.437619 19.6045 3.486404 28.988218L893.82573 486.837924z" :fill="msg.likeStatus === 1 ? '#1677ff' : '#bfbfbf'"/>
                        <path d="M264.827039 924.31872c0.319272 0.024559 0.441045 0.024559 0.295735-0.024559 0.243547-0.048095 0.367367-0.074701-0.295735-0.074701s-0.539282 0.026606-0.271176 0.074701C264.43409 924.343279 264.532327 924.343279 264.827039 924.31872z" :fill="msg.likeStatus === 1 ? '#1677ff' : '#bfbfbf'"/>
                      </svg>
                    </span>
                  </el-tooltip>
                  <el-tooltip content="点踩" placement="top" :show-after="500">
                    <span
                      :class="['thumbs-down', { active: msg.likeStatus === -1 }]"
                      @click="likeAnswer(msg, 'down')"
                      style="width: 28px; height: 28px; display: inline-flex; align-items: center; justify-content: center; cursor: pointer; outline: none; box-shadow: none; border: none;"
                      tabindex="0"
                      @mousedown.prevent
                    >
                      <svg viewBox="0 0 1024 1024" width="20" height="20" xmlns="http://www.w3.org/2000/svg">
                        <path d="M231.552 128a103.04 103.04 0 0 0-103.36 95.68L128 230.4v246.4c0 54.4 42.816 98.752 96.768 102.208l6.784 0.192H318.08l134.656 297.984a32 32 0 0 0 24.512 18.496l4.544 0.32c74.24 0 135.04-57.344 139.136-129.792l0.192-7.808v-108.8h170.112c24.576 0.32 48.448-8.064 67.456-23.68l6.016-5.44 5.696-5.888a101.76 101.76 0 0 0 25.28-74.752l-0.96-8.128-49.344-316.736a103.168 103.168 0 0 0-96.256-86.784L742.4 128H231.552z m75.328 64v323.2H231.552a39.168 39.168 0 0 1-39.296-33.92L192 476.8V230.4c0-19.584 15.168-35.904 34.944-38.08L231.552 192h75.328z m194.56 637.44l-130.56-289.152V192h371.84a39.68 39.68 0 0 1 36.992 23.808l1.536 4.416 1.024 4.608 49.408 316.8a38.016 38.016 0 0 1-9.152 30.72 39.872 39.872 0 0 1-25.728 13.12l-5.12 0.192H589.312a32 32 0 0 0-31.68 27.648l-0.32 4.416v140.8c0 30.208-18.688 56.32-45.568 67.584l-5.888 2.176-4.352 1.28z" :fill="msg.likeStatus === -1 ? '#1677ff' : '#bfbfbf'"/>
                      </svg>
                    </span>
                  </el-tooltip>
                </div>
            </div>
        </div>
          </template>
        </div>

        <!-- 输入区域 -->
        <div class="input-area">
          <!-- 输入区域上方操作栏 -->
          <div class="input-toolbar">
            <div class="flat-new-chat-btn" @click="createNewConversation">
              <el-icon><Plus /></el-icon>
              <span>新建对话</span>
            </div>
            <!-- 添加功能按钮到右侧 -->
            <div class="toolbar-right">
              <el-tooltip content="开启后AI会联网搜索最新信息，提供更准确的回答" placement="top">
                <el-button 
                  :type="enableSearch ? 'primary' : 'default'" 
                  @click="toggleEnableSearch" 
                  size="small"
                  class="search-button"
                >
                  <el-icon><Search /></el-icon> 联网搜索
                </el-button>
              </el-tooltip>
              <el-tooltip content="开启后AI会展示思考过程，帮助理解回答的推理逻辑" placement="top">
                <el-button 
                  :type="thinkingMode ? 'primary' : 'default'" 
                  @click="toggleThinkingMode" 
                  size="small"
                  class="thinking-button"
                >
                  <el-icon><Cpu /></el-icon> 深度思考
                </el-button>
              </el-tooltip>
            </div>
          </div>
          
          <div class="input-wrapper">
            <el-input
              v-model="userInput"
              type="textarea"
              :autosize="{ minRows: 1, maxRows: 4 }"
              placeholder="请输入问题，Enter发送，Shift+Enter换行"
              :disabled="isLoading"
              @keydown="onInputKeydown"
            />
            
            <el-button 
              :loading="isLoading" 
              :disabled="!userInput.trim()" 
              type="primary" 
              class="send-button"
              @click="handleSendMessage"
            >
              <el-icon><position /></el-icon>
            </el-button>
          </div>
          
          <div class="input-tips" v-if="props.isFullscreen || isStreaming">
            <span v-if="isStreaming">
              <el-button type="text" @click="togglePause">
                {{ isPaused ? '▶ 继续生成' : '⏸ 暂停生成' }}
              </el-button>
              <el-button type="text" @click="stopGenerating">⏹ 停止生成</el-button>
            </span>
          </div>
        </div>
      </template>
    </div>

    <!-- 对话框 -->
    <el-dialog
      v-model="newConvDialogVisible"
      title="创建新会话"
      width="30%"
    >
      <el-form :model="newConvForm" label-width="80px">
        <el-form-item label="会话标题">
          <el-input v-model="newConvForm.title" placeholder="请输入会话标题" />
        </el-form-item>
        <el-form-item label="系统提示">
          <el-input 
            v-model="newConvForm.systemPrompt" 
            type="textarea" 
            placeholder="可选：设置AI行为的系统提示词" 
            :autosize="{ minRows: 2, maxRows: 5 }" 
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="newConvDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmCreateConversation">确认</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 系统提示词设置对话框 -->
    <el-dialog
      v-model="systemPromptDialogVisible"
      title="设置系统提示词"
      width="40%"
    >
      <el-input 
        v-model="systemPromptForm.content" 
        type="textarea" 
        placeholder="设置AI行为的系统提示词" 
        :autosize="{ minRows: 4, maxRows: 8 }" 
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="systemPromptDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="updateSystemPromptHandler">确认</el-button>
        </span>
      </template>
    </el-dialog>


    </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed, watch } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus, More, Setting, InfoFilled, Search, Upload, Position, Delete, Cpu, QuestionFilled, Edit, DocumentCopy, RefreshRight, CircleCheckFilled, CircleCloseFilled, CaretTop, CaretBottom, Top, Bottom, FullScreen } from '@element-plus/icons-vue';
import MarkdownIt from 'markdown-it';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark-dimmed.css';
import useUserStore from '@/store/modules/user';
import { getConversations, createConversation, deleteConversation, clearConversationMessages, getConversationMessages, updateConversationTitle, updateSystemPrompt, feedbackMessage } from '@/api/ai/conversation';
import { getStreamChatUrl } from '@/api/ai/chat';
import { processStream } from '@/utils/streamHelper';
import { getToken } from '@/utils/auth';
import { getLanguage } from '@/lang';

// 添加自定义头像属性
interface Props {
  customAvatar?: string;
  isFullscreen?: boolean;
  showHistory?: boolean;
  currentChatId?: number | string;
  thinkingMode?: boolean; // 接收深度思考模式参数
}

const props = withDefaults(defineProps<Props>(), {
  customAvatar: '',
  isFullscreen: false,
  showHistory: false,
  currentChatId: undefined,
  thinkingMode: false // 默认关闭深度思考
});

// 定义事件
const emit = defineEmits<{
  (e: 'switchConversation', conversationId: string): void;
  (e: 'reasoning-update', content: string): void; // 新增思考过程更新事件
  (e: 'toggle-fullscreen'): void; // 新增全屏切换事件
}>();

// 初始化Markdown解析器
const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
  highlight: function (str, lang) {
    if (lang && hljs.getLanguage(lang)) {
      try {
        return hljs.highlight(str, { language: lang }).value;
      } catch (__) {}
    }
    return ''; // 使用默认的转义
  }
});

// 类型定义
interface Conversation {
  conversationId: string;
  title: string;
  systemPrompt: string;
  messageCount: number;
  totalTokens: number;
  createTime: string;
  updateTime: string;
}

interface Message {
  messageId: number;
  conversationId: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  tokens?: number;
  finishReason?: string;
  references?: string[];
  createTime: string;
  reasoningContent?: string; // 新增思考过程内容
  likeStatus?: 1 | -1; // 新增点赞/点踩状态
}

interface KnowledgeBase {
  knowledgeId: number;
  knowledgeName: string;
  knowledgeDesc?: string;
  knowledgeType?: string;
  status?: string;
}

const userStore = useUserStore();

// 响应式数据
const conversations = ref<Conversation[]>([]);
const currentConversation = ref<Conversation | null>(null);
const messages = ref<Message[]>([]);
const userInput = ref('');
const messagesRef = ref<HTMLElement | null>(null);
const isLoading = ref(false);
const isStreaming = ref(false);
const isPaused = ref(false);
const abortController = ref<AbortController | null>(null);
const loading = ref(false);
// 使用当前登录用户信息
const userAvatar = computed(() => userStore.avatar );
const userNickname = computed(() => userStore.nickname || '用户');
const aiAvatar = computed(() => props.customAvatar );

// 新增深度思考模式状态
const thinkingMode = ref(false);

// 新增联网搜索模式状态
const enableSearch = ref(false);

// 知识库相关状态 - 从AI配置中自动获取，无需用户选择
const enableKnowledge = ref(true); // 默认启用知识库检索

// 保证外部props.thinkingMode能控制内部thinkingMode
watch(() => props.thinkingMode, (val) => {
  thinkingMode.value = val;
}, { immediate: true });

// 对话框控制
const newConvDialogVisible = ref(false);
const newConvForm = reactive({
  title: '',
  systemPrompt: ''
});

const systemPromptDialogVisible = ref(false);
const systemPromptForm = reactive({
  content: ''
});



// 监听外部传入的会话ID变化
watch(() => props.currentChatId, (newId) => {
  if (newId && currentConversation.value?.conversationId !== String(newId)) {
    // 查找对应的会话
    const conv = conversations.value.find(c => c.conversationId === String(newId));
    if (conv) {
      switchConversation(conv);
    }
  }
}, { immediate: true });

// 初始化
onMounted(async () => {
  await fetchConversations();
});

// 获取会话列表时，最多查30条
const fetchConversations = async () => {
  try {
    const res = await getConversations({ limit: 30 });
    if (res.code === 200 && res.data) {
      conversations.value = res.data;
      
      // 如果有会话但没有当前会话，选择第一个
      if (conversations.value.length > 0 && !currentConversation.value) {
        switchConversation(conversations.value[0]);
      } else if (conversations.value.length === 0) {
        // 如果没有会话，重置当前会话
        currentConversation.value = null;
        messages.value = [];
      }
    }
  } catch (error) {
    console.error('获取会话列表失败:', error);
    ElMessage({
      message: '获取会话列表失败',
      type: 'error',
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
  }
};

// 切换会话
const switchConversation = async (conversation: Conversation) => {
  if (currentConversation.value?.conversationId === conversation.conversationId) {
    return;
  }

  currentConversation.value = conversation;
  messages.value = [];
  
  // 通知父组件会话已切换
  emit('switchConversation', conversation.conversationId);
  
  try {
    loading.value = true;
    const res = await getConversationMessages(conversation.conversationId);
    if (res.code === 200 && res.data) {
      messages.value = res.data;
      // 如果有消息包含reasoningContent，发送最后一条的reasoningContent给父组件
      const lastAssistantMsg = [...messages.value].reverse().find(msg => msg.role === 'assistant' && msg.reasoningContent);
      if (lastAssistantMsg && lastAssistantMsg.reasoningContent) {
        emit('reasoning-update', lastAssistantMsg.reasoningContent);
      }
      nextTick(() => {
        scrollToBottom();
      });
    }
  } catch (error) {
    console.error('获取会话消息失败:', error);
      ElMessage({
        message: '获取会话消息失败',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
  } finally {
    loading.value = false;
  }
};

// 根据ID切换到指定会话
const switchToConversation = async (chatId: number | string) => {
  try {
    // 先刷新会话列表，确保有最新数据
    await fetchConversations();
    
    const conv = conversations.value.find(c => c.conversationId === String(chatId));
    if (conv) {
      // 清空当前消息
      messages.value = [];
      
      // 设置当前会话
      currentConversation.value = conv;
      
      // 加载会话消息
      try {
        loading.value = true;
        const res = await getConversationMessages(conv.conversationId);
        if (res.code === 200 && res.data) {
          if (Array.isArray(res.data)) {
            messages.value = res.data;
            console.log('已加载消息数量:', messages.value.length);
            
            // 如果有消息包含reasoningContent，发送最后一条的reasoningContent给父组件
            const lastAssistantMsg = [...messages.value].reverse().find(msg => msg.role === 'assistant' && msg.reasoningContent);
            if (lastAssistantMsg && lastAssistantMsg.reasoningContent) {
              emit('reasoning-update', lastAssistantMsg.reasoningContent);
            }
          } else {
            console.error('获取会话消息返回的数据不是数组');
            messages.value = [];
          }
          nextTick(() => {
            scrollToBottom();
          });
        } else {
          console.error('获取会话消息失败，返回码:', res.code, '消息:', res.msg);
          messages.value = [];
        }
      } catch (msgError) {
        console.error('获取会话消息失败:', msgError);
        ElMessage.error('获取会话消息失败');
      } finally {
        loading.value = false;
      }
      
      // 通知父组件会话已切换
      emit('switchConversation', conv.conversationId);
      return true;
    } else {
      console.error('未找到ID为', chatId, '的会话');
      return false;
    }
  } catch (error) {
    console.error('切换会话失败:', error);
    return false;
  }
};

// 创建并切换到新会话
const createAndSwitchConversation = async (title: string) => {
  try {
    const res = await createConversation({
      title: title,
      systemPrompt: ''
    });

    if (res.code === 200) {
      // 确保res.data是会话ID字符串
      const conversationId = res.data;
      console.log('创建会话成功，会话ID:', conversationId);
      
      // 创建一个新的会话对象，无需等待刷新
      const newConversation: Conversation = {
        conversationId: conversationId,
        title: title,
        systemPrompt: '',
        messageCount: 0,
        totalTokens: 0,
        createTime: new Date().toISOString(),
        updateTime: new Date().toISOString()
      };
      
      // 将新会话添加到会话列表的开头
      conversations.value.unshift(newConversation);
      
      // 立即设置当前会话
      currentConversation.value = newConversation;
      messages.value = []; // 清空当前消息
      
      // 通知父组件会话已切换
      emit('switchConversation', newConversation.conversationId);
      
      // 再次刷新会话列表以保证与服务器同步
      fetchConversations().catch(err => {
        console.error('刷新会话列表失败:', err);
      });
      
      return true;
    } else {
      console.error('创建会话失败，返回码:', res.code, '消息:', res.msg);
      ElMessage({
        message: `创建会话失败: ${res.msg}`,
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
      return false;
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    ElMessage.error('创建会话失败');
    return false;
  }
};

// 创建新会话
const createNewConversation = async () => {
  // 直接创建新会话，不显示对话框
  const title = '新会话 ' + new Date().toLocaleTimeString();
  const success = await createAndSwitchConversation(title);
  return success;
};

// 确认创建新会话
const confirmCreateConversation = async () => {
  if (!newConvForm.title.trim()) {
    ElMessage({
      message: '请输入会话标题',
      type: 'warning',
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
    return;
  }

  try {
    const res = await createConversation({
      title: newConvForm.title,
      systemPrompt: newConvForm.systemPrompt
    });

    if (res.code === 200) {
      ElMessage({
        message: '创建会话成功',
        type: 'success',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
      await fetchConversations();
      
      // 切换到新创建的会话
      const conversationId = res.data;
      const newConv = conversations.value.find(c => c.conversationId === conversationId);
      if (newConv) {
        switchConversation(newConv);
      }
      
      newConvDialogVisible.value = false;
    } else {
      ElMessage({
        message: `创建会话失败: ${res.msg}`,
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    }
  } catch (error) {
    console.error('创建会话失败:', error);
    ElMessage({
      message: '创建会话失败',
      type: 'error',
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
  }
};

// 处理会话操作
const handleConvAction = (action: string, conversation: Conversation) => {
  // 根据是否全屏模式决定挂载位置和自定义类名
  const appendToElement = props.isFullscreen ? '.chat-main' : '.ai-chat-modal';
  const customClass = props.isFullscreen ? 'fullscreen-confirm-dialog' : 'ai-delete-confirm-dialog';
  
  switch (action) {
    case 'rename':
      ElMessageBox.prompt('请输入新的会话标题', '重命名会话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: conversation.title,
        appendTo: appendToElement,
        customClass: customClass
      }).then(async ({ value }) => {
        try {
          // 调用重命名API
          await updateConversationTitle(conversation.conversationId, value);
          ElMessage({
            message: `已将会话重命名为: ${value}`,
            type: 'success',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
          // 更新本地数据
          if (currentConversation.value?.conversationId === conversation.conversationId) {
            currentConversation.value.title = value;
          }
          // 刷新会话列表
          await fetchConversations();
        } catch (error) {
          console.error('重命名会话失败:', error);
          ElMessage({
            message: '重命名会话失败',
            type: 'error',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
        }
      }).catch(() => {});
      break;
      
    case 'delete':
      ElMessageBox.confirm('确定要删除此会话吗？', '删除会话', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        appendTo: appendToElement,
        customClass: customClass
      }).then(async () => {
        try {
          await deleteConversation(conversation.conversationId);
          ElMessage({
            message: '删除会话成功',
            type: 'success',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
          
          if (currentConversation.value?.conversationId === conversation.conversationId) {
            currentConversation.value = null;
            messages.value = [];
          }
          
          await fetchConversations();
        } catch (error) {
          console.error('删除会话失败:', error);
          ElMessage({
            message: '删除会话失败',
            type: 'error',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
        }
      }).catch(() => {});
      break;
      
    case 'clear':
      ElMessageBox.confirm('确定要清空此会话的所有消息吗？', '清空消息', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        appendTo: appendToElement,
        customClass: customClass
      }).then(async () => {
        try {
          await clearConversationMessages(conversation.conversationId);
          ElMessage({
            message: '清空消息成功',
            type: 'success',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
          
          if (currentConversation.value?.conversationId === conversation.conversationId) {
            messages.value = [];
          }
        } catch (error) {
          console.error('清空消息失败:', error);
          ElMessage({
            message: '清空消息失败',
            type: 'error',
            appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
            customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
          });
        }
      }).catch(() => {});
      break;
      
    case 'system-prompt':
      systemPromptForm.content = conversation.systemPrompt || '';
      systemPromptDialogVisible.value = true;
      break;
      
    case 'knowledge':
      knowledgeDialogVisible.value = true;
      break;
  }
};

// 更新系统提示词
const updateSystemPromptHandler = async () => {
  if (currentConversation.value) {
    try {
      // 调用更新系统提示词的API
      await updateSystemPrompt(currentConversation.value.conversationId, systemPromptForm.content);
      ElMessage({
        message: '系统提示词已更新',
        type: 'success',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
      // 更新本地数据
      currentConversation.value.systemPrompt = systemPromptForm.content;
      systemPromptDialogVisible.value = false;
    } catch (error) {
      console.error('更新系统提示词失败:', error);
      ElMessage({
        message: '更新系统提示词失败',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    }
  }
};



// Markdown渲染
const renderMarkdown = (content: string = '') => {
  if (!content) return '';
  return md.render(content);
};

// 发送消息(流式响应版本)
const sendMessageStream = async () => {
  if (!userInput.value.trim() || isLoading.value || !currentConversation.value) return;

  const userContent = userInput.value.trim();
  userInput.value = '';

  // 清空上一次思考过程
  emit('reasoning-update', '');

  // 添加用户消息到UI
  const userMsg: Message = {
    messageId: Date.now(),
    conversationId: currentConversation.value.conversationId,
    role: 'user',
    content: userContent,
    createTime: new Date().toISOString()
  };
  messages.value.push(userMsg);
  scrollToBottom();
  
  // 设置加载状态
  isLoading.value = true;
  isStreaming.value = true;
  
  // 创建中止控制器
  abortController.value = new AbortController();

  try {
    // 准备请求数据
    const requestData = {
      conversationId: currentConversation.value.conversationId,
      message: userContent,
      thinkingMode: thinkingMode.value, // 深度思考参数
      enableSearch: enableSearch.value, // 联网搜索参数
      enableKnowledge: enableKnowledge.value // 知识库检索参数，后端会自动从配置中获取知识库ID
    };
    
    // 创建AI回复的占位消息
    const aiMsg: Message = {
      messageId: Date.now(),
      conversationId: currentConversation.value.conversationId,
      role: 'assistant',
      content: '',
      createTime: new Date().toISOString()
    };
    messages.value.push(aiMsg);
    scrollToBottom();
    
    // 创建事件源URL (SSE)
    const params = new URLSearchParams();
    Object.entries(requestData).forEach(([key, value]) => {
      params.append(key, String(value));
    });
    
    // 获取完整URL
    const url = `${getStreamChatUrl()}`;
    
    // 使用POST方法发送SSE请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + getToken(),
        'clientid': import.meta.env.VITE_APP_CLIENT_ID,
        'Content-Language': getLanguage()
      },
      body: JSON.stringify(requestData),
      signal: abortController.value.signal
    });
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    // 创建事件读取器
    const reader = response.body?.getReader();
    if (!reader) {
      throw new Error('Stream reader is null');
    }
    
    const decoder = new TextDecoder();
    let buffer = '';
    
    // 创建处理函数
    const processChunk = async () => {
      while (true) {
        if (abortController.value?.signal.aborted) {
          console.log('Stream aborted by user');
          break;
        }
        
        const { done, value } = await reader.read();
        
        if (done) {
          console.log('Stream complete');
          break;
        }
        
        // 解码并添加到缓冲区
        const chunk = decoder.decode(value, { stream: true });
        console.log('Received chunk:', chunk);
        buffer += chunk;
        
        // 处理完整行
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';
        
        for (const line of lines) {
          // 跳过空行和注释
          if (!line.trim() || line.startsWith(':')) {
            continue;
          }
          
          if (line.startsWith('data:')) {
            const data = line.slice(5).trim();
            console.log('Parsed SSE data:', data);
            
            if (data === '[DONE]') {
              console.log('Stream ended with [DONE]');
            } else {
              try {
                const parsedData = JSON.parse(data);
                console.log('Parsed JSON data:', parsedData);
                
          // 更新最后一条AI消息的内容
                if (!isPaused.value && messages.value.length > 0) {
            const lastMsg = messages.value[messages.value.length - 1];
            if (lastMsg.role === 'assistant') {
                    if (parsedData.content) {
                      lastMsg.content += parsedData.content;
                    }
                    
                    // 更新其他字段
                    if (parsedData.messageId) {
                      lastMsg.messageId = parsedData.messageId;
                    }
                    if (parsedData.tokens) {
                      lastMsg.tokens = parsedData.tokens;
                    }
                    if (parsedData.finishReason) {
                      lastMsg.finishReason = parsedData.finishReason;
                    }
                    if (parsedData.reasoningContent) { // 4. 渲染AI消息时，如果有reasoningContent字段，单独高亮显示
                      lastMsg.reasoningContent = parsedData.reasoningContent;
                      // 触发思考过程更新事件，通知父组件
                      emit('reasoning-update', parsedData.reasoningContent);
                    }
                    if (parsedData.likeStatus) { // 5. 渲染AI消息时，如果有likeStatus字段，更新点赞/点踩状态
                      lastMsg.likeStatus = parsedData.likeStatus;
                    }
                    
              scrollToBottom();
            }
          }
              } catch (error) {
                console.error('Failed to parse SSE JSON data:', error);
              }
            }
          } else {
            console.log('Non-data line:', line);
      }
        }
      }
    };
    
    // 处理流
    await processChunk();
    
  } catch (error) {
    console.error('流式聊天错误:', error);
    if ((error as Error).name !== 'AbortError') {
        ElMessage({
          message: '发送消息失败，请重试',
          type: 'error',
          appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
          customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
        });
    }
  } finally {
    isStreaming.value = false;
      isLoading.value = false;
    abortController.value = null;
  }
};

// 处理发送消息，根据设置选择流式或普通响应
const handleSendMessage = () => {
  sendMessageStream();
};

// 滚动到底部
const scrollToBottom = () => {
    nextTick(() => {
        if (messagesRef.value) {
            messagesRef.value.scrollTop = messagesRef.value.scrollHeight;
        }
    });
};

// 暂停/继续生成
const togglePause = () => {
    isPaused.value = !isPaused.value;
};

// 停止生成
const stopGenerating = () => {
  if (abortController.value) {
    abortController.value.abort();
    abortController.value = null;
  }
  isStreaming.value = false;
  isLoading.value = false;
};

// 格式化时间
const formatTime = (dateStr: string) => {
  if (!dateStr) return '';
  const date = new Date(dateStr);
  return date.toLocaleString();
};

// 提供给父组件的方法 - 提问问题
const askQuestion = (question: string) => {
  if (question && !isLoading.value) {
    userInput.value = question;
    handleSendMessage();
  }
};

// 提供给父组件的方法 - 更新布局
const updateLayout = (isFullscreen: boolean) => {
  // 当布局改变时可能需要执行的操作
  nextTick(() => {
    scrollToBottom();
  });
};

// 切换历史记录显示
const toggleHistory = () => {
  // 这里可以添加切换历史记录的逻辑
  // 目前只是占位方法，实际逻辑可能需要与父组件通信
  console.log('切换历史记录显示');
};

// 切换深度思考模式
const toggleThinkingMode = () => {
  thinkingMode.value = !thinkingMode.value;
  console.log('深度思考模式已切换为:', thinkingMode.value);
};

// 切换联网搜索模式
const toggleEnableSearch = () => {
  enableSearch.value = !enableSearch.value;
  console.log('联网搜索模式已切换为:', enableSearch.value);
};

// 切换知识库模式
const toggleKnowledgeMode = () => {
  enableKnowledge.value = !enableKnowledge.value;
  if (!enableKnowledge.value) {
    selectedKnowledgeId.value = null;
  }
  console.log('知识库模式已切换为:', enableKnowledge.value);
};

// 知识库选择变化处理
const onKnowledgeChange = (knowledgeId) => {
  selectedKnowledgeId.value = knowledgeId;
  enableKnowledge.value = knowledgeId !== null;
  console.log('知识库选择已切换为:', knowledgeId, '启用状态:', enableKnowledge.value);
};



// 复制消息内容到剪贴板
const copyMessage = (content: string) => {
  navigator.clipboard.writeText(content)
    .then(() => {
      ElMessage({
        message: '已复制到剪贴板',
        type: 'success',
        duration: 1500,
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    })
    .catch(err => {
      console.error('复制失败:', err);
      ElMessage({
        message: '复制失败',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    });
};

// 点赞或点踩回答
const likeAnswer = async (msg: Message, action: 'up' | 'down') => {
  if (!currentConversation.value) return;

  // 计算新的状态
  let newStatus: 1 | -1 | 0;
  if (action === 'up') {
    newStatus = msg.likeStatus === 1 ? 0 : 1;
  } else {
    newStatus = msg.likeStatus === -1 ? 0 : -1;
  }

  const oldStatus = msg.likeStatus;
  msg.likeStatus = newStatus;

  try {
    // 调用API更新服务器状态
    await feedbackMessage(currentConversation.value.conversationId, msg.messageId, newStatus);
    // 显示操作成功提示
    let message = '';
    if (newStatus === 1) {
      message = '已点赞';
    } else if (newStatus === -1) {
      message = '已点踩';
    } else {
      message = '已取消评价';
    }
    ElMessage({
      message,
      type: 'success',
      duration: 1500,
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
  } catch (error) {
    // 恢复原状态
    msg.likeStatus = oldStatus;
    ElMessage({
      message: '评价操作失败，请稍后再试',
      type: 'error',
      duration: 2000,
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
  }
};

// 清空所有会话
const clearAllConversations = () => {
  if (conversations.value.length === 0) {
    ElMessage({
      message: '没有可清空的历史记录',
      type: 'info',
      appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
      customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
    });
    return;
  }
  
  const appendToElement = props.isFullscreen ? '.chat-main' : '.ai-chat-modal';
  const customClass = props.isFullscreen ? 'fullscreen-confirm-dialog' : 'ai-delete-confirm-dialog';
  
  ElMessageBox.confirm('确定要清空所有历史记录吗？此操作不可恢复。', '清空历史记录', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
    appendTo: appendToElement,
    customClass: customClass
  }).then(async () => {
    try {
      // 逐个删除会话
      const deletePromises = conversations.value.map(conv => 
        deleteConversation(conv.conversationId)
      );
      
      await Promise.all(deletePromises);
      
      // 重置当前会话和消息
      currentConversation.value = null;
      messages.value = [];
      
      // 刷新会话列表
      await fetchConversations();
      
      ElMessage({
        message: '已清空所有历史记录',
        type: 'success',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    } catch (error) {
      console.error('清空历史记录失败:', error);
      ElMessage({
        message: '清空历史记录失败',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
    }
  }).catch(() => {});
};

// 组件卸载时清理
onBeforeUnmount(() => {
    if (abortController.value) {
        abortController.value.abort();
    }
});

// 暴露方法给父组件
defineExpose({
  askQuestion,
  updateLayout,
  switchToConversation,
  createAndSwitchConversation,
  createNewConversation,
  deleteConversation: async (conversationId: string) => {
    try {
      console.log('准备删除会话:', conversationId);
      const res = await deleteConversation(conversationId);
      
      if (res.code === 200) {
        console.log('会话删除成功');
        
        // 如果删除的是当前会话，重置当前会话
        if (currentConversation.value?.conversationId === conversationId) {
          console.log('重置当前会话');
          currentConversation.value = null;
          messages.value = [];
        }
        
        // 刷新会话列表
        await fetchConversations();
        
        // 确认当前会话已被正确设置
        if (currentConversation.value?.conversationId === conversationId) {
          console.error('警告：已删除的会话仍被设置为当前会话，强制重置');
          currentConversation.value = null;
          messages.value = [];
        }
        
        return true;
      } else {
        console.error('删除会话API返回错误:', res.msg || '未知错误');
        ElMessage.error(res.msg || '删除会话失败');
        return false;
      }
    } catch (error) {
      console.error('删除会话请求发生异常:', error);
      ElMessage.error('删除会话失败，请重试');
      return false;
    }
  },
  getConversations: async () => {
    try {
      await fetchConversations();
      console.log('获取到会话列表，数量:', conversations.value.length);
      return conversations.value;
    } catch (error) {
      console.error('获取会话列表失败:', error);
      ElMessage({
        message: '获取会话列表失败，请重试',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
      return [];
    }
    },
  updateConversationTitle: async (conversationId: string, title: string) => {
    try {
      // 调用重命名API
      await updateConversationTitle(conversationId, title);
      
      // 更新本地数据
      if (currentConversation.value?.conversationId === conversationId) {
        currentConversation.value.title = title;
      }
      
      // 更新会话列表中的标题
      const conv = conversations.value.find(c => c.conversationId === conversationId);
      if (conv) {
        conv.title = title;
      }
      
      return true;
    } catch (error) {
      console.error('重命名会话失败:', error);
      ElMessage({
        message: '重命名会话失败',
        type: 'error',
        appendTo: props.isFullscreen ? '.chat-main' : '.ai-chat-modal',
        customClass: props.isFullscreen ? 'fullscreen-message' : 'ai-message-in-chat'
      });
      return false;
    }
  },
  clearAllConversations: clearAllConversations
});

// 在<script setup>中添加：
const onInputKeydown = (e: KeyboardEvent) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    handleSendMessage();
  }
  // Shift+Enter 默认行为就是换行，无需处理
};

// 在<script setup>中添加方法：
const toggleFullscreen = () => {
  emit('toggle-fullscreen'); // 通知父组件切换全屏，或自行实现全屏逻辑
};
</script>

<style scoped>
/* 整体布局 */
.ai-chat-container {
    display: flex;
    height: 100%;
  width: 100%;
    background-color: #ffffff;
    border-radius: 0;
    overflow: hidden;
    box-shadow: none;
}

/* 侧边栏样式 */
.sidebar {
  width: 370px; 
  background-color: #fff;
  border-right: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
  padding-bottom: 20px;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;
    display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #ffffff;
  margin-bottom: 15px;
}

.sidebar-header h2 {
  font-size: 16px;
    margin: 0;
  color: #333333;
  font-weight: 500;
}

.conversation-list {
  max-height: 650px;
  overflow-y: auto;
  padding: 0 16px;
}
.conversation-list::-webkit-scrollbar {
  width: 6px;
}
.conversation-list::-webkit-scrollbar-thumb {
  background: #e0e0e0;
  border-radius: 3px;
}

.conversation-item {
  padding: 14px;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f7f7f7;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
}

.conversation-item:hover {
  background-color: #f0f0f0;
  transform: translateY(-1px);
  box-shadow: 0 3px 5px rgba(0, 0, 0, 0.08);
}

.conversation-item.active {
  background-color: #edf5ff;
  border: 1px solid #c6e2ff;
}

.conversation-title {
  font-size: 15px;
  color: #4b5563;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex-grow: 1;
  padding-left: 4px;
}

.conversation-actions {
  display: flex;
  align-items: center;
  visibility: hidden;
}

.conversation-item:hover .conversation-actions,
.conversation-item.active .conversation-actions {
  visibility: visible;
    }

/* 聊天主窗口 */
.chat-main {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
}

.chat-main.with-sidebar {
  width: calc(100% - 420px); /* 修改宽度计算，与sidebar宽度对应 */
}

.chat-header {
  padding: 12px 16px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
  color: #374151;
}

/* 空状态 */
.no-conversation {
  flex-grow: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.welcome-content {
  max-width: 400px;
}

.welcome-content h2 {
  font-size: 24px;
  color: #4b5563;
  margin-bottom: 16px;
}

.welcome-content p {
  color: #6b7280;
  margin-bottom: 24px;
}

/* 消息区域 */
.chat-messages {
  flex-grow: 1;
  overflow-y: auto;
  padding: 12px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  background-color: #f5f7fa;
}

.chat-messages.with-header {
  height: calc(100% - 50px);
}

.loading-messages,
.empty-messages {
  padding: 24px;
  text-align: center;
  color: #9ca3af;
}

.message {
    display: flex;
    gap: 12px;
  max-width: 85%;
}

.message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message .avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.message .content {
  background-color: #ffffff;
  padding: 12px 16px;
  border-radius: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.message.user .content {
  background-color: #1976D2;
  color: white;
}

.message-header {
    display: flex;
  justify-content: space-between;
  font-size: 12px;
  margin-bottom: 6px;
  color: #9ca3af;
}

.message.user .message-header {
  color: rgba(255, 255, 255, 0.7);
}

.message-text {
  line-height: 1.5;
  font-size: 14px;
}

/* 引用的知识来源 */
.references {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px dashed #e5e7eb;
  font-size: 12px;
}

.reference-title {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #6b7280;
  margin-bottom: 4px;
}

.references ul {
  margin: 4px 0 0 0;
  padding-left: 20px;
  color: #4b5563;
}

/* Markdown样式 */
.markdown-rendered :deep(pre) {
  background-color: #f3f4f6;
  border-radius: 6px;
  padding: 12px;
  overflow-x: auto;
  font-size: 13px;
}

.markdown-rendered :deep(code) {
  background-color: #f3f4f6;
  padding: 2px 4px;
  border-radius: 4px;
  font-size: 0.9em;
}

.markdown-rendered :deep(p) {
  margin: 0.8em 0;
}

.markdown-rendered :deep(ul), .markdown-rendered :deep(ol) {
  padding-left: 1.5em;
  margin: 0.8em 0;
}

.markdown-rendered :deep(h1), .markdown-rendered :deep(h2), .markdown-rendered :deep(h3) {
  margin: 1em 0 0.5em;
}

/* 输入区域 */
.input-area {
  padding: 12px;
  background-color: #fff;
  border-top: 1px solid #e5e7eb;
}

.input-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding: 0 8px;
}

.toolbar-right {
  display: flex;
  gap: 8px;
  align-items: center;
}

.thinking-button {
  transition: all 0.2s ease;
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 12px;
}

.thinking-button .el-icon {
  margin-right: 4px;
}

.thinking-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flat-new-chat-btn, .flat-history-toggle {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #1976D2;
  cursor: pointer;
  transition: color 0.2s;
  border: none;
  padding: 0;
  font-size: 14px;
  font-weight: 500;
}

.flat-new-chat-btn:hover, .flat-history-toggle:hover {
  color: #1565C0;
}

.flat-new-chat-btn .el-icon, .flat-history-toggle .el-icon {
  font-size: 16px;
}

.toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.input-wrapper {
  display: flex;
  gap: 8px;
  align-items: center;
  background-color: #f5f7fa;
  border-radius: 20px;
  padding: 4px 4px 4px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.input-wrapper :deep(.el-textarea__inner) {
  resize: none;
  padding: 8px 0;
  border: none;
  background-color: transparent;
  font-size: 14px;
  box-shadow: none;
}

.input-wrapper :deep(.el-textarea__inner:focus) {
  box-shadow: none;
}

.input-wrapper :deep(.el-textarea__inner::placeholder) {
  color: #9ca3af;
}

.send-button {
  border-radius: 50%;
  width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 36px;
}

.input-tips {
  font-size: 12px;
  color: #9ca3af;
  margin-top: 8px;
  display: flex;
  justify-content: space-between;
}

/* 流式响应开关 */
.stream-switch {
  margin-left: auto;
}

/* 深度思考模式样式 */
.thinking-toggle {
  cursor: pointer;
  font-size: 14px;
  color: #6b7280;
  transition: color 0.2s, font-weight 0.2s;
  border: none;
  padding: 0;
  background-color: transparent;
}

.thinking-toggle:hover {
  color: #1976D2;
  font-weight: bold;
}

.thinking-toggle.active {
  color: #1976D2;
  font-weight: bold;
}

/* 思考过程高亮样式 */
.reasoning-content {
  background: #f8f5ff;
  border-left: 3px solid #7e57c2;
  padding: 10px 12px;
  margin-bottom: 12px;
  border-radius: 4px;
  position: relative;
}

.reasoning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.reasoning-title {
  font-weight: 500;
  color: #7e57c2;
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.reasoning-text {
  color: #555;
  font-size: 13px;
  line-height: 1.5;
  white-space: pre-wrap;
  max-height: 200px;
  overflow-y: auto;
}

/* 添加思考过程加载动画 */
@keyframes thinking {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

.thinking-animation {
  animation: thinking 1.5s infinite;
}

/* 消息操作按钮样式 */
.message-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  padding-right: 4px;
  padding-left: 4px;
}

.action-buttons-group,
.feedback-buttons-group {
  display: flex;
  gap: 4px;
}

.action-button {
  border: 1px solid #e4e7ed;
  background-color: #ffffff;
  padding: 0;
  height: 24px;
  width: 24px;
  font-size: 12px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-button:hover {
  background-color: #f5f7fa;
  color: #409EFF;
  border-color: #c6e2ff;
}

/* 确认框样式 */
:deep(.fullscreen-confirm-dialog) {
  z-index: 2000;
  position: relative;
}

/* 全屏模式下的消息样式 */
:deep(.el-message) {
  z-index: 2001;
}

:deep(.fullscreen-message) {
  z-index: 2001;
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
}

.active-like {
  color: #67c23a !important;
  border-color: #67c23a !important;
  background: #f0f9eb !important;
}
.active-dislike {
  color: #f56c6c !important;
  border-color: #f56c6c !important;
  background: #fef0f0 !important;
}

/* 重新生成回答样式 */
.regenerating-message {
  padding: 8px;
  background-color: #f9f9fb;
  border-radius: 4px;
  margin: 4px 0;
}

.regenerating-text {
  font-size: 13px;
  color: #909399;
  margin-top: 8px;
  text-align: center;
  font-style: italic;
}

.compact-actions {
  display: flex;
  align-items: center;
  gap: 0;
  margin-top: 8px;
  padding: 0 4px;
}
.compact-actions .action-button {
  border: none;
  background: none;
  box-shadow: none;
  width: 28px;
  height: 28px;
  margin: 0 2px;
  color: #888;
  transition: color 0.2s;
}
.compact-actions .action-button:hover {
  color: #409EFF;
  background: none;
}
.compact-actions .divider {
  width: 1px;
  height: 18px;
  background: #e0e0e0;
  margin: 0 6px;
}
.compact-actions .active-like {
  color: #67c23a !important;
}
.compact-actions .active-dislike {
  color: #f56c6c !important;
}
.message .content {
  position: relative;
}
.bubble-actions {
  position: absolute;
  right: -8px;
  bottom: -32px;
  z-index: 2;
  background: transparent;
  box-shadow: none;
}
.message.assistant {
  margin-bottom: 32px;
}
.svg-thumb svg {
  display: block;
}
.thumbs-up, .thumbs-down {
  transition: box-shadow 0.2s, background 0.2s;
  border-radius: 6px;
}
.thumbs-up.active, .thumbs-down.active {
  background: #e6f0fd;
  box-shadow: 0 0 0 2px #409EFF;
}
.thumbs-up.active {
  filter: drop-shadow(0 0 2px #67c23a) brightness(1.2);
}
.thumbs-down.active {
  filter: drop-shadow(0 0 2px #f56c6c) brightness(1.2);
}

/* thumbs-up/down按钮去除focus/active的outline和box-shadow */
.thumbs-up:focus, .thumbs-up:active,
.thumbs-down:focus, .thumbs-down:active {
  outline: none !important;
  box-shadow: none !important;
  border: none !important;
}

/* 统一点赞和点踩按钮激活状态，仅高亮图标颜色，不加背景和box-shadow */
.thumbs-up.active svg,
.thumbs-down.active svg {
  filter: drop-shadow(0 0 2px #1677ff) brightness(1.2);
}
.thumbs-up.active svg {
  fill: #1677ff !important;
}
.thumbs-down.active svg {
  fill: #1677ff !important;
}
.thumbs-up.active,
.thumbs-down.active {
  background: none !important;
  box-shadow: none !important;
}

/* 保证点赞和点踩按钮SVG大小一致 */
.thumbs-up svg {
  width: 18px;
  height: 18px;
  display: block;
}
.thumbs-down svg {
  width: 21px;
  height: 21px;
  display: block;
}
</style>
