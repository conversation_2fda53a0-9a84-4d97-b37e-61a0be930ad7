<template>
    <div class="intervention-plan">
        <div class="intervention-plan__header">
            <div class="intervention-plan__header-content">
                <!-- 背景装饰元素 -->
                <div class="intervention-plan__bg-element-1"></div>
                <div class="intervention-plan__bg-element-2"></div>

                <!-- 内容区域 -->
                <div class="intervention-plan__content-wrapper">
                    <!-- 图标 -->
                    <div class="intervention-plan__icon-wrapper">
                        <svg class="intervention-plan__icon" viewBox="0 0 24 24" fill="none" stroke="white"
                            stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <path
                                d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z">
                            </path>
                        </svg>
                    </div>

                    <!-- 文字内容 -->
                    <div>
                        <h2 class="intervention-plan__header-title">
                            心理危机干预
                        </h2>
                        <p class="intervention-plan__header-description">
                            及早预防、及时疏导、有效干预、快速控制可能出现的心力危机事件
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <div class="intervention-plan__search-container">
            <div class="intervention-plan__search-wrapper">
                <el-input clearable v-model="queryParams.planTitle" class="intervention-plan__search-input"
                    placeholder="请输入标题" />
                <el-button class="intervention-plan__search-button" type="primary" @click="getList">查询</el-button>
            </div>
        </div>
        <div style="display: flex; align-items: center; justify-content: center; width: 100%">
            <div style="width: 80%; padding-bottom: 50px">
                <el-tabs v-loading="loading" v-model="activeName" @tab-click="handleClick">
                    <el-tab-pane label="文字方案" name="first">
                        <div class="intervention-plan__container-header" v-for="item in interventionPlanList" :key="item.planId"
                            @click="openModal(item)">
                            <div class="intervention-plan__container">
                                <div class="intervention-plan__image-container">
                                    <img :src="item.fileUrl" alt="Image" />
                                </div>
                                <div class="intervention-plan__content-container">
                                    <h2 class="intervention-plan__title">{{ item.planTitle }}</h2>
                                    <p class="intervention-plan__content">{{ item.planContent }}</p>
                                    <div class="intervention-plan__meta-info">
                                        <span class="intervention-plan__time">{{ item.releaseTime }}</span>
                                        <span class="intervention-plan__readers">阅读人数: {{ item.previewCount }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                            v-model:limit="queryParams.pageSize" @pagination="getList" />
                    </el-tab-pane>
                    <el-tab-pane label="视频方案" name="second" v-if="false">
                        <div class="intervention-plan__view-container">
                            <div class="intervention-plan__view-content" v-for="item in interventionPlanList" :key="item.planId">
                                <div>
                                    <video :src="item.fileUrl" :controls="true"></video>
                                </div>
                                <div>
                                    <h2 class="intervention-plan__video-title">{{ item.planTitle }}</h2>
                                </div>
                            </div>
                        </div>
                        <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                            v-model:limit="queryParams.pageSize" @pagination="getList" />
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>

        <el-dialog v-model="dialog.visible" width="1200px" append-to-body :before-close="handleClose">
            <!-- 弹窗 -->
            <div class="intervention-plan__dialog-content">
                <div class="intervention-plan__dialog-title">{{ dialog.title }}</div>
                <div class="intervention-plan__dialog-info">{{ dialog.releaseTime }} &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;预览人数:{{
                    dialog.previewCount }}</div>
                <div class="intervention-plan__plan-content">{{ dialog.planContent }}</div>
            </div>
        </el-dialog>
    </div>
</template>

<script setup name="InterventionHotline" lang="ts">
import { InterventionHotlineVO, InterventionHotlineQuery } from '@/api/app/interventionHotline/types';
import { queryListByPid } from '@/api/app/interventionHotline';
import { listInterventionPlan, updateInterventionPlan } from '@/api/app/interventionPlan';
import { InterventionPlanVO, InterventionPlanQuery, InterventionPlanForm } from '@/api/app/interventionPlan/types';
import type { TabsPaneContext } from 'element-plus'

const interventionHotlineList = ref<InterventionHotlineVO[]>([]);
const loading = ref(false);
const activeName = ref('first');

// 确保查询参数符合InterventionPlanQuery类型
const queryParams = ref<InterventionPlanQuery>({
    pageNum: 1,
    pageSize: 9,
    planType: 0,
    publishStatus: 1,
    planTitle: undefined,
    orderByColumn: 'createTime',
    isAsc: 'desc'
});

const total = ref(0);
const districtData = ref<string | number>(16);

const interventionPlanList = ref<InterventionPlanVO[]>([]);

// 确保表单对象符合InterventionPlanForm类型
const InterventionPlanFormVo = reactive<Partial<InterventionPlanForm>>({
    previewCount: 0,
    publishStatus: 1 // 添加必需的字段
});

interface ExtendedDialogOption {
    visible: boolean;
    title: string;
    planContent: string;
    releaseTime: string;
    previewCount: string | number;
    isView?: boolean;
}

const dialog = reactive<ExtendedDialogOption>({
    visible: false,
    title: '',
    planContent: '',
    releaseTime: '',
    previewCount: ''
});

/** 查询危机干预热线列表 */
const getList = async () => {
    interventionPlanList.value = [];
    loading.value = true;
    try {
        const res = await listInterventionPlan(queryParams.value);
        interventionPlanList.value = res.rows;
        total.value = res.total;
    } catch (error) {
        console.error('获取列表失败:', error);
    } finally {
        loading.value = false;
    }
};

/** 查询危机干预热线列表 */
const init = async () => {
    try {
        const res = await queryListByPid(queryParams.value as any);
    } catch (error) {
        console.error('初始化失败:', error);
    }
};

// 修改changeDistrict函数，确保districtId存在于queryParams
const changeDistrict = async () => {
    // 使用类型断言确保districtId可以被赋值
    (queryParams.value as any).districtId = districtData.value;
    getList();
};

const handleClick = (tab: TabsPaneContext, event: Event) => {
    if (tab.props.name == 'first') {
        queryParams.value.pageSize = 12;
        queryParams.value.planType = 0;
    } else {
        queryParams.value.planType = 1;
        queryParams.value.pageSize = 6;
    }
    getList();
};

/** 关闭dialog前置事件 */
const handleClose = (done) => {
    getList();
    dialog.visible = false;
    done();
};

const openModal = (item: InterventionPlanVO) => {
    dialog.visible = true;
    dialog.title = item.planTitle;
    dialog.planContent = item.planContent;
    dialog.releaseTime = item.releaseTime;
    dialog.previewCount = item.previewCount;
    
    // 确保表单对象包含所有必需的字段
    InterventionPlanFormVo.previewCount = item.previewCount + 1;
    InterventionPlanFormVo.planId = item.planId;
    
    // 使用类型断言确保updateInterventionPlan接受部分表单
    updateInterventionPlan(InterventionPlanFormVo as InterventionPlanForm);
};

onMounted(() => {
    loading.value = true;
    Promise.all([init(), getList()]).finally(() => {
        loading.value = false;
    });
});
</script>
<style lang="scss" scoped>
.intervention-plan {
    &__header {
        display: flex;
        justify-content: center;
        align-items: center;
        background: linear-gradient(135deg, #87ceeb 0%, #00bfff 100%);
    }

    &__header-content {
        position: relative;
        width: 80%;
        height: 180px;
        display: flex;
        align-items: center;
        padding: 0 40px;
        overflow: hidden;
        font-family: 'Microsoft YaHei', sans-serif;
        backdrop-filter: blur(5px);
    }

    &__bg-element-1 {
        position: absolute;
        width: 300px;
        height: 300px;
        background-color: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        top: -50px;
        left: -100px;
        z-index: 1;
    }

    &__bg-element-2 {
        position: absolute;
        width: 150px;
        height: 150px;
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 50%;
        bottom: -30px;
        right: -50px;
        z-index: 1;
    }

    &__content-wrapper {
        display: flex;
        align-items: center;
        z-index: 2;
    }

    &__icon-wrapper {
        background: rgba(255, 255, 255, 0.2);
        width: 120px;
        height: 120px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 30px;
        backdrop-filter: blur(5px);
        border: 2px solid rgba(255, 255, 255, 0.3);
    }

    &__icon {
        width: 60px;
        height: 60px;
    }

    &__header-title {
        font-size: 38px;
        color: white;
        margin: 0 0 8px 0;
        font-weight: 600;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    &__header-description {
        font-size: 22px;
        color: rgba(255, 255, 255, 0.9);
        margin: 0;
        line-height: 1.4;
    }

    &__search-container {
        margin-top: 20px;
        display: flex;
        justify-content: center;
    }

    &__search-wrapper {
        width: 80%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        z-index: 999;
    }

    &__search-input {
        width: 240px;
        margin-right: 10px;
    }

    &__search-button {
        width: 100px;
    }

    &__container-header {
        width: 100%;
        transition: background-color 0.3s ease;
        background-color: #f5f7fa;
        border-radius: 8px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        
        &:hover {
            background-color: #f0f0f0;
            cursor: pointer;
            box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.15);
        }
    }

    &__content {
        margin: 10px 0 0 0;
        font-size: 14px;
        color: #666;
        display: -webkit-box;
        /* 使用弹性盒子布局 */
        -webkit-box-orient: vertical;
        /* 垂直方向排列 */
        -webkit-line-clamp: 1;
        /* 限制显示两行 */
        overflow: hidden;
        /* 超出部分隐藏 */
        text-overflow: ellipsis;
        /* 超出部分显示省略号 */
    }

    &__image-container {
        flex: 0 0 200px;
        /* 图片宽度 */
        margin-right: 50px;
        
        img {
            width: 100%;
            height: 100%;
            /* 图片高度与容器一致 */
            object-fit: cover;
            /* 保持图片比例，裁剪多余部分 */
            border-radius: 8px;
            /* 可选：图片圆角 */
        }
    }

    &__content-container {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    &__title {
        margin: 20px 0 10px 0;
        font-size: 18px;
        font-weight: bold;
    }

    &__meta-info {
        margin-top: 60px;
        /* 将 .meta-info 推到最底部 */
        font-size: 12px;
        color: #999;
    }

    &__time {
        margin-right: 10px;
    }

    &__container {
        display: flex;
        align-items: stretch;
        margin-bottom: 20px;
        margin-top: 20px;
        transition: background-color 0.3s ease;
        cursor: pointer;
    }

    &__dialog-content {
        display: flex;
        flex-direction: column;
        align-items: center;
        /* 内容水平居中 */
        height: 600px;
        /* 确保内容占据整个高度 */
    }

    &__dialog-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 10px;
    }

    &__dialog-info {
        font-size: 16px;
        color: #666;
        margin-bottom: 20px;
    }

    &__plan-content {
        white-space: pre-line;
        align-self: flex-start;
        /* 左对齐 */
        width: 100%;
        /* 确保宽度与父容器一致 */
    }

    &__view-container {
        display: flex;
        flex-wrap: wrap;
    }

    &__view-content {
        flex: 0 0 calc(33.33% - 10px);
        /* 每行3列，减去间距 */
        margin-bottom: 30px;
        /* 行间距 */
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px;
    }

    &__video-title {
        text-align: left;
        /* 标题靠左对齐 */
        font-family: 'Arial', sans-serif;
        /* 设置字体 */
        font-size: 18px;
        /* 字体大小 */
        font-weight: 600;
        /* 字体粗细 */
        color: #333;
        /* 字体颜色 */
        margin: 0;
        /* 去除默认的外边距 */
        padding: 10px 0;
        /* 上下内边距 */
        letter-spacing: 0.5px;
        /* 字间距 */
        line-height: 1.5;
        /* 行高 */
    }
}

/* 确保样式一致性的额外规则 */
:deep(.el-tabs__content) {
    overflow: visible;
}
</style>
