package org.senyor.ai.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.senyor.ai.domain.AiKnowledgeChunk;

import java.util.List;

/**
 * AI知识库分块Mapper接口
 *
 * <AUTHOR>
 */
@Mapper
public interface AiKnowledgeChunkMapper extends BaseMapper<AiKnowledgeChunk> {

    /**
     * 从指定知识库中检索相关分块
     *
     * @param knowledgeId 知识库ID
     * @param query 查询内容
     * @param topK 返回数量
     * @return 相关分块列表
     */
    List<AiKnowledgeChunk> selectRelevantChunks(@Param("knowledgeId") Long knowledgeId, 
                                               @Param("query") String query, 
                                               @Param("topK") int topK);

    /**
     * 从指定知识库中检索相关分块（支持关键词数组）
     */
    List<AiKnowledgeChunk> selectRelevantChunksWithKeywords(@Param("knowledgeId") Long knowledgeId,
                                                           @Param("query") String query,
                                                           @Param("keywords") List<String> keywords,
                                                           @Param("topK") int topK);

    /**
     * 从所有知识库中检索相关分块
     *
     * @param query 查询内容
     * @param topK 返回数量
     * @return 相关分块列表
     */
    List<AiKnowledgeChunk> selectRelevantChunksFromAll(@Param("query") String query, 
                                                      @Param("topK") int topK);

    /**
     * 从所有知识库中检索相关分块（支持关键词数组）
     */
    List<AiKnowledgeChunk> selectRelevantChunksFromAllWithKeywords(@Param("query") String query,
                                                                  @Param("keywords") List<String> keywords,
                                                                  @Param("topK") int topK);

    /**
     * 根据文档ID查询分块列表
     *
     * @param documentId 文档ID
     * @return 分块列表
     */
    List<AiKnowledgeChunk> selectChunksByDocumentId(@Param("documentId") Long documentId);

    /**
     * 根据知识库ID查询分块列表
     *
     * @param knowledgeId 知识库ID
     * @return 分块列表
     */
    List<AiKnowledgeChunk> selectChunksByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 删除文档的所有分块
     *
     * @param documentId 文档ID
     * @return 删除数量
     */
    int deleteChunksByDocumentId(@Param("documentId") Long documentId);

    /**
     * 删除知识库的所有分块
     *
     * @param knowledgeId 知识库ID
     * @return 删除数量
     */
    int deleteChunksByKnowledgeId(@Param("knowledgeId") Long knowledgeId);

    /**
     * 模糊匹配检索（指定知识库）
     *
     * @param knowledgeId 知识库ID
     * @param query 查询内容
     * @param keywords 关键词列表
     * @param topK 返回数量
     * @return 相关分块列表
     */
    List<AiKnowledgeChunk> selectChunksByFuzzyMatch(@Param("knowledgeId") Long knowledgeId,
                                                   @Param("query") String query,
                                                   @Param("keywords") List<String> keywords,
                                                   @Param("topK") int topK);

    /**
     * 模糊匹配检索（所有知识库）
     *
     * @param query 查询内容
     * @param keywords 关键词列表
     * @param topK 返回数量
     * @return 相关分块列表
     */
    List<AiKnowledgeChunk> selectAllChunksByFuzzyMatch(@Param("query") String query,
                                                      @Param("keywords") List<String> keywords,
                                                      @Param("topK") int topK);
} 