package org.senyor.statistics.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.senyor.statistics.domain.vo.GradeInfoCarouselVo;
import org.senyor.statistics.domain.vo.WarningStatusStatisticsVo;

import java.util.List;
import java.util.Map;


@Mapper
public interface HomepageDataStatisticsMapper {

    Map<String, Long> getWarningUserCount();

    Map<String, Long> getNewUserCount();

    /**
     * 获取部门基本信息
     */
    List<GradeInfoCarouselVo> getDeptBaseInfo();

    /**
     * 获取部门学生数量
     */
    Integer getDeptStudentCount(@Param("deptId") Long deptId);

    /**
     * 获取部门班级数量
     */
    Integer getDeptClassCount(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

    /**
     * 获取部门预警人数
     */
    Integer getDeptWarningCount(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

    /**
     * 获取部门心理老师数量
     */
    Integer getDeptTeacherCount(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

    /**
     * 获取部门预警级别分布
     */
    List<Map<String, Object>> getDeptWarningLevelDistribution(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

    /**
     * 获取部门所有预警记录
     */
    List<Map<String, Object>> getDeptWarningRecords(@Param("deptId") Long deptId, @Param("tenantId") String tenantId);

    /**
     * 批量获取多个部门的学生数量
     */
    List<Map<String, Object>> batchGetDeptStudentCount(@Param("deptIds") List<Long> deptIds);

    /**
     * 批量获取多个部门的班级数量
     */
    List<Map<String, Object>> batchGetDeptClassCount(@Param("deptIds") List<Long> deptIds);

    /**
     * 批量获取多个部门的预警人数
     */
    List<Map<String, Object>> batchGetDeptWarningCount(@Param("deptIds") List<Long> deptIds);

    /**
     * 批量获取多个部门的心理老师数量
     */
    List<Map<String, Object>> batchGetDeptTeacherCount(@Param("deptIds") List<Long> deptIds);

    /**
     * 批量获取多个部门的所有预警记录
     */
    List<Map<String, Object>> batchGetDeptWarningRecords(@Param("deptIds") List<Long> deptIds);

    /**
     * 获取最大学生数量
     */
    String getMaxStudentCount(@Param("tenantId") String tenantId);

    /**
     * 获取预警状态统计数据
     */
    List<Map<String, Object>> getWarningStatusStatistics(@Param("tenantId") String tenantId);
}
