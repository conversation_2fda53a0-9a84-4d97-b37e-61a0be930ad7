<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>org.senyor</groupId>
        <artifactId>senyor-modules</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>senyor-scale</artifactId>

    <description>
        量表管理模块
    </description>

    <dependencies>

        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-system</artifactId>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-scale</artifactId>
        </dependency>

        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-doc</artifactId>
        </dependency>

        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.deepoove</groupId>
            <artifactId>poi-tl</artifactId>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-export-fo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sun.xml.bind</groupId>
            <artifactId>jaxb-impl</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.xml.bind</groupId>
            <artifactId>jakarta.xml.bind-api</artifactId>
        </dependency>



        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.xmlgraphics</groupId>
            <artifactId>fop</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
        </dependency>

        <dependency>
            <groupId>org.jfree</groupId>
            <artifactId>jfreechart</artifactId>
        </dependency>

        <dependency>
            <groupId>xalan</groupId>
            <artifactId>xalan</artifactId>
        </dependency>
        <dependency>
            <groupId>net.sf.saxon</groupId>
            <artifactId>saxon</artifactId>
        </dependency>

    </dependencies>

</project>
