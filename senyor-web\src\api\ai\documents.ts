import request from '@/utils/request'

// 查询AI知识库文档列表
export function listDocuments(query) {
  return request({
    url: '/ai/documents/list',
    method: 'get',
    params: query
  })
}

// 查询AI知识库文档详细
export function getDocument(documentId) {
  return request({
    url: '/ai/documents/' + documentId,
    method: 'get'
  })
}

// 新增AI知识库文档
export function addDocument(data) {
  return request({
    url: '/ai/documents',
    method: 'post',
    data: data
  })
}

// 修改AI知识库文档
export function updateDocument(data) {
  return request({
    url: '/ai/documents',
    method: 'put',
    data: data
  })
}

// 删除AI知识库文档
export function delDocument(documentId) {
  return request({
    url: '/ai/documents/' + documentId,
    method: 'delete'
  })
}

// 上传文档
export function uploadDocument(data) {
  return request({
    url: '/ai/documents/upload',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 重新处理文档
export function reprocessDocument(documentId) {
  return request({
    url: '/ai/documents/reprocess/' + documentId,
    method: 'put'
  })
}

// 更新文档状态
export function updateDocumentStatus(documentId, status) {
  return request({
    url: '/ai/documents/status/' + documentId + '/' + status,
    method: 'put'
  })
}

// 更新文档进度
export function updateDocumentProgress(documentId, progress) {
  return request({
    url: '/ai/documents/progress/' + documentId + '/' + progress,
    method: 'put'
  })
}

// 向量化文档
export function vectorizeDocument(documentId) {
  return request({
    url: '/ai/chat/vector/vectorize/' + documentId,
    method: 'post'
  })
}

// 批量向量化文档
export function batchVectorizeDocuments(documentIds) {
  return request({
    url: '/ai/chat/vector/batch-vectorize',
    method: 'post',
    data: documentIds
  })
}

// 向量化知识库下的所有文档
export function vectorizeKnowledgeBase(knowledgeId) {
  return request({
    url: '/ai/chat/vector/vectorize-knowledge/' + knowledgeId,
    method: 'post'
  })
}

// 重新向量化文档
export function reVectorizeDocument(documentId) {
  return request({
    url: '/ai/chat/vector/re-vectorize/' + documentId,
    method: 'post'
  })
}

// 获取向量化进度
export function getVectorizationProgress(documentId) {
  return request({
    url: '/ai/chat/vector/progress/' + documentId,
    method: 'get'
  })
} 