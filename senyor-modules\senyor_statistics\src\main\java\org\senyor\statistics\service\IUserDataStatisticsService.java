package org.senyor.statistics.service;

import org.senyor.statistics.domain.vo.*;

import java.util.List;
import java.util.Map;

/**
 * 用户数据统计
 */
public interface IUserDataStatisticsService {

    /**
     * 根据层级和区域ID查询该地区的统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    UserBaseDataStatisticsVo getUserBaseStats(String level, Integer levelId);

    /**
     * 人员状况分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    UserConditionDataStatisticsVo getUserConditionStats(String level, Integer levelId);

    /**
     * 科普解压 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    RelaxationStatisticsVo getRelaxationStatistics(String level, Integer levelId, String flagStr);

    /**
     * 附属单位预警分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    List<TenantWarningStatisticsVo> getTenantWarningStats(String level, Integer levelId);

    /**
     * 心理咨询统计 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    ConsultationRecordsStatisticsVo getConsultationRecordsStats(String level, Integer levelId);

    /**
     * 测评活动分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    ScaleWarningRecordsStatisticsVo getScaleWarningRecords(String level, Integer levelId);

    /**
     * 获取用户当前机构的信息 地区级别,adCode等
     * @param tenantId 机构编号
     * */
    TenantBaseCodeVo getTenantBaseCode(String tenantId);

    /**
     * 各区域人数 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    Map<String, Long> getUserNumOnLevelCode(String level, Integer levelId);

    /**
     * 区域内的机构具体信息 统计
     * @param level 层级
     * @param levelId 区域ID
     * */
    List<TenantsResultObjVo> getTenantsInArea(String level, Integer levelId);
}
