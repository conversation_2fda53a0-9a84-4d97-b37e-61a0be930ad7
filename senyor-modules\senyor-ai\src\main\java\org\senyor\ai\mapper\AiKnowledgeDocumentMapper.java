package org.senyor.ai.mapper;

import java.util.List;
import org.apache.ibatis.annotations.Param;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.domain.vo.AiKnowledgeDocumentVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * AI知识库文档数据层
 * 
 * <AUTHOR>
 */
public interface AiKnowledgeDocumentMapper extends BaseMapper<AiKnowledgeDocument> {
    
    /**
     * 查询单个文档
     * 
     * @param documentId 文档ID
     * @return AI文档对象
     */
    AiKnowledgeDocumentVo selectAiKnowledgeDocumentById(Long documentId);
    
    /**
     * 根据知识库ID查询文档列表
     * 
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    List<AiKnowledgeDocumentVo> selectAiKnowledgeDocumentsByKnowledgeId(Long knowledgeId);
    
    /**
     * 查询所有文档
     * 
     * @param aiKnowledgeDocument 查询参数
     * @return 文档列表
     */
    List<AiKnowledgeDocumentVo> selectAiKnowledgeDocumentList(AiKnowledgeDocument aiKnowledgeDocument);
    
    /**
     * 根据知识库ID统计文档数量
     * 
     * @param knowledgeId 知识库ID
     * @return 文档数量
     */
    int countDocumentsByKnowledgeId(Long knowledgeId);
    
    /**
     * 根据知识库ID删除文档
     * 
     * @param knowledgeId 知识库ID
     * @return 结果
     */
    int deleteDocumentsByKnowledgeId(Long knowledgeId);
} 
 