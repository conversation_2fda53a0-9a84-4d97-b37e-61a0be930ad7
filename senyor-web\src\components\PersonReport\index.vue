<template>
    <div style="width: 100%; height: 50px; text-align: center; line-height: 50px; font-size: 18px; font-weight: bold">
        {{ userInfo?.userName }}个体报告
        <span style="float: right">
            <el-button v-hasPermi="['scale:scaleAnswer:exportWord']" style="background-color: #578bf9; color: white" @click="handleExportWord"
                >导出Word</el-button
            >
        </span>
        <span style="float: right">
            <el-button v-hasPermi="['scale:scaleAnswer:exportPDF']" style="background-color: #57c9f9; color: white" @click="handleExportPDF"
                >导出PDF</el-button
            >
        </span>
        <div style="border: 3px solid #578bf9"></div>
    </div>
    <div style="display: flex; align-items: center; background-color: #e7efff; border-radius: 12px; margin-top: 15px">
        <img src="@/assets/images/doctor.png" style="width: 110px; height: 110px; margin-left: 20px" />
        <el-row style="background-color: #e7efff; border-radius: 12px">
            <el-col :span="6">
                <div><span style="color: #409eff; font-weight: bold">部门名称：</span>{{ userInfo?.deptName ?? '暂无' }}</div>
            </el-col>
            <el-col :span="6">
                <div><span style="color: #409eff; font-weight: bold">年龄：</span>{{ userInfo?.age ?? '暂无' }}</div>
            </el-col>
            <el-col :span="6">
                <div><span style="color: #409eff; font-weight: bold">性别：</span>{{ getDictLabel(sys_user_sex, userInfo?.sex) }}</div>
            </el-col>
            <el-col :span="6">
                <div><span style="color: #409eff; font-weight: bold">邮箱：</span>{{ userInfo?.email ?? '暂无' }}</div>
            </el-col>
            <el-col :span="5">
                <div><span style="color: #409eff; font-weight: bold">电话：</span>{{ userInfo?.phonenumber ?? '暂无' }}</div>
            </el-col>
            <el-col :span="8">
                <div><span style="color: #409eff; font-weight: bold">测评时间：</span>{{ scaleAnswerVo?.createTime ?? '暂无' }}</div>
            </el-col>
            <el-col :span="11">
                <div><span style="color: #409eff; font-weight: bold">报告生成时间：</span>{{ scaleRecordVoList?.[0]?.createTime ?? '暂无' }}</div>
            </el-col>
        </el-row>
    </div>
    <div style="margin-top: 40px; width: 100%; height: 50px; border-radius: 10px; background-color: var(--el-color-primary)">
        <div style="font-size: 16px; text-align: center; line-height: 50px; color: white; font-family: '楷体', 'KaiTi', 'STKaiti', serif">
            {{ scaleInfoVo?.scaleName }}
        </div>
    </div>
    <div style="border: 1px solid #eef3ff; border-radius: 12px; background-color: #eef3ff; margin-top: 20px">
        <div class="title">量表介绍</div>
        <div class="content">
            {{ scaleInfoVo?.introduction }}
        </div>
    </div>
    <div style="border: 1px solid #eef3ff; border-radius: 12px; background-color: #eef3ff; margin-top: 20px">
        <div class="title">量表描述</div>
        <div class="content">
            {{ scaleInfoVo?.description }}
        </div>
    </div>
    <div style="border-radius: 12px; background-color: #abc5fc; margin-top: 20px">
        <div class="title">因子分析</div>
        <div style="width: 100%; overflow-x: auto; padding: 10px">
            <el-table :data="transposedData" border style="border-radius: 12px" :show-header="false">
                <!-- 左侧标题列 -->
                <el-table-column prop="title" label="标题" width="120" class-name="vertical-title-cell">
                    <template #default="{ row }">
                        <div class="vertical-title">
                            {{ row.title }}
                        </div>
                    </template>
                </el-table-column>

                <!-- 动态生成数据列（纵向展示的数据） -->
                <el-table-column v-for="(item, index) in transposedData[0].values" :key="index" :prop="'values.' + index">
                    <template #default="{ row }">
                        <div class="vertical-content">
                            {{ row.values[index] }}
                        </div>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div style="border: 4px solid #6594f9; border-radius: 12px; margin: 10px; background-color: #f1f6ff">
            <div v-for="(item, index) in scaleRecordVoList" :key="index">
                <h3>【{{ item.factor }}】</h3>
                <p><span style="color: #6594f9; padding: 10px">分数级别：</span>{{ item.appraiseTitle }} 得分:{{ item.score }}</p>
                <div style="color: #6594f9; padding: 10px">评价标题：</div>
                <div style="padding: 10px">{{ item.appraiseIntro }}</div>
                <div style="color: #6594f9; padding: 10px">评价简述：</div>
                <div style="padding: 10px">{{ item?.appraiseContent ?? '暂无' }}</div>
            </div>
        </div>
    </div>
    <div v-if="hasWarning" style="border: 2px dashed #eef3ff; border-radius: 8px; background-color: #eef3ff; margin-top: 20px">
        <div class="title">预警信息</div>
        <div style="text-indent: 2em; padding: 8px">
            <div v-for="(item, index) in scaleEarlyWarningRecordVoList" :key="index">
                <p style="padding: 3px 5px">预警名称：{{ item.warningGroupName }}</p>
                <p style="padding: 3px 5px">预警级别：{{ item.warningLevelName ?? '暂无' }}</p>
                <p style="padding: 3px 5px">预警级别解释：{{ item.warningIntro ?? '暂无' }}</p>
                <p style="padding: 3px 5px">预警级别建议：{{ item.warningSuggestions ?? '暂无' }}</p>
                <p style="padding: 3px 5px">备注：{{ item.remark ?? '暂无' }}</p>
            </div>
        </div>
    </div>
    <div style="border: 1px solid #eef3ff; border-radius: 8px; background-color: #eef3ff; margin-top: 20px">
        <div class="title">答题情况</div>

        <div v-if="!answerData.length" style="color: red; padding: 10px">无答题数据</div>

        <div v-else style="margin-top: 20px; overflow-x: auto; background-color: #eef3ff">
            <table
                v-for="(chunk, chunkIndex) in chunkArray(answerData, 15)"
                :key="'chunk-' + chunkIndex"
                style="border-collapse: collapse; width: 100%; margin-bottom: 20px"
            >
                <tr>
                    <th
                        v-for="item in chunk"
                        :key="'q-' + item.questionId"
                        style="border: 1px solid #ddd; padding: 8px; text-align: center; background-color: #eef3ff"
                    >
                        {{ item.questionId }}
                    </th>
                </tr>
                <tr>
                    <th
                        v-for="item in chunk"
                        :key="'a-' + item.questionId"
                        style="border: 1px solid #ddd; padding: 8px; text-align: center; color: #409eff"
                    >
                        {{ item.key }}
                    </th>
                </tr>
            </table>
        </div>
    </div>
    <div style="text-align: right; margin-top: 20px">签名:_____________ 报告日期：{{ scaleRecordVoList?.[0]?.createTime ?? '暂无' }}</div>
    <div style="text-align: right; margin-top: 10px">（本报告仅供临床参考，不作诊断证明之用）</div>
</template>
<script setup name="WarningRecord" lang="ts">
import { WarningRecordVO, WarningRecordQuery, WarningRecordForm } from '@/api/scale/warningRecord/types';
import { getUser } from '@/api/system/user';
import { UserInfo, UserVO, UserForm, UserQuery } from '@/api/system/user/types';
import { listScaleRecord, getReportInfo, exportWord, exportPDF } from '@/api/scale/scaleRecord';
import { ScaleReportVo } from '@/api/scale/assessPlan/types';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_user_sex } = toRefs<any>(proxy?.useDict('sys_user_sex'));
const userInfo = ref<UserVO>();
const reportInfo = ref<ScaleReportVo>();
const showReportVisible = ref(false);
// const getDictLabel = (dictOptions:any[],value:any) =>{
//   const item = dictOptions.find(item=>item.value === value);
//   return item?item.label:value;
// }
const getDictLabel = (dictOptions, value) => {
    if (!Array.isArray(dictOptions)) {
        return '未知';
    }
    const item = dictOptions.find((item) => item.value === value);
    return item?.label || value;
};
/** 查看报告*/
const checkReport = async (row?: WarningRecordVO) => {
    showReportVisible.value = true;
    const userId = row.userId;
    const res = await getUser(userId);
    userInfo.value = res.data.user;
    //查询报告相关信息
    const reportInfo = await getReportInfo(row.answerId);
};
const getUserInfo = async () => {
    const res = await getUser(props.userId);
    userInfo.value = res.data.user;
};

const getRecordInfo = async () => {
    const res2 = await getReportInfo(props.answerId, props.scaleId);
    reportInfo.value = res2.data;
    scaleInfoVo.value = res2.data.scaleInfoVo;
    scaleAnswerVo.value = res2.data.scaleAnswerVo;
    scaleRecordVoList.value = res2.data.scaleRecordVoList;
    scaleEarlyWarningRecordVoList.value = res2.data.scaleEarlyWarningRecordVoList;
};
const answerData = computed(() => {
    if (!scaleAnswerVo.value?.answer) return [];
    try {
        let data = scaleAnswerVo.value.answer;
        if (typeof data === 'string') {
            data = JSON.parse(data);
        }
        if (!Array.isArray(data)) {
            console.error('答题数据不是数组格式：', data);
            return [];
        }
        return data;
    } catch (e) {
        console.error('解析答题数据出错：', e);
        return [];
    }
});
const chunkArray = (arr: any[], chunkSize: number) => {
    const chunks = [];
    for (let i = 0; i < arr.length; i += chunkSize) {
        chunks.push(arr.slice(i, i + chunkSize));
    }
    return chunks;
};
const scaleInfoVo = ref();
const scaleAnswerVo = ref();
const scaleRecordVoList = ref();
const scaleEarlyWarningRecordVoList = ref();
const props = defineProps({
    answerId: {
        type: [String, Number],
        required: true
    },
    userId: {
        type: [String, Number],
        required: true
    },
    showWarning: {
        type: Boolean,
        default: true //默认展示
    },
    scaleId: {
        type: [String, Number],
        required: true
    }
});

// 转置数据：将行转为列
const transposedData = computed(() => {
    if (!scaleRecordVoList.value || scaleRecordVoList.value.length === 0) {
        return [
            { title: '因子', values: [] },
            { title: '得分', values: [] },
            { title: '结果', values: [] }
        ];
    }

    return [
        {
            title: '因子',
            values: scaleRecordVoList.value.map((item) => item.factor)
        },
        {
            title: '得分',
            values: scaleRecordVoList.value.map((item) => item.score)
        },
        {
            title: '结果',
            values: scaleRecordVoList.value.map((item) => item.appraiseTitle)
        }
    ];
});

const hasWarning = computed(() => {
    return props.showWarning && scaleEarlyWarningRecordVoList.value?.length > 0;
});
/**
 * 导出word
 */
const handleExportWord = async () => {
    try {
        // const hasWarning = scaleEarlyWarningRecordVoList.value?.length > 0; //判断是否含有预警信息

        const exportData = {
            //用户信息
            userInfo: {
                userName: userInfo.value?.userName || '暂无',
                deptName: userInfo.value?.deptName || '暂无',
                age: userInfo.value?.age || '暂无',
                sex: getDictLabel(sys_user_sex, userInfo.value?.sex),
                email: userInfo.value?.email || '暂无',
                phonenumber: userInfo.value?.phonenumber || '暂无'
            },
            //量表信息
            scaleInfo: {
                scaleName: scaleInfoVo.value?.scaleName || '暂无',
                introduction: scaleInfoVo.value?.introduction || '暂无',
                description: scaleInfoVo.value?.description || '暂无'
            },

            //详细评价
            evaluations:
                scaleRecordVoList.value?.map((item) => ({
                    factor: item.factor || '暂无',
                    score: Number(item.score) || 0,
                    appraiseTitle: item.appraiseTitle || '暂无',
                    appraiseContent: item.appraiseContent || '暂无',
                    appraiseIntro: item.appraiseIntro || '暂无',
                    createTime: item.createTime || '无'
                })) || [],
            //预警信息
            scaleEarlyWarningRecordVoList: hasWarning.value
                ? scaleEarlyWarningRecordVoList.value?.map((item) => ({
                      warningGroupName: item.warningGroupName || '暂无',
                      warningLevelName: item.warningLevelName || '暂无',
                      warningIntro: item.warningIntro || '暂无',
                      warningSuggestions: item.warningSuggestions || '暂无',
                      remark: item.remark || '暂无'
                  }))
                : undefined,
            //答题情况
            answers: {
                answer: scaleAnswerVo.value?.answer
            }
        };
        const dictType = 'sys_file_template'; //字典类型
        const dictLabel = hasWarning.value ? '个人报告' : '个人报告（不含预警）'; //字典标签
        //提交导出任务
        const response = await exportWord(exportData, dictType, dictLabel);
        if (response instanceof Blob) {
            const jsonText = await response.text();
            const data = JSON.parse(jsonText);
            if (data.code === 200) {
                ElMessage.success(`导出任务已提交，请到下载任务列表查看`);
            } else {
                ElMessage.warning(data.msg);
            }
        }
    } catch (error) {
        console.log('导出失败', error);
        ElMessage.error('提交导出任务失败，请稍后重试');
    }
};
/**
 * 导出PDF
 */
const handleExportPDF = async () => {
    try {
        const exportData = {
            //用户信息
            userInfo: {
                userName: userInfo.value?.userName || '暂无',
                deptName: userInfo.value?.deptName || '暂无',
                age: userInfo.value?.age || '暂无',
                sex: getDictLabel(sys_user_sex, userInfo.value?.sex),
                email: userInfo.value?.email || '暂无',
                phonenumber: userInfo.value?.phonenumber || '暂无'
            },
            //量表信息
            scaleInfo: {
                scaleName: scaleInfoVo.value?.scaleName || '暂无',
                introduction: scaleInfoVo.value?.introduction || '暂无',
                description: scaleInfoVo.value?.description || '暂无'
            },

            //详细评价
            evaluations:
                scaleRecordVoList.value?.map((item) => ({
                    factor: item.factor || '暂无',
                    score: Number(item.score) || 0,
                    appraiseTitle: item.appraiseTitle || '暂无',
                    appraiseContent: item.appraiseContent || '暂无',
                    appraiseIntro: item.appraiseIntro || '暂无',
                    createTime: item.createTime || '无'
                })) || [],
            //预警信息
            scaleEarlyWarningRecordVoList: hasWarning.value
                ? scaleEarlyWarningRecordVoList.value?.map((item) => ({
                      warningGroupName: item.warningGroupName || '暂无',
                      warningLevelName: item.warningLevelName || '暂无',
                      warningIntro: item.warningIntro || '暂无',
                      warningSuggestions: item.warningSuggestions || '暂无',
                      remark: item.remark || '暂无'
                  }))
                : undefined,
            //答题情况
            answers: {
                answer: scaleAnswerVo.value?.answer
            }
        };
        const dictType = 'sys_file_template'; //字典类型
        const dictLabel = hasWarning.value ? '个人报告' : '个人报告（不含预警）'; //字典标签
        //提交导出任务
        const response = await exportPDF(exportData, dictLabel, dictType);
        if (response instanceof Blob) {
            const jsonText = await response.text();
            const data = JSON.parse(jsonText);
            if (data.code === 200) {
                ElMessage.success(`导出任务已提交，请到下载任务列表查看`);
            } else {
                ElMessage.warning('收到未知格式响应');
            }
        }
    } catch (error) {
        console.log('导出失败', error);
        ElMessage.error('提交导出任务失败，请稍后重试');
    }
};
watch(
    [() => props.answerId, () => props.userId],
    ([newOptions, newData]) => {
        getUserInfo();
        getRecordInfo();
    },
    { deep: true }
);

onMounted(async () => {
    getUserInfo();
    getRecordInfo();
});
</script>
<style>
.medical-report-header {
    width: 100%;
    background: linear-gradient(135deg, #f0f9ff, #d0ebff);
    font-family: 'Arial', sans-serif;
    padding: 25px;
    color: black;
    box-sizing: border-box;
    border-radius: 4px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    position: relative;
    overflow: hidden;
}
.hospital-logo {
    position: absolute;
    top: 20px;
    left: 25px;
    width: 60px;
    height: 60px;
    background-color: #4dabf7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: clamp(1px, 2vw, 16px);
    box-shadow: 0 2px 8px rgba(77, 171, 247, 0.3);
}

.report-title {
    margin-left: 90px;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 8px;
    color: #1971c2;
    letter-spacing: 1px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}
.report-subtitle {
    margin-left: 90px;
    font-size: 16px;
    margin-bottom: 15px;
    color: #1c7ed6;
}

.report-number {
    font-weight: bold;
    color: #1971c2;
}

.medical-symbol {
    position: absolute;
    top: 20px;
    right: 25px;
    width: 40px;
    height: 40px;
    background-color: rgba(250, 82, 82, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fa5252;
    font-size: 24px;
    box-shadow: 0 2px 4px rgba(250, 82, 82, 0.1);
}
.title {
    width: 134px;
    height: 46px;
    background-color: var(--el-color-primary);
    border-radius: 12px 0px 12px 0px;
    color: white;
    text-align: center;
    line-height: 46px;
}
.answer-sheet {
    padding: 20px;
}

.question-row,
.answer-row {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 10px;
}

.question-item,
.answer-item {
    display: inline-block;
    width: 20px;
    text-align: center;
    font-weight: bold;
}

.answer-item {
    color: #409eff;
}
/* 左侧标题列样式 */
.vertical-title-cell {
    background-color: #578bf9 !important;
    font-weight: bold;
    color: white;
}

.vertical-title {
    writing-mode: vertical-lr;
    text-align: center;
    white-space: nowrap;
    height: 100px;
    line-height: 1.5;
    margin: 0 auto;
}

/* 调整表格样式 */
.el-table :deep(.el-table__cell) {
    padding: 8px 0;
    text-align: center;
}
.content {
    text-indent: 2em;
    padding: 8px;
    border: 1px solid #578bf9;
    border-right: 5px solid #578bf9;
    border-radius: 12px;
    margin: 10px;
}
</style>
