<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.ai.mapper.AiKnowledgeDocumentMapper">

    <resultMap type="org.senyor.ai.domain.vo.AiKnowledgeDocumentVo" id="AiKnowledgeDocumentResult">
        <id     property="documentId"       column="document_id"       />
        <result property="knowledgeId"      column="knowledge_id"      />
        <result property="knowledgeName"    column="knowledge_name"    />
        <result property="documentName"     column="document_name"     />
        <result property="documentType"     column="document_type"     />
        <result property="content"          column="content"           />
        <result property="fileSize"         column="file_size"         />
        <result property="filePath"         column="file_path"         />
        <result property="status"           column="status"            />
        <result property="progress"         column="progress"          />
        <result property="vectorStatus"     column="vector_status"     />
        <result property="chunkCount"       column="chunk_count"       />
        <result property="errorMessage"     column="error_message"     />
        <result property="userId"           column="user_id"           />
        <result property="userName"         column="user_name"         />
        <result property="createBy"         column="create_by"         />
        <result property="createTime"       column="create_time"       />
        <result property="updateBy"         column="update_by"         />
        <result property="updateTime"       column="update_time"       />
        <result property="remark"           column="remark"            />
    </resultMap>

    <sql id="selectAiKnowledgeDocumentVo">
        select d.document_id, d.knowledge_id, k.knowledge_name, d.document_name, d.document_type, d.content,
               d.file_size, d.file_path, d.status, d.progress, d.vector_status, d.chunk_count, d.error_message,
               d.user_id, d.user_name, d.create_by, d.create_time, d.update_by, d.update_time, d.remark
        from ai_knowledge_document d
        left join ai_knowledge_base k on d.knowledge_id = k.knowledge_id
    </sql>

    <select id="selectAiKnowledgeDocumentById" parameterType="Long" resultMap="AiKnowledgeDocumentResult">
        <include refid="selectAiKnowledgeDocumentVo"/>
        where d.document_id = #{documentId}
    </select>

    <select id="selectAiKnowledgeDocumentsByKnowledgeId" parameterType="Long" resultMap="AiKnowledgeDocumentResult">
        <include refid="selectAiKnowledgeDocumentVo"/>
        where d.knowledge_id = #{knowledgeId}
        order by d.create_time desc
    </select>

    <select id="selectAiKnowledgeDocumentList" parameterType="org.senyor.ai.domain.AiKnowledgeDocument" resultMap="AiKnowledgeDocumentResult">
        <include refid="selectAiKnowledgeDocumentVo"/>
        <where>
            <if test="knowledgeId != null">
                AND d.knowledge_id = #{knowledgeId}
            </if>
            <if test="documentName != null and documentName != ''">
                AND d.document_name like concat('%', #{documentName}, '%')
            </if>
            <if test="documentType != null and documentType != ''">
                AND d.document_type = #{documentType}
            </if>
            <if test="status != null and status != ''">
                AND d.status = #{status}
            </if>
            <if test="vectorStatus != null and vectorStatus != ''">
                AND d.vector_status = #{vectorStatus}
            </if>
            <if test="userId != null">
                AND d.user_id = #{userId}
            </if>
        </where>
        order by d.create_time desc
    </select>

    <select id="countDocumentsByKnowledgeId" parameterType="Long" resultType="int">
        select count(1) from ai_knowledge_document where knowledge_id = #{knowledgeId}
    </select>

    <delete id="deleteDocumentsByKnowledgeId" parameterType="Long">
        delete from ai_knowledge_document where knowledge_id = #{knowledgeId}
    </delete>

</mapper>
