<template>
    <div v-show="isShow">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="userQueryParams" :inline="true">
                        <el-form-item label="用户账号" prop="userName">
                            <el-input v-model="userQueryParams.userName" placeholder="请输入用户账号" clearable @keyup.enter="handleQuery" />
                        </el-form-item>
                        <el-form-item label="性别" prop="sex">
                            <el-select v-model="userQueryParams.sex" placeholder="用户性别" clearable>
                                <el-option v-for="dict in sys_user_sex" :key="dict.value" :label="dict.label" :value="dict.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="部门" prop="deptId">
                            <el-tree-select
                                v-model="userQueryParams.deptId"
                                :data="deptOptions"
                                :props="{ value: 'id', label: 'label', children: 'children' }"
                                value-key="id"
                                placeholder="请选择部门"
                                check-strictly
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>
        <el-table v-loading="loading" :data="userList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center" />
            <template v-for="column in columns">
                <el-table-column
                    v-if="column.visible"
                    :label="column.label"
                    align="center"
                    :prop="column.prop"
                    :show-overflow-tooltip="column.overflowTooltip"
                >
                    <template #default="scope">
                        <dict-tag v-if="column.prop == 'sex'" :options="sys_user_sex" :value="scope.row.sex" />
                    </template>
                </el-table-column>
            </template>
            <el-table-column label="操作" fixed="right" class-name="small-padding fixed-width">
                <template #default="scope">
                    <el-button link type="primary" size="small" @click="handleUpdate(scope.row)">查看电子档案 </el-button>
                    <el-button link type="primary" size="small" @click="handleImprove(scope.row)">发送档案完善 </el-button>
                </template>
            </el-table-column>
        </el-table>

        <pagination
            v-if="userTotal > 0"
            v-model:page="userQueryParams.pageNum"
            v-model:limit="userQueryParams.pageSize"
            :total="userTotal"
            @pagination="getList"
        />
    </div>
    <div v-show="!isShow">
        <!-- 返回头部 -->
        <div class="header-wrap"
        >
            <div class="back-header" @click="handleBack"  >
                <el-page-header :icon="ArrowLeft"></el-page-header>
                <span class="header-title" >查看电子档案</span>
            </div>
        </div>

        <!-- 紧凑导航块 -->
        <el-tabs type="border-card" class="demo-tabs full-width-tabs" @tab-click="handleTabClick">
            <el-tab-pane class="full-width-tab">
                <template #label>
                    <span class="custom-tabs-label">
                        <span>基本信息</span>
                    </span>
                </template>
                <div class="settings-container" @click="">
                    <div>
                        <div class="setting-block">
                            <div class="setting-header">
                                <div class="vertical-line"></div>
                                <div class="title">基本信息</div>
                                <div class="action"></div>
                            </div>
                            <el-descriptions :size="size" border>
                                <!-- 原始字段 -->
                                <el-descriptions-item>
                                    <template #label>
                                        <div class="cell-item">
                                            <el-icon :style="iconStyle">
                                                <user />
                                            </el-icon>
                                            姓名：
                                        </div>
                                    </template>
                                    {{ userInfo.userName == '' ? '暂未登记' : userInfo.userName }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>
                                        <div class="cell-item">
                                            <el-icon :style="iconStyle">
                                                <iphone />
                                            </el-icon>
                                            手机号：
                                        </div>
                                    </template>
                                    {{ userInfo.phonenumber == '' ? '暂未登记' : userInfo.phonenumber }}
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>
                                        <div class="cell-item">
                                            <el-icon :style="iconStyle">
                                                <location />
                                            </el-icon>
                                            性别：
                                        </div>
                                    </template>
                                    <dict-tag v-if="userInfo.sex" :options="sys_user_sex" :value="userInfo.sex" />
                                    <span v-else>暂未登记</span>
                                </el-descriptions-item>
                                <el-descriptions-item>
                                    <template #label>
                                        <div class="cell-item">
                                            <el-icon :style="iconStyle">
                                                <tickets />
                                            </el-icon>
                                            角色：
                                        </div>
                                    </template>
                                    {{
                                        userInfo.userType == '' || userInfo.userType == null || userInfo.userType == undefined
                                            ? '暂未登记'
                                            : userInfo.userType
                                    }}
                                </el-descriptions-item>
                                <!-- <el-descriptions-item>
                                    <template #label>
                                        <div class="cell-item">
                                            <el-icon :style="iconStyle">
                                                <office-building />
                                            </el-icon>
                                            部门：
                                        </div>
                                    </template>
                                    {{ userInfo.deptNameAll == '' ? '暂未登记' : userInfo.deptNameAll }}
                                </el-descriptions-item> -->

                                <!-- 扩展字段 -->
                                <template v-if="fieldsArray.length > 0">
                                    <el-descriptions-item v-for="(item, index) in fieldsArray" :key="index">
                                        <template #label>
                                            <div class="cell-item">
                                                <el-icon :style="iconStyle">
                                                    <document />
                                                </el-icon>
                                                {{ item.title }}：
                                            </div>
                                        </template>
                                        <span v-if="item.type == 'input'">
                                            {{ item.realValue || '暂未登记' }}
                                        </span>
                                        <span v-else-if="item.type == 'select'">
                                            {{ parseMoreFieldOptions(item.options)[item.realValue] || '暂未登记' }}
                                        </span>
                                    </el-descriptions-item>
                                </template>
                            </el-descriptions>
                        </div>
                        <!-- 心理状态跟踪记录已移至单独标签页 -->
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane v-if="hasPermission('scale:scaleAnswer:list')"  label="测评记录">
                <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
                    <el-tab-pane label="自主测评" name="self"> </el-tab-pane>
                    <el-tab-pane label="测评活动" name="plan"> </el-tab-pane>
                </el-tabs>
                <el-table v-loading="tableLoading" :data="scaleAnswerList" @selection-change="handleSelectionChange">
                    <el-table-column type="selection" width="55" align="center" />
                    <el-table-column v-if="true" label="量表回答ID" align="center" prop="answerId" />
                    <el-table-column v-if="activeName == 'plan'" label="普查活动" align="center">
                        <template #default="scope">
                            {{ getPlanName(scope.row.planId) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="用户账号" align="center" prop="userName" />
                    <el-table-column label="用户姓名" align="center" prop="nickName" />
                    <el-table-column label="量表" align="center">
                        <template #default="scope">
                            {{ getScaleName(scope.row.scaleId) }}
                        </template>
                    </el-table-column>
                    <el-table-column label="数据来源" align="center" prop="source">
                        <template #default="scope">
                            <dict-tag :options="sys_device_type" :value="scope.row.source" />
                        </template>
                    </el-table-column>
                    <el-table-column label="答题时间(秒)" align="center" prop="answerTime" />
                    <el-table-column label="自评他评" align="center" prop="selfOrOther">
                        <template #default="scope">
                            <dict-tag :options="scale_type" :value="scope.row.selfOrOther" />
                        </template>
                    </el-table-column>
                    <el-table-column label="数据状态" align="center" prop="status">
                        <template #default="scope">
                            <dict-tag :options="scale_answer_status" :value="scope.row.status" />
                        </template>
                    </el-table-column>
                    <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                        <template #default="scope">
                            <el-tooltip content="查看个体报告" placement="top">
                                <el-button
                                    v-hasPermi="['scale:scaleAnswer:edit']"
                                    link
                                    type="primary"
                                    icon="Document"
                                    @click="handleReport(scope.row)"
                                ></el-button>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                </el-table>

                <pagination
                    v-if="scaleRecordTotal > 0"
                    v-model:page="scaleRecordQueryParams.pageNum"
                    v-model:limit="scaleRecordQueryParams.pageSize"
                    :total="scaleRecordTotal"
                    @pagination="getScaleRecordList"
                />
                
                <!-- 测评活动图表 -->
                <div class="setting-block" style="margin-top: 20px;">
                    <div class="setting-header">
                        <div class="vertical-line"></div>
                        <div class="title">测评活动跟踪记录</div>
                        <div class="action"></div>
                    </div>
                    <div class="description">折线图表默认展示近一个月内的测评活动记录数据，您可以根据时间进行筛选展示。</div>
                    <!-- 折线图容器 -->
                    <div class="charts-container">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'assessment')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'assessment')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'assessment')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="assessmentDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'assessment')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>测评活动</span>
                                            <div class="chart-download" @click="downloadChart('assessment')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="assessmentChart" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'assessment')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'assessment')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: assessmentTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'assessment')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="assessmentDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'assessment')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>测评活动</span>
                                            <div class="chart-download" @click="downloadChart('assessment')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="assessmentChart2" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane v-if="hasPermission('app:consultationRecords:list')" label="咨询记录">
                <el-card class="box-card" shadow="hover">
                    <el-table v-loading="consultationLoading" :data="consultationRecordsList" @selection-change="handleConsultationSelectionChange">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column label="咨询时间" align="center" prop="consultationTime">
                            <template #default="scope">
                                <span>{{ parseTime(scope.row.consultationTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="来访者" align="center" prop="conUserName" />
                        <el-table-column label="记录人" align="center" prop="consultantName" />
                        <el-table-column label="联系方式" align="center" prop="conInformation" />
                        <el-table-column label="结案状态" align="center" prop="closingStatus">
                            <template #default="scope">
                                <dict-tag :options="ap_closing_status" :value="scope.row.closingStatus" />
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                            <template #default="scope">
                                <el-tooltip content="查看记录" placement="top">
                                    <el-button link type="primary" icon="View" @click="handleViewConsultation(scope.row)"></el-button>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                    </el-table>
                    <pagination
                        v-show="consultationTotal > 0"
                        v-model:page="consultationQueryParams.pageNum"
                        v-model:limit="consultationQueryParams.pageSize"
                        :total="consultationTotal"
                        @pagination="getConsultationPagination"
                    />
                </el-card>

                <!-- 咨询记录组件 -->
                <ConsultantManagement
                    ref="consultantManagementRef"
                    :con-cialog-data="conCialogData"
                    @confirm-call-back-con="handleConsultationCallback"
                ></ConsultantManagement>
                
                <!-- 心理咨询图表 -->
                <div class="setting-block" style="margin-top: 20px;">
                    <div class="setting-header">
                        <div class="vertical-line"></div>
                        <div class="title">心理咨询跟踪记录</div>
                        <div class="action"></div>
                    </div>
                    <div class="description">折线图表默认展示近一个月内的心理咨询记录数据，您可以根据时间进行筛选展示。</div>
                    <!-- 折线图容器 -->
                    <div class="charts-container">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'consulting')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'consulting')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'consulting')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="consultingDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'consulting')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理咨询</span>
                                            <div class="chart-download" @click="downloadChart('consulting')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="consultingChart" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'consulting')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'consulting')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: consultingTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'consulting')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="consultingDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'consulting')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理咨询</span>
                                            <div class="chart-download" @click="downloadChart('consulting')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="consultingChart2" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane v-if="hasPermission('intervention:record121:view')" label="谈心谈话">
                <el-card class="box-card" shadow="hover">
                    <el-table v-loading="conversationLoading" :data="conversationList" @selection-change="handleConversationSelectionChange">
                        <el-table-column type="selection" width="55" align="center" />
                        <el-table-column prop="talkType" label="类型" align="center" />
                        <el-table-column prop="userName" label="谈话对象" align="center" />
                        <el-table-column prop="userSex" label="性别" align="center">
                            <template #default="{ row }">
                                <dict-tag :options="sys_user_sex" :value="row.userSex" />
                            </template>
                        </el-table-column>
                        <el-table-column prop="createTime" label="上报时间" align="center" />
                        <el-table-column prop="reportpersonName" label="上报人" align="center" />
                        <el-table-column prop="talkTime" label="谈话时间" align="center" />
                        <el-table-column prop="psychManagementStr" label="心理状况" align="center" />
                        <el-table-column prop="talkContent" label="谈话内容" align="center" :show-overflow-tooltip="true" />
                    </el-table>

                    <pagination
                        v-show="conversationList.length > 0"
                        v-model:page="conversationQueryParams.pageNum"
                        v-model:limit="conversationQueryParams.pageSize"
                        :total="conversationList.length"
                        @pagination="getConversationList"
                    />
                </el-card>
                
                <!-- 心理记录图表 -->
                <div class="setting-block" style="margin-top: 20px;">
                    <div class="setting-header">
                        <div class="vertical-line"></div>
                        <div class="title">心理记录跟踪</div>
                        <div class="action"></div>
                    </div>
                    <div class="description">折线图表默认展示近一个月内的心理记录数据，您可以根据时间进行筛选展示。</div>
                    <!-- 折线图容器 -->
                    <div class="charts-container">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'psychology')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'psychology')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'psychology')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="psychologyDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'psychology')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理记录</span>
                                            <div class="chart-download" @click="downloadChart('psychology')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="psychologyChart" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'psychology')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'psychology')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: psychologyTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'psychology')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="psychologyDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'psychology')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理记录</span>
                                            <div class="chart-download" @click="downloadChart('psychology')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="psychologyChart2" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-tab-pane>
            <el-tab-pane v-if="hasPermission('app:psychologicalInterview:list')" label="危机事件处理记录">
                危机事件处理记录
                
                <!-- 心理工作图表 -->
                <div class="setting-block" style="margin-top: 20px;">
                    <div class="setting-header">
                        <div class="vertical-line"></div>
                        <div class="title">心理工作跟踪记录</div>
                        <div class="action"></div>
                    </div>
                    <div class="description">折线图表默认展示近一个月内的心理工作记录数据，您可以根据时间进行筛选展示。</div>
                    <!-- 折线图容器 -->
                    <div class="charts-container">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'work')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'work')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'work')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="workDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'work')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理工作</span>
                                            <div class="chart-download" @click="downloadChart('work')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="workChart" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                            <el-col :span="12">
                                <div class="chart-card">
                                    <!-- 时间选择器 -->
                                    <div class="time-filter-bar">
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'year' }"
                                            @click="handleTimeOptionClick('year', 'work')"
                                            >近一年记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'halfYear' }"
                                            @click="handleTimeOptionClick('halfYear', 'work')"
                                            >近6个月记录</a
                                        >
                                        <a
                                            href="javascript:;"
                                            class="time-link"
                                            :class="{ active: workTimeOption === 'month' }"
                                            @click="handleTimeOptionClick('month', 'work')"
                                            >近1个月记录</a
                                        >
                                        <span class="time-divider">自定义时间：</span>
                                        <el-date-picker
                                            v-model="workDateRange"
                                            type="daterange"
                                            range-separator="~"
                                            start-placeholder="开始时间"
                                            end-placeholder="结束时间"
                                            format="YYYY-MM-DD"
                                            value-format="YYYY-MM-DD"
                                            class="custom-date-picker"
                                            @change="(val) => handleDateRangeChange(val, 'work')"
                                        />
                                    </div>
                                    <div class="chart-header">
                                        <div class="chart-title">
                                            <span>心理工作</span>
                                            <div class="chart-download" @click="downloadChart('work')">
                                                <el-icon>
                                                    <Download />
                                                </el-icon>
                                            </div>
                                        </div>
                                        <div class="warning-status">
                                            <span class="status-label">预警状态</span>
                                            <div class="status-scale">
                                                <div class="scale-line"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="workChart2" class="chart"></div>
                                    <div class="chart-footer"></div>
                                </div>
                            </el-col>
                        </el-row>
                    </div>
                </div>
            </el-tab-pane>
        </el-tabs>

        <!-- 报告弹窗 -->
        <el-dialog v-model="showReportVisible">
            <person-report :answer-id="reportId" :user-id="currentUserId" :scale-id="scaleId" :show-warning="shouldShowWarning"></person-report>
        </el-dialog>
    </div>
</template>
<script setup name="User" lang="ts">
// 导入接口
import api, { getUserProfiles, getVisitorList } from '@/api/system/user';
import { UserForm, UserQuery, UserVO, VisitorQuery } from '@/api/system/user/types';
import { DeptVO } from '@/api/system/dept/types';
import { listScaleRecord, getScaleRecord, delScaleRecord, addScaleRecord, updateScaleRecord } from '@/api/scale/scaleRecord';
import { ArrowLeft, Download } from '@element-plus/icons-vue';
import { computed, nextTick, ref, watch, onMounted, onUnmounted } from 'vue';
import { Iphone, Location, OfficeBuilding, Tickets, User } from '@element-plus/icons-vue';
import { ComponentSize } from 'element-plus';
import { ScaleRecordForm, ScaleRecordQuery, ScaleRecordVO } from '@/api/scale/scaleRecord/types';
import { ScaleAnswerForm, ScaleAnswerQuery, ScaleAnswerVO } from '@/api/scale/scaleAnswer/types';
import { listScaleAnswer } from '@/api/scale/scaleAnswer';

import { listAssessPlan } from '@/api/scale/assessPlan';
import { listScaleInfo } from '@/api/scale/scaleInfo';
import useUserStore from '@/store/modules/user';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { listPsychologicalInterview, listConPsychologicalInterview } from '@/api/app/psychologicalInterview';
import { PsychologicalInterviewVO } from '@/api/app/psychologicalInterview/types';
import { GridComponent, TooltipComponent, TitleComponent, LegendComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { addPsychologicalInterview } from '@/api/app/psychologicalInterview';
import UserSelect from '@/components/UserSelect/index.vue';
import { globalHeaders } from '@/utils/request';
import { listConsultationRecords, delConsultationRecords } from '@/api/app/consultationRecords';
import { ConsultationRecordsVO, ConsultationRecordsQuery } from '@/api/app/consultationRecords/types';
import ConsultantManagement from '@/components/ConsultantManagement/index.vue';
import { getTemplateFieldDataByUserId, improveByUserId } from '@/api/app/template';
// 注册必要的组件
echarts.use([LineChart, GridComponent, TooltipComponent, TitleComponent, LegendComponent, CanvasRenderer]);
import { checkPermi } from '@/utils/permission';
const router = useRouter();
const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { scale_type, ap_closing_status, sys_user_sex, sys_device_type, scale_answer_status } = toRefs<any>(
    proxy?.useDict('scale_type', 'ap_closing_status', 'sys_user_sex', 'sys_device_type', 'scale_answer_status')
); // 自评他评字典
const userList = ref<UserVO[]>();
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<number | string>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const dateRange = ref<[DateModelType, DateModelType]>(['', '']);
const scaleAnswerList = ref<ScaleAnswerVO[]>([]);
const deptOptions = ref<DeptVO[]>([]);
const initPassword = ref<string>('');
const isShow = ref(true);

const userInfo = ref({
    userName: '',
    phonenumber: '',
    sex: '',
    userType: '',
    deptNameAll: '',
    userId: ''
});
const conversationList = ref<any[]>([]);
const conversationLoading = ref(false);
// 列显隐信息
const columns = ref<FieldOption[]>([
    { key: 0, visible: true, prop: `userId`, label: `用户编号`, overflowTooltip: false, children: [] },
    { key: 1, visible: true, prop: `userName`, label: `用户账号`, overflowTooltip: true, children: [] },
    { key: 2, visible: true, prop: `nickName`, label: `用户昵称`, overflowTooltip: true, children: [] },
    { key: 3, visible: false, prop: `deptNameAll`, label: `部门`, overflowTooltip: true, children: [] },
    { key: 4, visible: false, prop: `userType`, label: `人员类型`, overflowTooltip: false, children: [] },
    { key: 5, visible: true, prop: `sex`, label: `性别`, overflowTooltip: false, children: [] },
    { key: 6, visible: true, prop: `phonenumber`, label: `手机号`, overflowTooltip: false, children: [] },
    { key: 7, visible: true, prop: `createTime`, label: `创建时间`, overflowTooltip: false, children: [] }
]);

const deptTreeRef = ref<ElTreeInstance>();
const queryFormRef = ref<ElFormInstance>();

// 在现有ref定义区域添加
const tableLoading = ref(false);
const reportLoading = ref(false); // 报告加载状态
const activeName = ref('plan'); // 默认为'plan'，控制普查活动列显示
const activeTabName = ref(''); // 用于跟踪当前激活的标签页
const showReportVisible = ref(false);
const reportId = ref<string | number>(0);
const currentUserId = ref<string | number>(0);
const scaleId = ref<string | number>(0);
const shouldShowWarning = ref(true);

const consultationRecordsList = ref<ConsultationRecordsVO[]>([]);
const consultationLoading = ref(false);
const consultationShowSearch = ref(true);
const consultationIds = ref<Array<string | number>>([]);
const consultationSingle = ref(true);
const consultationMultiple = ref(true);
const consultationTotal = ref(0);
const consultationQueryFormRef = ref<ElFormInstance>();
const consultantManagementRef = ref<InstanceType<typeof ConsultantManagement>>();
const conversationIds = ref<Array<string | number>>([]);
const conversationSingle = ref(true);
const conversationMultiple = ref(true);
const conversationQueryParams = ref({
    pageNum: 1,
    pageSize: 10
});
const allConsultationRecords = ref<ConsultationRecordsVO[]>([]);

// 咨询记录查询参数
const consultationQueryParams = ref<ConsultationRecordsQuery>({
    pageNum: 1,
    pageSize: 10,
    conUserName: '', // 将根据当前用户名自动填充
    params: {}
});

// 咨询记录弹窗数据
const conCialogData = reactive({
    ifEdit: undefined,
    diaLogTitle: undefined,
    conId: undefined,
    resId: undefined,
    apUserName: '', // 当前用户名
    apUserId: '' // 当前用户ID
});

const initFormData: UserForm = {
    userId: undefined,
    parentUserId: undefined,
    deptId: undefined,
    userName: '',
    nickName: undefined,
    password: '',
    userType: undefined,
    email: undefined,
    sex: '',
    status: '0',
    remark: '',
    postIds: [],
    roleIds: []
};

// 用户相关的状态和查询参数
const userState = reactive<PageData<UserForm, UserQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        userName: '',
        userType: '',
        sex: '',
        deptId: '',
        roleId: '',
        userId: '',
        orderByColumn: '',
        isAsc: 'asc'
    },
    rules: {
        userName: [
            { required: true, message: '用户账号不能为空', trigger: 'blur' },
            {
                min: 2,
                max: 20,
                message: '用户账号长度必须介于 2 和 20 之间',
                trigger: 'blur'
            }
        ],
        nickName: [{ required: true, message: '用户昵称不能为空', trigger: 'blur' }],
        password: [
            { required: true, message: '用户密码不能为空', trigger: 'blur' },
            {
                min: 5,
                max: 20,
                message: '用户密码长度必须介于 5 和 20 之间',
                trigger: 'blur'
            }
        ],
        email: [
            {
                type: 'email',
                message: '请输入正确的邮箱地址',
                trigger: ['blur', 'change']
            }
        ],
        phonenumber: [
            {
                pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
                message: '请输入正确的手机号码',
                trigger: 'blur'
            }
        ],
        roleIds: [{ required: true, message: '用户角色不能为空', trigger: 'blur' }]
    }
});

// 量表记录相关的状态和查询参数
const scaleRecordInitFormData: ScaleAnswerForm = {
    answerId: undefined,
    userId: undefined,
    nickName: undefined,
    scaleId: undefined,
    source: undefined,
    answerTime: undefined,
    answer: undefined,
    status: undefined,
    planId: undefined
};

const scaleRecordState = reactive<PageData<ScaleAnswerForm, ScaleAnswerQuery>>({
    form: { ...scaleRecordInitFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: undefined,
        nickName: undefined,
        scaleId: undefined,
        source: undefined,
        status: undefined,
        planId: undefined,
        hasPlan: undefined,
        params: {},
        orderByColumn: '',
        isAsc: 'asc'
    },
    rules: {
        answerId: [{ required: true, message: '量表回答ID不能为空', trigger: 'blur' }]
    }
});

const { queryParams: userQueryParams, form: userForm, rules: userRules } = toRefs(userState);
const { queryParams: scaleRecordQueryParams, form: scaleRecordForm, rules: scaleRecordRules } = toRefs(scaleRecordState);
const activeIndex = ref(-1);

const handleBlockClick = (index) => {
    activeIndex.value = index;
};
// 权限检查方法
const permissions = useUserStore().permissions;
const hasPermission = (permission: string) => {
    // 如果有所有权限的标记，直接返回true
    if (permissions.includes('*:*:*')) {
        return true;
    }
    // 否则检查是否包含特定权限
    return permissions.includes(permission);
};
const handleBack = () => {
    // 返回逻辑
    isShow.value = true;
};
/** 查询部门下拉树结构 */
const getTreeSelect = async () => {
    const res = await api.deptTreeSelect();
    deptOptions.value = res.data;
};

/** 查询谈心谈话列表 */
const getConversationList = async () => {
    if(checkPermi('app:psychologicalInterview:list')){
    conversationLoading.value = true;
    const userId = userInfo.value.userId;

    try {
        const [res1, res2] = await Promise.all([listPsychologicalInterview({ userId }), listConPsychologicalInterview({ userId })]);

        const currentUserId = String(userId);

        const list1 = (res1.rows || [])
            .filter((item) => String(item.userId) === currentUserId)
            .map((item) => ({
                ...item,
                talkType: '负责人谈心谈话',
                sortTime: new Date(item.createTime || item.talkTime).getTime()
            }));

        const list2 = (res2.rows || [])
            .filter((item) => String(item.userId) === currentUserId)
            .map((item) => ({
                ...item,
                talkType: '心理委员谈心谈话',
                sortTime: new Date(item.createTime || item.talkTime).getTime()
            }));

        conversationList.value = [...list1, ...list2].sort((a, b) => b.sortTime - a.sortTime);
    } catch (error) {
        console.error('获取谈心谈话列表失败:', error);
        proxy?.$modal.msgError('获取谈心谈话列表失败');
    } finally {
        conversationLoading.value = false;
    }
    }

};

// 分页相关
const userTotal = ref(0);
const scaleRecordTotal = ref(0);

/** 查询用户列表 */
const getList = async () => {
    loading.value = true;
    const res = await getUserProfiles(proxy?.addDateRange(userQueryParams.value, dateRange.value));
    loading.value = false;
    userList.value = res.rows;
    userTotal.value = res.total;
    userList.value = res.rows;
};

/** 查询量表测评结果列表 */
const getScaleRecordList = async () => {
    tableLoading.value = true; // 开始加载：显示加载动画
    try {
        scaleRecordQueryParams.value.userId = userInfo.value.userId;
        const res = await listScaleAnswer(scaleRecordQueryParams.value);
        scaleAnswerList.value = res.rows;
        scaleRecordTotal.value = res.total;

        // 缓存当前数据
        const currentTab = activeName.value as 'self' | 'plan';
        tabDataCache[currentTab] = {
            data: [...res.rows],
            total: res.total,
            queryParams: { ...scaleRecordQueryParams.value }
        };
    } catch (error) {
        console.error('查询量表记录失败:', error);
        proxy?.$modal.msgError('加载数据失败');
    } finally {
        tableLoading.value = false; // 无论成功/失败，都关闭加载动画
    }
};

// 使用字典转换性别值
const formatSex = (sex) => {
    if (!sex) return '';
    // 查找对应的字典标签
    const sexDict = sys_user_sex.value.find(item => item.value === sex);
    return sexDict ? sexDict.label : sex;
};

/** 搜索按钮操作 */
const handleQuery = () => {
    userQueryParams.value.pageNum = 1;
    getList();
};
/** 重置按钮操作 */
const resetQuery = () => {
    dateRange.value = ['', ''];
    queryFormRef.value?.resetFields();
    userQueryParams.value.pageNum = 1;
    userQueryParams.value.deptId = undefined;
    deptTreeRef.value?.setCurrentKey(undefined);
    handleQuery();
};

/** 选择条数  */
const handleSelectionChange = (selection: UserVO[]) => {
    ids.value = selection.map((item) => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};
const fieldsArray = ref([]);
const loadingFields = ref(false);

// 获取用户扩展字段
const getUserMoreFields = async (userId) => {
    try {
        loadingFields.value = true;
        const res = await getTemplateFieldDataByUserId(userId);
        fieldsArray.value = res.data.fieldList.filter((item) => item.status == '1');
    } catch (error) {
        console.error('获取扩展字段失败:', error);
        fieldsArray.value = [];
    } finally {
        loadingFields.value = false;
    }
};
const scaleAnswerInfo = (row) => {
    console.log(row);
};
const handleDelete = (row) => {
    console.log(row);
};
const size = ref<ComponentSize>('default');

const iconStyle = computed(() => {
    const marginMap = {
        large: '8px',
        default: '6px',
        small: '4px'
    };
    return {
        marginRight: marginMap[size.value] || marginMap.default
    };
});

// 折线图日期范围和时间选项
const assessmentTimeOption = ref('');
const psychologyTimeOption = ref('');
const workTimeOption = ref('');
const consultingTimeOption = ref('');

// 初始化日期范围为空
const assessmentDateRange = ref<[string, string]>(['', '']);
const psychologyDateRange = ref<[string, string]>(['', '']);
const workDateRange = ref<[string, string]>(['', '']);
const consultingDateRange = ref<[string, string]>(['', '']);

// 折线图实例
let assessmentChart: echarts.ECharts | null = null;
let psychologyChart: echarts.ECharts | null = null;
let workChart: echarts.ECharts | null = null;
let consultingChart: echarts.ECharts | null = null;

// 初始化所有图表
const initCharts = (chartTypes?: string[]) => {
    setTimeout(() => {
        // 如果没有指定图表类型，初始化所有图表
        const typesToInit = chartTypes || ['assessment', 'psychology', 'work', 'consulting'];
        
        typesToInit.forEach((chartType) => {
            // 初始化第一个图表
            const chartDom = document.getElementById(`${chartType}Chart`);
            if (chartDom) {
                // 销毁已存在的图表实例
                const existingChart = echarts.getInstanceByDom(chartDom);
                if (existingChart) {
                    existingChart.dispose();
                }

                const chartInstance = echarts.init(chartDom);
                switch (chartType) {
                    case 'assessment':
                        assessmentChart = chartInstance;
                        break;
                    case 'psychology':
                        psychologyChart = chartInstance;
                        break;
                    case 'work':
                        workChart = chartInstance;
                        break;
                    case 'consulting':
                        consultingChart = chartInstance;
                        break;
                }

                // 更新图表
                const dateRangeMap = {
                    'assessment': assessmentDateRange.value,
                    'psychology': psychologyDateRange.value,
                    'work': workDateRange.value,
                    'consulting': consultingDateRange.value
                };
                updateChart(chartType, dateRangeMap[chartType]);
            }
            
            // 初始化第二个图表
            const chartDom2 = document.getElementById(`${chartType}Chart2`);
            if (chartDom2) {
                // 销毁已存在的图表实例
                const existingChart = echarts.getInstanceByDom(chartDom2);
                if (existingChart) {
                    existingChart.dispose();
                }

                const chartInstance = echarts.init(chartDom2);
                // 更新图表
                const dateRangeMap = {
                    'assessment': assessmentDateRange.value,
                    'psychology': psychologyDateRange.value,
                    'work': workDateRange.value,
                    'consulting': consultingDateRange.value
                };
                updateChart(chartType, dateRangeMap[chartType], chartInstance);
            }
        });
    }, 300); // 添加300ms延迟
};

// 监听窗口大小变化
const handleResize = () => {
    ['assessment', 'psychology', 'work', 'consulting'].forEach((chartType) => {
        const chart = {
            assessment: assessmentChart,
            psychology: psychologyChart,
            work: workChart,
            consulting: consultingChart
        }[chartType];

        if (chart) {
            chart.resize();
        }
    });
};

// 为不同标签页创建数据缓存
const tabDataCache = reactive({
    self: {
        // 自主测评缓存
        data: [] as ScaleAnswerVO[],
        total: 0,
        queryParams: {} as ScaleAnswerQuery
    },
    plan: {
        // 测评活动缓存
        data: [] as ScaleAnswerVO[],
        total: 0,
        queryParams: {} as ScaleAnswerQuery
    }
});

// 修改标签页切换事件
const handleClick = (tab: any, event: MouseEvent) => {
    const tabName = tab.paneName;
    activeName.value = tabName;

    // 检查缓存
    const cachedData = tabDataCache[tabName as 'self' | 'plan'];

    if (cachedData && cachedData.data.length > 0) {
        // 使用缓存数据快速显示
        scaleAnswerList.value = cachedData.data;
        scaleRecordTotal.value = cachedData.total;
        scaleRecordQueryParams.value = { ...cachedData.queryParams };

        return;
    }
    // 没有缓存时正常加载
    tableLoading.value = true;
    scaleAnswerList.value = [];

    // 设置查询条件
    if (tabName === 'self') {
        scaleRecordQueryParams.value.planId = null;
        scaleRecordQueryParams.value.hasPlan = false;
    } else if (tabName === 'plan') {
        scaleRecordQueryParams.value.planId = undefined;
        scaleRecordQueryParams.value.hasPlan = true;
    }

    scaleRecordQueryParams.value.pageNum = 1;
    getScaleRecordList();
};

// 添加标签页切换处理函数
const handleTabClick = (tab) => {
    activeTabName.value = tab.props.label;
    
    // 根据不同标签页初始化对应的图表
    nextTick(() => {
        if (activeTabName.value === '测评记录') {
            initCharts(['assessment']);
        } else if (activeTabName.value === '咨询记录') {
            initCharts(['consulting']);
        } else if (activeTabName.value === '谈心谈话') {
            initCharts(['psychology']);
        } else if (activeTabName.value === '危机事件处理记录') {
            initCharts(['work']);
        }
    });
};

// 从完整页面中复用数据
const optionsAssessPlanList = ref<AssessPlanVO[]>([]); // 普查活动列表
const optionsScaleInfoList = ref<ScaleInfoVO[]>([]); // 量表列表

// 获取普查活动名称
const getPlanName = (planId: string | number): string => {
    if (!planId) return '自主测评';
    const planIdStr = String(planId);
    const plan = optionsAssessPlanList.value.find((item) => String(item.planId) === planIdStr);
    return plan ? plan.planName : `未知活动(${planIdStr})`;
};

// 获取量表名称
const getScaleName = (scaleId: string | number): string => {
    if (!scaleId) return '未知量表';
    const scaleIdStr = String(scaleId);
    const scale = optionsScaleInfoList.value.find((item) => String(item.scaleId) === scaleIdStr);
    return scale ? scale.scaleName : `未知量表(${scaleIdStr})`;
};

// 初始化数据的方法
const initPlanAndScaleData = async () => {
    try {
        // 获取普查活动列表
        const planRes = await listAssessPlan({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        });
        optionsAssessPlanList.value = planRes.rows;

        // 获取量表列表
        const scaleRes = await listScaleInfo({
            pageNum: 1,
            pageSize: 1000,
            params: {}
        });
        optionsScaleInfoList.value = scaleRes.rows;
    } catch (error) {
        console.error('加载普查活动和量表数据失败:', error);
        proxy?.$modal.msgError('加载数据失败');
    }
};

// 在适当的生命周期钩子中调用初始化方法
onMounted(() => {
    initPlanAndScaleData();
});

/** 查看个体报告 */
const handleReport = async (row?: ScaleAnswerVO) => {
    reportLoading.value = true; // 显示加载动画
    reportId.value = row.answerId;
    currentUserId.value = row.userId;
    scaleId.value = row.scaleId;

    // 显示对话框
    showReportVisible.value = true;

    // 模拟加载延迟
    await nextTick(); // 等待DOM更新
    // 这里可以添加实际的数据加载逻辑，完成后关闭加载状态
    setTimeout(() => {
        reportLoading.value = false; // 数据加载完成后隐藏动画
    }, 500); // 示例：500ms后关闭，实际应根据接口响应调整
};

onMounted(() => {
    console.log('sys_device_type:', sys_device_type.value); // 检查数据来源字典
    console.log('scale_type:', scale_type.value); // 检查自评他评字典
});

onMounted(async () => {
    try {
        // 1. 先加载基础依赖数据（部门树、计划和量表列表）
        const [deptRes, planScaleRes] = await Promise.all([
            getTreeSelect(), // 部门树数据
            initPlanAndScaleData() // 普查活动和量表列表数据
        ]);

        // 2. 设置初始标签页为"测评活动"，并加载表格数据
        activeName.value = 'plan';
        scaleRecordQueryParams.value.hasPlan = true;
        scaleRecordQueryParams.value.planId = undefined;
        scaleRecordQueryParams.value.pageNum = 1;
        await getScaleRecordList(); // 等待表格数据加载完成

        // 3. 加载其他非关键数据（不阻塞表格显示）
        await Promise.all([
            getList(), // 用户列表数据
            new Promise<void>((resolve) => {
                proxy?.getConfigKey('sys.user.initPassword').then((response) => {
                    initPassword.value = response.data;
                    resolve();
                });
            })
        ]);
    } catch (error) {
        console.error('初始化失败:', error);
        proxy?.$modal.msgError('加载数据失败');
    } finally {
        // 所有关键数据加载完成后，关闭表格加载状态（显示表格）
        tableLoading.value = false;
    }

    // 初始化图表相关监听
    window.addEventListener('resize', handleResize);
});

// 组件卸载时清理图表实例
onUnmounted(() => {
    [assessmentChart, psychologyChart, workChart, consultingChart].forEach((chart) => {
        if (chart) {
            chart.dispose();
        }
    });

    window.removeEventListener('resize', handleResize);
});

watch(scaleAnswerList, (val) => {
    console.log('表格数据:', val); // 查看每个对象是否有source和selfOrOther
});

// 监听 isShow 变化，当切换到图表视图时重新初始化图表
watch(isShow, (newVal) => {
    if (!newVal) {
        // 延迟初始化，确保DOM已经渲染
        nextTick(() => {
            // 默认初始化第一个标签页的图表
            setTimeout(() => {
                // 触发第一个标签页的点击事件
                const tabs = document.querySelector('.el-tabs__nav');
                if (tabs && tabs.children.length > 0) {
                    (tabs.children[0] as HTMLElement).click();
                }
            }, 100);
        });
    }
});

// 处理时间选择图标点击
const handleTimeOptionClick = (option: string, chartType: string) => {
    const timeOptionMap = {
        assessment: assessmentTimeOption,
        psychology: psychologyTimeOption,
        work: workTimeOption,
        consulting: consultingTimeOption
    };

    const dateRangeMap = {
        assessment: assessmentDateRange,
        psychology: psychologyDateRange,
        work: workDateRange,
        consulting: consultingDateRange
    };

    timeOptionMap[chartType].value = option;

    const endDate = new Date();
    const startDate = new Date();

    if (option === 'year') {
        startDate.setFullYear(startDate.getFullYear() - 1);
    } else if (option === 'halfYear') {
        startDate.setMonth(startDate.getMonth() - 6);
    } else if (option === 'month') {
        startDate.setMonth(startDate.getMonth() - 1);
    }

    const formatDate = (date: Date) => {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    };

    dateRangeMap[chartType].value = [formatDate(startDate), formatDate(endDate)];
    handleDateRangeChange(dateRangeMap[chartType].value, chartType);
};

// 处理日期范围变化
const handleDateRangeChange = (val: [string, string], chartType: string) => {
    if (val && val[0] && val[1]) {
        const timeOptionMap = {
            assessment: assessmentTimeOption,
            psychology: psychologyTimeOption,
            work: workTimeOption,
            consulting: consultingTimeOption
        };
        timeOptionMap[chartType].value = 'custom';
        updateChart(chartType, val);
    }
};

// 更新指定图表数据
const updateChart = (chartType: string, dateRange: [string, string], chartInstance?: echarts.ECharts) => {
    const chartMap = {
        assessment: assessmentChart,
        psychology: psychologyChart,
        work: workChart,
        consulting: consultingChart
    };

    const chart = chartInstance || chartMap[chartType];
    if (!chart) return;

    chart.resize();

    const mockData = {
        dates: [],
        values: []
    };

    let dateLabel = '';
    if (!dateRange || !dateRange[0] || !dateRange[1]) {
        dateLabel = `开始时间 ~ 结束时间`;
    } else {
        dateLabel = `${dateRange[0]} ~ ${dateRange[1]}`;
    }

    const option = {
        grid: {
            top: '5%',
            left: '0%',
            right: '20%',
            bottom: '5%',
            containLabel: true,
            show: false,
            height: '85%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'line',
                lineStyle: {
                    color: '#dcdfe6',
                    width: 1
                }
            },
            formatter: '{b}<br/>{a}: {c}'
        },
        xAxis: {
            type: 'category',
            boundaryGap: true,
            data: [],
            axisLine: {
                show: true,
                lineStyle: {
                    color: '#909399',
                    width: 2
                }
            },
            axisTick: {
                show: false,
                alignWithLabel: true
            },
            axisLabel: {
                color: '#606266',
                fontSize: 12,
                interval: 'auto',
                rotate: 0,
                margin: 15,
                formatter: (value: string) => {
                    return value;
                }
            },
            name: '日期（年月日）',
            nameLocation: 'end',
            nameTextStyle: {
                color: '#606266',
                padding: [0, 0, 0, 20]
            }
        },
        yAxis: {
            type: 'value',
            min: 0,
            max: 3,
            interval: 0.5,
            show: true,
            inverse: false,
            axisLine: {
                show: false
            },

            axisLabel: {
                color: '#606266',
                fontSize: 12,
                align: 'right',
                margin: 20,
                verticalAlign: 'middle',
                padding: [10, 0, 10, 0],
                formatter: function (value) {
                    const labels = ['正常', '轻度', '中度', '重度'];
                    if (Number.isInteger(value) && value >= 0 && value < labels.length) {
                        return labels[value];
                    }
                    return '';
                }
            },
            position: 'left',
            offset: 0,
            splitLine: {
                show: true,
                lineStyle: {
                    color: '#EBEEF5',
                    width: 1.5,
                    type: 'solid'
                },
                interval: 0
            }
        },
        series: [
            {
                name: getChartName(chartType),
                type: 'line',
                smooth: true,
                symbol: 'circle',
                symbolSize: 6,
                lineStyle: {
                    width: 2,
                    color: getChartColor(chartType)
                },
                itemStyle: {
                    color: getChartColor(chartType)
                },
                areaStyle: {
                    color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                            {
                                offset: 0,
                                color: getChartColor(chartType, 0.2)
                            },
                            {
                                offset: 1,
                                color: getChartColor(chartType, 0.02)
                            }
                        ]
                    }
                },
                data: []
            }
        ]
    };

    chart.setOption(option);
};

// 获取图表名称
const getChartName = (chartType: string) => {
    const nameMap = {
        assessment: '测评活动',
        psychology: '心理记录',
        work: '心理工作',
        consulting: '心理咨询'
    };
    return nameMap[chartType] || '';
};

// 获取图表颜色
const getChartColor = (chartType: string, alpha = 1) => {
    const colorMap = {
        assessment: `rgba(84, 112, 198, ${alpha})`,
        psychology: `rgba(145, 204, 117, ${alpha})`,
        work: `rgba(250, 200, 88, ${alpha})`,
        consulting: `rgba(238, 102, 102, ${alpha})`
    };
    return colorMap[chartType] || `rgba(84, 112, 198, ${alpha})`;
};

// 生成日期范围
const generateDateRange = (start: string, end: string) => {
    const dates = [];
    const current = new Date(start);
    const endDate = new Date(end);

    while (current <= endDate) {
        dates.push(formatDate(current));
        current.setDate(current.getDate() + 1);
    }

    return dates;
};

const formatDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
};

// 下载图表为PNG
const downloadChart = (chartType: string) => {
    const chartMap = {
        assessment: assessmentChart,
        psychology: psychologyChart,
        work: workChart,
        consulting: consultingChart
    };

    const chart = chartMap[chartType];
    if (!chart) return;

    // 获取图表的base64数据URL
    const url = chart.getDataURL({
        type: 'png',
        backgroundColor: '#ffffff'
    });

    // 创建下载链接
    const link = document.createElement('a');
    link.download = `${getChartName(chartType)}_${formatDate(new Date())}.png`;
    link.href = url;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};

/** 多选框选中数据 */
const handleConversationSelectionChange = (selection: any[]) => {
    conversationIds.value = selection.map((item) => item.intId);
};

/** 查询咨询记录列表 */
const getConsultationList = async () => {
    consultationLoading.value = true;

    try {
        // 获取所有咨询记录，不带用户过滤条件
        const res = await listConsultationRecords({
            pageNum: 1,
            pageSize: 10000, // 获取足够多的记录
            params: {}
        });

        // 保存所有记录
        allConsultationRecords.value = res.rows || [];

        // 根据当前用户过滤记录
        filterAndPaginateConsultationRecords();
    } catch (error) {
        console.error('获取咨询记录失败:', error);
        proxy?.$modal.msgError('获取咨询记录列表失败');
    } finally {
        consultationLoading.value = false;
    }
};

// 过滤和分页
const filterAndPaginateConsultationRecords = () => {
    // 根据当前用户ID过滤
    const filteredRecords = allConsultationRecords.value.filter((record) => !userInfo.value.userId || record.conUserId === userInfo.value.userId);

    // 计算总数
    consultationTotal.value = filteredRecords.length;

    // 本地分页
    const startIndex = (consultationQueryParams.value.pageNum - 1) * consultationQueryParams.value.pageSize;
    const endIndex = startIndex + consultationQueryParams.value.pageSize;

    // 分页后的数据
    consultationRecordsList.value = filteredRecords.slice(startIndex, endIndex);
};

/** 多选框选中数据 */
const handleConsultationSelectionChange = (selection: ConsultationRecordsVO[]) => {
    consultationIds.value = selection.map((item) => item.conId);
    consultationSingle.value = selection.length != 1;
    consultationMultiple.value = !selection.length;
};

// 修改分页事件处理
const getConsultationPagination = () => {
    filterAndPaginateConsultationRecords();
};

/** 查看记录按钮操作 */
const handleViewConsultation = (row: ConsultationRecordsVO) => {
    conCialogData.diaLogTitle = '咨询师访谈评估项';
    conCialogData.ifEdit = 1;
    conCialogData.conId = row.conId;
    consultantManagementRef.value.open();
};

// 监听用户信息变化，重新加载咨询记录
watch(
    () => userInfo.value.userId,
    (newVal) => {
        if (newVal) {
            getConsultationList();
        }
    }
);
// 监听分页参数变化，实时更新分页显示
watch(
    () => [consultationQueryParams.value.pageNum, consultationQueryParams.value.pageSize],
    () => {
        filterAndPaginateConsultationRecords();
    }
);

// 监听用户信息变化，重新过滤数据
watch(
    () => userInfo.value.userId,
    (newVal) => {
        if (newVal) {
            filterAndPaginateConsultationRecords();
        }
    }
);

/** 处理咨询记录回调 */
const handleConsultationCallback = () => {
    // 重新获取所有记录
    getConsultationList();
};

// 修改原有的handleUpdate函数，添加加载咨询记录的逻辑
const handleUpdate = async (row: ScaleAnswerVO | undefined) => {
    userInfo.value = row;
    // 并行处理所有数据请求
    await Promise.all([getScaleRecordList(), getConversationList(), getConsultationList(), getUserMoreFields(row.userId)]);
    isShow.value = false;
};

const handleImprove = (row) => {
    improveByUserIdFunc(row.userId);
};

const improveByUserIdFunc = async (userId: string | number) => {
    const res = await improveByUserId(userId);
    if (res.code == 200) {
        proxy?.$modal.msgSuccess('已发送完善通知');
    }
};

const parseMoreFieldOptions = (options: string) => {
    return JSON.parse(options);
};
</script>
<style>
.container {
    padding: 20px;
}

/* 头部样式 */
.header-wrap {
    margin-bottom: 20px;
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
}

.back-header {
    display: inline-flex;
    align-items: center;
    cursor: pointer;
    transition: all 0.2s;
}

.turn-icon {
    font-size: 20px;
    margin-right: 8px;
    transform: rotate(90deg) scaleY(-1);
}

.back-header:hover .turn-icon {
    color: #409eff;
}

.header-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
}

/* 紧凑导航块 */
.nav-blocks {
    margin-left: 50px;
    margin-right: 50px;
    display: flex;
    width: 95%;
    border: 1px solid #dcdfe6;
}

.nav-block {
    padding: 10px;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 300;
    font-size: 15px;
    cursor: pointer;
    border-right: 1px solid #dcdfe6;
    transition: all 0.2s;
}

.nav-block:last-child {
    border-right: none;
}

.nav-block:hover {
    color: #409eff;
    background: #f5faff;
}

.nav-block.active {
    color: #409eff;
    background: #fff;
    border-bottom: 2px solid #409eff;
    margin-bottom: -1px;
    /* 覆盖底部边框 */
}

.settings-container {
    padding: 20px;
    min-height: 100vh;
}

.setting-block {
    margin-bottom: 20px;
}

.setting-header {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

/* 蓝色竖线 */
.vertical-line {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 10px;
}

/* 标题 */
.title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-right: 10px;
}

/* 描述文字 */
.description {
    font-size: 14px;
    color: #909399;
    padding-left: 14px;
    /* 与蓝色竖线对齐 */
}

.el-descriptions {
    margin-top: 20px;
}

.cell-item {
    display: flex;
    align-items: center;
}

.margin-top {
    margin-top: 20px;
}

/* 使标签栏占满一行 */
.full-width-tabs .el-tabs__nav {
    display: flex;
    width: 100%;
}

/* 每个标签项平均分配宽度 */
.full-width-tabs .el-tabs__item {
    flex: 1;
    text-align: center;
    border-right: 1px solid #dcdfe6;
}

/* 移除最后一个标签项的右边框 */
.full-width-tabs .el-tabs__item:last-child {
    border-right: none;
}

/* 调整激活标签的样式 */
.full-width-tabs .el-tabs__item.is-active {
    background-color: #f5f7fa;
    border-bottom: 2px solid #409eff;
}

/* 调整标签容器的边框 */
.full-width-tabs.el-tabs--border-card {
    border: 1px solid #dcdfe6;
}

/* 调整标签栏的边框 */
.full-width-tabs.el-tabs--border-card > .el-tabs__header {
    border-bottom: 1px solid #dcdfe6;
}

/* 调整内容区域的边框 */
.full-width-tabs.el-tabs--border-card > .el-tabs__content {
    padding: 15px;
    border-left: 1px solid #dcdfe6;
    border-right: 1px solid #dcdfe6;
    border-bottom: 1px solid #dcdfe6;
}

/* 添加图表相关样式 */
.chart-filters {
    margin: 15px 0;
    display: flex;
    justify-content: flex-end;
}

.date-filter {
    margin-left: 10px;
}

.charts-container {
    margin-top: 20px;
}

.chart-card {
    background: #fff;
    border-radius: 0;
    padding: 15px;
    box-shadow: none;
    height: 100%;
}

.chart-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    font-size: 14px;
    font-weight: 600;
}

.chart-legend {
    display: flex;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-left: 10px;
    font-size: 12px;
    font-weight: normal;
}

.legend-color {
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 5px;
}

.chart {
    height: 400px;
    margin: 20px 0;
    width: 100%;
}

.chart-download {
    margin-left: 10px;
}

.chart-status-label {
    margin-bottom: 10px;
}

.status-scale {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.status-item {
    margin-right: 10px;
    font-size: 12px;
    color: #606266;
}

.status-line {
    flex: 1;
    height: 2px;
    background-color: #dcdfe6;
}

.chart-date {
    margin-top: 10px;
    text-align: right;
    font-size: 12px;
    color: #909399;
}

/* u65f6u95f4u9009u62e9u94feu63a5u6837u5f0f */
.time-options {
    margin: 15px 0;
}

.time-selector {
    display: flex;
    align-items: center;
}

.time-link {
    color: #606266;
    text-decoration: none;
    margin-right: 15px;
    font-size: 14px;
}

.time-link.active {
    color: #409eff;
    font-weight: bold;
}

/* 统一的时间选择器 */
.time-filter-bar {
    display: flex;
    align-items: center;
    padding: 10px 0;
    margin-bottom: 15px;
    border-bottom: 1px solid #ebeef5;
    flex-wrap: wrap;
}

.time-link {
    padding: 4px 8px;
    color: #606266;
    text-decoration: none;
    font-size: 13px;
    border-radius: 4px;
    transition: all 0.3s;
    margin-right: 8px;
    white-space: nowrap;
}

.time-link:hover {
    color: #409eff;
    background-color: #ecf5ff;
}

.time-link.active {
    color: #409eff;
    background-color: #ecf5ff;
}

.time-divider {
    margin: 0 8px;
    color: #606266;
    font-size: 13px;
    white-space: nowrap;
}

.custom-date-picker {
    width: 240px;
}

/* 图表卡片样式 */
.chart-card {
    background: #fff;
    border-radius: 0;
    padding: 15px;
    box-shadow: none;
    height: 100%;
}

.chart-header {
    margin-bottom: 15px;
}

.chart-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.chart-title span {
    font-size: 15px;
    font-weight: 600;
    color: #303133;
}

.chart-download {
    cursor: pointer;
    color: #909399;
}

.chart-download:hover {
    color: #409eff;
}

/* 预警状态样式 */
.warning-status {
    margin-top: 10px;
    display: flex;
    align-items: flex-start;
}

.status-label {
    font-size: 13px;
    color: #606266;
    margin-right: 15px;
    white-space: nowrap;
}

.status-scale {
    position: relative;
    flex: 1;
}

.scale-marks {
    position: relative;
    display: flex;
    flex-direction: column;
    z-index: 2;
}

.mark-item {
    position: relative;
    padding: 8px 0;
    font-size: 12px;
    color: #606266;
    display: flex;
    align-items: center;
}

.mark-item::after {
    content: '';
    position: absolute;
    left: 60px;
    right: 0;
    top: 50%;
    height: 1px;
    background: #ebeef5;
    z-index: 1;
}

/* 移除原有的水平线 */
.scale-line {
    display: none;
}

/* 图表容器样式 */
.chart {
    height: 240px;
    margin: 15px 0;
}

.chart-footer {
    text-align: right;
    padding-top: 8px;
    border-top: 1px solid #ebeef5;
}

.date-label {
    font-size: 12px;
    color: #909399;
}

/* 图表网格样式 */
.charts-container {
    margin-top: 20px;
}

.el-row {
    margin-bottom: 15px;
}

.el-col {
    margin-bottom: 15px;
}

@media screen and (max-width: 1400px) {
    .time-filter-bar {
        flex-direction: column;
        align-items: flex-start;
    }

    .time-link-group {
        display: flex;
        margin-bottom: 8px;
    }

    .date-picker-group {
        display: flex;
        align-items: center;
        width: 100%;
    }

    .custom-date-picker {
        width: 100%;
    }
}
</style>
