<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.statistics.mapper.HomepageDataStatisticsMapper">

    <!-- 获取用户总数及上个月数据 -->
    <select id="getWarningUserCount" resultType="java.util.Map">
        SELECT
        -- 所有预警的去重用户数
        COUNT(DISTINCT user_id) AS currentTotal,
        -- 上个月预警的去重用户数
        COUNT(DISTINCT CASE
        WHEN DATE_FORMAT(create_time, '%Y%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y%m')
            THEN user_id
        ELSE NULL
        END) AS lastMonthEndTotal
        FROM risk_warning_record;
    </select>

    <select id="getNewUserCount" resultType="java.util.Map">
        SELECT
            -- 当月创建人数（去重）
            COUNT(DISTINCT CASE
                WHEN DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(CURDATE(), '%Y-%m')
                    THEN user_id
                END) AS currentTotal,
            -- 上月创建人数（去重）
            COUNT(DISTINCT CASE
                WHEN DATE_FORMAT(create_time, '%Y-%m') = DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 1 MONTH), '%Y-%m')
                    THEN user_id
                END) AS lastMonthEndTotal
        FROM sys_user
        WHERE create_time IS NOT NULL;
    </select>

    <!-- 获取部门基本信息 - 返回所有部门 -->
    <select id="getDeptBaseInfo" resultType="org.senyor.statistics.domain.vo.GradeInfoCarouselVo">
        SELECT
            dept_id AS deptId,
            dept_name AS name,
            500 AS capacity
        FROM
            sys_dept
        WHERE
             status = '0'
            AND del_flag = '0'
            AND parent_id != 0
        ORDER BY
            order_num ASC
    </select>

    <!-- 获取部门学生数量 -->
    <select id="getDeptStudentCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_user
        WHERE
             dept_id = #{deptId}
            AND status = '0'
            AND del_flag = '0'
    </select>

    <!-- 获取部门班级数量 -->
    <select id="getDeptClassCount" resultType="java.lang.Integer">
        SELECT
            COUNT(1)
        FROM
            sys_dept
        WHERE
             parent_id = #{deptId}
            AND status = '0'
            AND del_flag = '0'
    </select>

    <!-- 获取部门预警人数 -->
    <select id="getDeptWarningCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT user_id)
        FROM
            scale_early_warning_record
        WHERE
             dept_id = #{deptId}
            AND status = '0'
    </select>

    <!-- 获取部门心理老师数量 -->
    <select id="getDeptTeacherCount" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT u.user_id)
        FROM
            sys_user u
            -- 通过sys_user_role关联用户和角色（已正确关联）
            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
            INNER JOIN sys_role r ON ur.role_id = r.role_id
        WHERE
            u.tenant_id = #{tenantId}
            AND u.dept_id = #{deptId}
            -- 用户状态正常（未禁用、未删除）
            AND u.status = '0'
            AND u.del_flag = '0'
            -- 精准匹配角色关键字（避免模糊匹配导致的错误）
            AND r.role_key = 'consultant'  -- 若角色关键字是精确的"consultant"
    </select>

    <!-- 获取部门预警级别分布 - 极简版 -->
    <select id="getDeptWarningLevelDistribution" resultType="java.util.Map">
        SELECT
            warning_level_name AS name,
            warning_color AS color,
            COUNT(1) AS count
        FROM
            scale_early_warning_record
        WHERE
             dept_id = #{deptId}
            AND status = '0'
        GROUP BY
            warning_level_name, warning_color
        ORDER BY
            warning_level_id ASC
    </select>

    <!-- 获取部门所有预警记录 -->
    <select id="getDeptWarningRecords" resultType="java.util.Map">
        SELECT
            user_id,
            warning_level_name,
            warning_color,
            warning_level_id
        FROM
            scale_early_warning_record
        WHERE
             dept_id = #{deptId}
            AND status = '0'
    </select>

    <!-- 批量获取多个部门的学生数量 -->
    <select id="batchGetDeptStudentCount" resultType="java.util.Map">
        SELECT
            dept_id AS deptId,
            COUNT(1) AS studentCount
        FROM
            sys_user
        WHERE
             dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND status = '0'
            AND del_flag = '0'
        GROUP BY
            dept_id
    </select>

    <!-- 批量获取多个部门的班级数量 -->
    <select id="batchGetDeptClassCount" resultType="java.util.Map">
        SELECT
            parent_id AS deptId,
            COUNT(1) AS classCount
        FROM
            sys_dept
        WHERE
             parent_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND status = '0'
            AND del_flag = '0'
        GROUP BY
            parent_id
    </select>

    <!-- 批量获取多个部门的预警人数 -->
    <select id="batchGetDeptWarningCount" resultType="java.util.Map">
        SELECT
            dept_id AS deptId,
            COUNT(DISTINCT user_id) AS warningCount
        FROM
            scale_early_warning_record
        WHERE
             dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND status = '0'
        GROUP BY
            dept_id
    </select>

    <!-- 批量获取多个部门的心理老师数量 -->
    <select id="batchGetDeptTeacherCount" resultType="java.util.Map">
        SELECT
            u.dept_id AS deptId,
            COUNT(DISTINCT u.user_id) AS teacherCount
        FROM
            sys_user u
            -- 通过sys_user_role关联用户和角色（已正确关联）
            INNER JOIN sys_user_role ur ON u.user_id = ur.user_id
            INNER JOIN sys_role r ON ur.role_id = r.role_id
        WHERE
            -- 批量查询指定部门
             u.dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            -- 用户状态正常（未禁用、未删除）
            AND u.status = '0'
            AND u.del_flag = '0'
            -- 精准匹配角色关键字（避免模糊匹配导致的错误）
            AND r.role_key = 'consultant'  -- 若角色关键字是精确的"consultant"
        GROUP BY
            u.dept_id
    </select>

    <!-- 批量获取多个部门的所有预警记录 -->
    <select id="batchGetDeptWarningRecords" resultType="java.util.Map">
        SELECT
            dept_id AS deptId,
            user_id,
            warning_level_name,
            warning_color,
            warning_level_id
        FROM
            scale_early_warning_record
        WHERE
             dept_id IN
            <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND status = '0'
    </select>

    <select id="getMaxStudentCount" resultType="java.lang.String">
        SELECT
            CASE
                WHEN account_count &lt; 0 THEN '无上限'
                ELSE CAST(account_count AS CHAR)
                END AS maxCount
        FROM
            sys_tenant
        WHERE
            status = '0'
            AND tenant_id = #{tenantId}
    </select>

    <!-- 获取预警状态统计数据 -->
    <select id="getWarningStatusStatistics" resultType="java.util.Map">
        SELECT
            t.warning_level_id AS warningLevelId,
            wl.warning_name AS level,
            wl.warning_color AS color,
            COUNT( CASE WHEN t.status = '6000' THEN t.user_id ELSE NULL END) AS notStarted,
            COUNT( CASE WHEN t.status = '6001' THEN t.user_id ELSE NULL END) AS tracking,
            COUNT( CASE WHEN t.status = '6002' THEN t.user_id ELSE NULL END) AS referral,
            COUNT( CASE WHEN t.status = '6003' THEN t.user_id ELSE NULL END) AS closed,
            COUNT( t.user_id) AS total
        FROM (
            SELECT
                rwr.user_id,
                rwr.warning_level_id,
                rwr.status
            FROM
                risk_warning_record rwr
            INNER JOIN (
                SELECT
                    user_id,
                    MAX(warning_level_id) AS max_warning_level_id
                FROM
                    risk_warning_record
                WHERE
                    tenant_id = #{tenantId}
                GROUP BY
                    user_id
            ) max_levels ON rwr.user_id = max_levels.user_id AND rwr.warning_level_id = max_levels.max_warning_level_id
            WHERE
                rwr.tenant_id = #{tenantId}
        ) t
        JOIN
            scale_early_warning_level_config wl ON t.warning_level_id = wl.warning_level_id
            AND wl.type = 'scale'
            AND wl.tenant_id = #{tenantId}
        GROUP BY
            t.warning_level_id, wl.warning_name, wl.warning_color
        ORDER BY
            t.warning_level_id ASC
    </select>

</mapper>
