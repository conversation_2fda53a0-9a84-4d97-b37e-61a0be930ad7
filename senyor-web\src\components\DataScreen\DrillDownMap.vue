<template>
    <div class="map-container">
        <div v-if="loading" class="loading-overlay">Loading...</div>
        <div v-if="error" class="error-message">{{ error }}</div>
        <div class="map-controls">
            <button :disabled="!canDrillBack" @click="drillBack">← Back</button>
            <div class="level-indicator">当前层级: {{ levelNames[currentLevel] }}</div>
        </div>
        <div ref="chartEl" class="chart"></div>

        <!-- 数据点详情弹窗 -->
        <div v-if="selectedPoint" class="data-point-modal">
            <h3>{{ selectedPoint.name }}</h3>
            <p>类型：{{ selectedPoint.type }}</p>
            <p v-if="selectedPoint.address">地址：{{ selectedPoint.address }}</p>
            <p>坐标：{{ selectedPoint.coordinates }}</p>
            <button @click="selectedPoint = null">关闭</button>
        </div>

        <!-- 性能监控面板 -->
        <!-- <div v-if="showPerf" class="perf-panel">
            <h4>性能指标</h4>
            <ul>
                <li>加载耗时: {{ metrics.loadTime.toFixed(1) }}ms</li>
                <li>渲染耗时: {{ metrics.renderTime.toFixed(1) }}ms</li>
                <li>内存占用: {{ metrics.memoryUsage.toFixed(1) }}MB</li>
                <li>FPS: {{ metrics.fps }}</li>
            </ul>
        </div> -->
    </div>
</template>

<script setup lang="ts">
// import { computed, onMounted, onUnmounted, ref, shallowRef } from 'vue';
import { computed, onMounted, onUnmounted, ref, shallowRef } from 'vue';
import * as echarts from 'echarts';
import { debounce } from 'lodash-es';
import { useMapStore } from '@/store/modules/mapStore';
import type { DataPointProperties, GeoJSONSource, MapFeatureProperties, MapLevel, WorkerMessage } from './dataWorker/dataScreenTypes';
import { PerfMonitor } from '@/utils/perfMonitor';
import useUserStore from "@/store/modules/user";
import {getTenantBaseCode, getTenantsInArea, getUserNumOnLevelCode} from "@/api/statistics/userStatistics";
import {dynamicTenant} from "@/api/system/tenant";

const perfMonitor = new PerfMonitor();

const showPerf = ref(true);

const metrics = ref({
    loadTime: 0,
    renderTime: 0,
    memoryUsage: 0,
    fps: 0
});

const levelNames: Record<MapLevel, string> = {
    nation: '全国',
    province: '省级',
    city: '市级',
    district: '区县级'
};

const emit = defineEmits<{
    (e: 'level-change', level: MapLevel, feature: MapFeatureProperties): void;
    (e: 'data-point-click', point: DataPointProperties): void;
}>();

const chartEl = ref<HTMLElement>();
const chartInstance = shallowRef<echarts.ECharts>();
const currentLevel = ref<MapLevel>('nation');
const loading = ref(false);
const error = ref<string>();
const historyStack = ref<Array<{
    featureProps: MapFeatureProperties;
    clickedArea: string;
    levelCode: string;
    adcode: string;
}>>([]);
const selectedPoint = ref<DataPointProperties | null>(null);
const mapStore = useMapStore();

const worker = new Worker(new URL('./dataWorker/dataWorker.ts', import.meta.url), {
    type: 'module'
});

const canDrillBack = computed(() => historyStack.value.length > 0);
const tenantId = useUserStore().tenantId
const fetchUserData = async (level: string, levelId: string) => {
    try {
        const response = await getUserNumOnLevelCode(level, levelId);
        if (response.code === 200) {
            return response.data;
        }
        return {};
    } catch (error) {
        console.error('获取用户数据失败:', error);
        return {};
    }
};

// 初始化地图
const initChart = async () => {
    try {
        const res = await getTenantBaseCode(tenantId);
        const level = res.data.level;
        const adCode = res.data.adCode;
        loading.value = true;
        const levelMap = {
            'nation': 'nation',
            'province': 'province',
            'city': 'city',
            'district': 'area'
        };
        const userLevel = levelMap[level] || 'nation';
        const userData = await fetchUserData(userLevel, adCode);

        const initialFeature = await fetchGeoData(adCode);
        const options = await processWorkerMessage('init', {
            geoData: safeClone(initialFeature),  // 添加安全克隆
            featureProps: { name: adCode, level: level, adcode: adCode },
            userData
        });

        chartInstance.value = echarts.init(chartEl.value!);
        echarts.registerMap(adCode, initialFeature);
        chartInstance.value.setOption(options);
        setupChartEvents();
        updateLevel({ name: adCode, level: level, adcode: adCode } as MapFeatureProperties);
    } catch (err) {
        error.value = err instanceof Error ? err.message : '初始化失败';
    } finally {
        loading.value = false;
    }
};
// 获取地理数据（带缓存）
const fetchGeoData = async (adcode: number): Promise<GeoJSONSource> => {
    //=============
    if (!adcode.toString().endsWith('00')) {
        const parentAdcode = adcode.toString().slice(0, 4) + '00';
        const res = await fetchGeoData(parseInt(parentAdcode));
        const adcodeFeature = res.features.find((f) => f.properties.adcode == adcode);
        return {
            type: 'FeatureCollection',
            features: [adcodeFeature]
        };
    }
    //==============

    const CACHE_KEY = `geo-${adcode}`;
    const cached = localStorage.getItem(CACHE_KEY);
    if (cached) {
        return JSON.parse(cached);
    }
    try {
        const res = await fetch(`https://geo.datav.aliyun.com/areas_v3/bound/${adcode}_full.json`, {
            method: 'GET',
            referrerPolicy: 'no-referrer' // 禁用 Referer，否则会403
        });
        if (!res.ok) throw new Error(`HTTP ${res.status}`);
        const data = await res.json();
        localStorage.setItem(CACHE_KEY, JSON.stringify(data));
        return data;
    } catch (err) {
        error.value = `地图加载失败: ${adcode}`;
        throw err;
    }
};

// 核心下钻处理方法
const handleDrill = async (params: echarts.ECElementEvent) => {
    if (loading.value) return;
    // console.log(`${params.componentType} - ${params.seriesType}`);
    if (!params.data) {
        // 处理区县级逻辑
        /* const { data } = await getAreaIdByName(params.name);
        console.log(`点击：${params.name}-${JSON.stringify(data)}`);
        if (!data) {
            return;
        }
        processTownMapDrill({ adcode: data.id, name: params.name, level: 'town', parent: { adcode: data.parent } }, data.parentName);*/
        return;
    }

    const featureData = params.data;
    console.log(`featureData=== ${JSON.stringify(featureData)}`);
    if (featureData) {
        try {
            loading.value = true;

            // 处理数据点点击（优先判断）
            if (params.seriesType === 'effectScatter') {
                handleDataPointClick(featureData);
                return;
            }

            // 处理地图下钻
            await processMapDrill(featureData, params);
        } catch (err) {
            handleDrillError(err);
        } finally {
            loading.value = false;
        }
    }
};

const checkTenantNowOnTenantId = async (tenantId: string | number) => {
    await dynamicTenant(tenantId)
}

// 处理数据点点击
const handleDataPointClick = async (point: DataPointProperties) => {
    // 显示详细信息弹窗
    await checkTenantNowOnTenantId(point.tenantId)
    selectedPoint.value = point;
    emit('data-point-click', point);

    /*// 自动聚焦到数据点
    chartInstance.value?.dispatchAction({
        type: 'geoRoam',
        zoom: 1.5,
        center: point.coordinates
    });

    // 高亮相关区域
    chartInstance.value?.dispatchAction({
        type: 'highlight',
        seriesIndex: 0,
        name: getParentRegion(point.coordinates)
    });*/
};

// 更新层级状态
const updateLevel = (featureProps: MapFeatureProperties) => {
    currentLevel.value = getLevel(featureProps);
    mapStore.setCurrentFeature(featureProps);
    emit('level-change', currentLevel.value, featureProps);
};

// 安全克隆
const safeClone = (obj: any): any => {
    try {
        return JSON.parse(JSON.stringify(obj));
    } catch (e) {
        console.error('克隆失败:', e);
        return {};
    }
};

// Worker通信
const processWorkerMessage = <T extends WorkerMessage['type']>(
    type: T,
    payload: any
): Promise<echarts.EChartsOption> => {
    return new Promise((resolve, reject) => {
        worker.onmessage = (e) => {
            if (e.data.error) {
                reject(new Error(e.data.error));
            } else {
                // 添加 tooltip.formatter 函数
                const options = e.data;
                if (options.tooltip) {
                    options.tooltip.formatter = function (params: any) {
                        if (params.seriesType === 'effectScatter') {
                            return `机构名称: ${params.name}<br>机构ID: ${params.data.tenantId}`;
                        }
                        return `名称: ${params.name}<br>注册人数: ${params.value}`;
                    };
                }
                resolve(options);
            }
        };
        const safePayload = safeClone(payload);
        worker.postMessage({ type, payload: safePayload });
    });
};

// 处理地图下钻逻辑
const processMapDrill = async (featureProps: MapFeatureProperties, params: echarts.ECElementEvent) => {
    historyStack.value.push({
        featureProps: mapStore.currentFeature!,
        clickedArea: params.name,
        levelCode: featureProps.level,
        adcode: mapStore.currentFeature.adcode.toString()
    });
    const levelMap = {
        'nation': 'nation',
        'province': 'province',
        'city': 'city',
        'district': 'area'
    };
    const userLevel = levelMap[featureProps.level] || 'nation';
    const userData = await fetchUserData(userLevel, featureProps.adcode.toString());

    let tenantPoints: DataPointProperties[] = [];
    if (featureProps.level === 'district' || featureProps.level === 'town') {
        try {
            const response = await getTenantsInArea('district', featureProps.adcode.toString());
            if (response.code === 200) {
                tenantPoints = response.data.map(item => ({
                    id: `tenant-${item.tenantId}`,
                    adcode: featureProps.adcode,
                    name: item.companyName,
                    type: 'tenant',
                    coordinates: item.coordinate.split(',').map(Number) as [number, number],
                    tenantId: item.tenantId
                }));
                console.log('租户点数据:', tenantPoints);
            }
        } catch (err) {
            console.error('获取机构数据失败:', err);
        }
    }

    const nextAdcode = await processWorkerMessage('drill', {
        feature: {
            properties: featureProps,
            geometry: params.data.geometry
        }
    });

    const geoData = await fetchGeoData(nextAdcode);
    if (!echarts.getMap(featureProps.name)) {
        echarts.registerMap(featureProps.name, geoData);
    }

    const options = await processWorkerMessage('update', {
        geoData: safeClone(geoData),
        featureProps: {
            ...featureProps,
            adcode: nextAdcode,
            level: getNextLevel(featureProps.level)
        },
        userData,
        tenantPoints
    });

    // 确保配置有效
    if (!options) {
        throw new Error('配置对象为空');
    }

    chartInstance.value!.setOption(options, true);
    updateLevel(featureProps);
};

// 处理区县地图下钻逻辑
const processTownMapDrill = async (featureProps: MapFeatureProperties, parentName: string) => {
    // 保存当前状态
    historyStack.value.push({
        featureProps: mapStore.currentFeature!,
        clickedAreas: mapStore.currentFeature.name,
        chartOptions: chartInstance.value!.getOption()
    });

    // console.log(`nextAdcode:${JSON.stringify(nextAdcode)},featureProps:${JSON.stringify(featureProps)}`);
    const geoData = await fetchGeoData(featureProps.adcode);
    console.log(`processTownMapDrill == featureProps.name:${JSON.stringify(featureProps)}, geoData:${JSON.stringify(geoData)}`);
    echarts.registerMap(parentName, geoData);
    const options = await processWorkerMessage('update', {
        geoData,
        featureProps: featureProps
    });
    console.log(JSON.stringify(options));

    chartInstance.value!.setOption(options, true);
    updateLevel(featureProps);
};

const getNextLevel = (currentLevel: string): MapLevel => {
    const levelMap: Record<string, MapLevel> = {
        'nation': 'province',
        'province': 'city',
        'city': 'district',
        'district': 'town',
        'town': 'town'
    };
    return levelMap[currentLevel] || 'nation';
};

// 错误处理
const handleDrillError = (err: unknown) => {
    const message = err instanceof Error ? err.message : '操作失败';
    error.value = message;

    // 自动回滚操作
    if (historyStack.value.length > 0) {
        const lastState = historyStack.value.pop()!;
        chartInstance.value?.setOption(lastState.chartOptions, true);
        updateLevel(lastState.featureProps);
    }

    // 错误上报
    console.error('下钻操作失败:', err);
};

// 事件绑定优化
const setupChartEvents = () => {
    if (!chartInstance.value) return;
    chartInstance.value.off('click');
    chartInstance.value.off('contextmenu');
    chartInstance.value.off('mouseover');
    chartInstance.value.off('mouseout');
    chartInstance.value.on('click', debounce(handleDrill, 300));
    chartInstance.value.on('mouseover', (params: any) => {
        if (params.seriesType === 'effectScatter' && params.data?.tenantId) {
            const tooltip = document.createElement('div');
            tooltip.className = 'custom-tooltip';
            tooltip.innerHTML = `机构名称: ${params.name}<br>机构ID: ${params.data.tenantId}`;
            tooltip.style.position = 'absolute';
            tooltip.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
            tooltip.style.color = 'white';
            tooltip.style.padding = '8px';
            tooltip.style.borderRadius = '4px';
            tooltip.style.zIndex = '1000';
            tooltip.style.pointerEvents = 'none';
            chartEl.value?.appendChild(tooltip);
            const updatePosition = (event: MouseEvent) => {
                tooltip.style.left = `${event.pageX + 10}px`;
                tooltip.style.top = `${event.pageY - 10}px`;
            };
            chartEl.value?.addEventListener('mousemove', updatePosition);
            (params as any).__tooltipHandler = updatePosition;
            (params as any).__tooltipElement = tooltip;
        }
    });

    chartInstance.value.on('mouseout', (params: any) => {
        if (params.seriesType === 'effectScatter' && params.data?.tenantId) {
            if (params.__tooltipElement) {
                chartEl.value?.removeChild(params.__tooltipElement);
            }
            if (params.__tooltipHandler) {
                chartEl.value?.removeEventListener('mousemove', params.__tooltipHandler);
            }
        }
    })
    // 右键返回
    chartEl.value?.addEventListener('contextmenu', (e) => {
        e.preventDefault();
        if (selectedPoint.value) {
            selectedPoint.value = null;
        } else {
            drillBack();
        }
    });
};
// 返回上级
const drillBack = async () => {
    if (!canDrillBack.value) return;

    try {
        loading.value = true;
        const prevState = historyStack.value.pop()!;
        const { adcode } = prevState.featureProps;
        const geoData = await fetchGeoData(adcode);
        const userData = await fetchUserData(prevState.levelCode, adcode.toString());
        echarts.registerMap(adcode.toString(), geoData);
        const options = await processWorkerMessage('update', {
            geoData: safeClone(geoData),  // 关键：克隆geoData
            featureProps: prevState.featureProps,
            userData
        });
        chartInstance.value!.setOption(options, true);
        updateLevel(prevState.featureProps);

    } catch (err) {
        error.value = '返回上级失败';
        console.error('drillBack error:', err);
    } finally {
        loading.value = false;
    }
};
// 辅助方法
const getLevel = (props: MapFeatureProperties): MapLevel => {
    const adcode = props.adcode.toString();
    if (props.level == 'town') {
        return 'town'
    }
    if (adcode === '100000') return 'nation';
    if (adcode.endsWith('0000')) return 'province';
    if (adcode.endsWith('00')) return 'city';
    return 'district';
};

// 生命周期
onMounted(() => {
    initChart();
    window.addEventListener(
        'resize',
        debounce(() => chartInstance.value?.resize(), 300)
    );

    // perfMonitor.value.startReporting();
});

onUnmounted(() => {
    worker.terminate();
    chartInstance.value?.dispose();
});
</script>

<style scoped>
.map-container {
    position: relative;
    height: 100%;
    width: 100%;
    background: rgba(16, 35, 75, 0.6);
    box-sizing: border-box;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
}

.perf-panel {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    z-index: 1000;
    font-size: 12px;
}

.map-controls {
    position: absolute;
    top: 15px;
    left: 15px;
    z-index: 100;
    background: rgba(0, 0, 0, 0.4);
    padding: 6px 10px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    gap: 10px;
    align-items: center;
}

.map-controls button {
    background: #4ecdc4;
    border: none;
    color: white;
    padding: 3px 8px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
}

.map-controls button:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.level-indicator {
    font-size: 12px;
    color: #fff;
}

.chart {
    width: 90%; /* 将地图宽度缩小 */
    height: 90%; /* 将地图高度缩小 */
    margin: 0 auto; /* 水平居中 */
    border-radius: 4px;
    overflow: hidden;
}

.error-message {
    color: #ff4d4f;
    padding: 10px;
    background: rgba(255, 0, 0, 0.1);
    border: 1px solid rgba(255, 0, 0, 0.3);
    margin: 10px;
    border-radius: 4px;
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 100;
    font-size: 12px;
}

/* 保持原有样式，添加数据点弹窗样式 */
.data-point-modal {
    position: absolute;
    top: 50px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    padding: 15px;
    border-radius: 6px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    max-width: 250px;
    color: #fff;
    font-size: 12px;
}

.data-point-modal h3 {
    margin-top: 0;
    margin-bottom: 10px;
    color: #4ecdc4;
    font-size: 14px;
}

.data-point-modal p {
    margin: 5px 0;
}

.data-point-modal button {
    background: #4ecdc4;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    margin-top: 10px;
    cursor: pointer;
}
</style>
