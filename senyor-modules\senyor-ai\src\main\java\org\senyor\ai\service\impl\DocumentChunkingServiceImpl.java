package org.senyor.ai.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.domain.AiKnowledgeChunk;
import org.senyor.ai.mapper.AiKnowledgeChunkMapper;
import org.senyor.ai.service.IDocumentChunkingService;
import org.senyor.ai.service.IEmbeddingService;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文档分块服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class DocumentChunkingServiceImpl implements IDocumentChunkingService {

    private final AiKnowledgeChunkMapper aiKnowledgeChunkMapper;
    private final IEmbeddingService embeddingService;
    private final IVectorStoreService vectorStoreService;
    private final ObjectMapper objectMapper;

    // 向量索引名称前缀
    private static final String VECTOR_INDEX_PREFIX = "ai_knowledge_vectors";

    @Override
    public List<String> chunkContent(String content, int chunkSize, int overlap) {
        List<String> chunks = new ArrayList<>();

        if (content == null || content.isEmpty()) {
            return chunks;
        }

        int start = 0;
        while (start < content.length()) {
            int end = Math.min(start + chunkSize, content.length());
            String chunk = content.substring(start, end);
            chunks.add(chunk);

            // 计算下一个块的起始位置，考虑重叠
            start = end - overlap;
            if (start >= content.length()) {
                break;
            }
        }

        return chunks;
    }

    @Override
    public List<String> smartChunkContent(String content, int maxChunkSize) {
        List<String> chunks = new ArrayList<>();

        if (content == null || content.isEmpty()) {
            return chunks;
        }

        // 按段落分割
        String[] paragraphs = content.split("\\n\\s*\\n");

        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty()) {
                continue;
            }

            if (paragraph.length() <= maxChunkSize) {
                // 段落小于最大块大小，直接作为一个块
                chunks.add(paragraph);
            } else {
                // 段落大于最大块大小，按句子分割
                List<String> sentences = splitIntoSentences(paragraph);
                StringBuilder currentChunk = new StringBuilder();

                for (String sentence : sentences) {
                    if (currentChunk.length() + sentence.length() <= maxChunkSize) {
                        currentChunk.append(sentence);
                    } else {
                        if (currentChunk.length() > 0) {
                            chunks.add(currentChunk.toString().trim());
                            currentChunk = new StringBuilder();
                        }

                        // 如果单个句子就超过最大块大小，强制分割
                        if (sentence.length() > maxChunkSize) {
                            List<String> subChunks = chunkContent(sentence, maxChunkSize, 50);
                            chunks.addAll(subChunks);
                        } else {
                            currentChunk.append(sentence);
                        }
                    }
                }

                if (currentChunk.length() > 0) {
                    chunks.add(currentChunk.toString().trim());
                }
            }
        }

        return chunks;
    }

    @Override
    @Transactional
    public int processDocument(Long documentId, String content, Long knowledgeId) {
        try {
            log.info("开始处理文档，文档ID: {}, 知识库ID: {}", documentId, knowledgeId);

            // 删除现有分块
            aiKnowledgeChunkMapper.deleteChunksByDocumentId(documentId);

            // 智能分块
            List<String> chunks = smartChunkContent(content, 1000);

            // 保存分块到数据库
            Long currentUserId = LoginHelper.getUserId();
            String currentUserName = LoginHelper.getUsername();

            for (int i = 0; i < chunks.size(); i++) {
                String chunkContent = chunks.get(i);

                AiKnowledgeChunk chunk = new AiKnowledgeChunk();
                chunk.setDocumentId(documentId);
                chunk.setKnowledgeId(knowledgeId);
                chunk.setContent(chunkContent);
                chunk.setChunkIndex(i);
                chunk.setChunkSize(chunkContent.length());
                chunk.setVectorStatus("0"); // 待向量化
                chunk.setVectorDimension(embeddingService.getVectorDimension());
                chunk.setUserId(currentUserId);
                chunk.setUserName(currentUserName);

                aiKnowledgeChunkMapper.insert(chunk);
            }

            log.info("文档处理完成，创建了 {} 个分块", chunks.size());


            return chunks.size();

        } catch (Exception e) {
            log.error("文档处理失败", e);
            throw new RuntimeException("文档处理失败", e);
        }
    }

    /**
     * 将向量转换为字符串存储
     */
    private String convertEmbeddingToString(List<Float> embedding) {
        try {
            return objectMapper.writeValueAsString(embedding);
        } catch (Exception e) {
            log.error("向量转字符串失败", e);
            return "[]";
        }
    }

    /**
     * 将文本按句子分割
     */
    private List<String> splitIntoSentences(String text) {
        List<String> sentences = new ArrayList<>();

        // 使用正则表达式匹配句子结束标记
        Pattern pattern = Pattern.compile("[.!?。！？]+\\s*");
        String[] parts = pattern.split(text);

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i].trim();
            if (!part.isEmpty()) {
                // 添加句子结束标记（除了最后一个部分）
                if (i < parts.length - 1) {
                    // 找到对应的结束标记
                    int start = text.indexOf(part);
                    if (start >= 0 && start + part.length() < text.length()) {
                        char endChar = text.charAt(start + part.length());
                        if (endChar == '.' || endChar == '!' || endChar == '?' ||
                            endChar == '。' || endChar == '！' || endChar == '？') {
                            part += endChar;
                        }
                    }
                }
                sentences.add(part);
            }
        }

        return sentences;
    }
}
