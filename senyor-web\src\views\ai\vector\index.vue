<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>向量存储管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="refreshStats" :loading="loading">
              <el-icon><RefreshRight /></el-icon>
              刷新统计
            </el-button>
            <el-button type="warning" @click="cleanupVectors" :loading="cleaning">
              <el-icon><Delete /></el-icon>
              清理过期向量
            </el-button>
          </div>
        </div>
      </template>

      <!-- 统计信息 -->
      <el-row :gutter="20" class="stats-row">
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.totalVectors || 0 }}</div>
              <div class="stat-label">总向量数量</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.vectorDimension || 1536 }}</div>
              <div class="stat-label">向量维度</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.storageType || 'Redis' }}</div>
              <div class="stat-label">存储类型</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="6">
          <el-card class="stat-card">
            <div class="stat-content">
              <div class="stat-number">{{ stats.indexName || 'ai_knowledge_vectors' }}</div>
              <div class="stat-label">索引名称</div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 操作日志 -->
      <el-card class="log-card">
        <template #header>
          <span>操作日志</span>
        </template>
        <div class="log-content">
          <div v-for="(log, index) in operationLogs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="operationLogs.length === 0" class="no-logs">
            暂无操作日志
          </div>
        </div>
      </el-card>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { RefreshRight, Delete } from '@element-plus/icons-vue'
import { getVectorStats, cleanupExpiredVectors } from '@/api/ai/chat'

// 响应式数据
const loading = ref(false)
const cleaning = ref(false)
const stats = ref({})
const operationLogs = ref([])

// 获取向量统计信息
const refreshStats = async () => {
  try {
    loading.value = true
    const response = await getVectorStats()
    if (response.code === 200) {
      stats.value = response.data
      addLog('刷新统计信息成功')
    } else {
      ElMessage.error('获取统计信息失败')
    }
  } catch (error) {
    console.error('获取统计信息失败:', error)
    ElMessage.error('获取统计信息失败')
  } finally {
    loading.value = false
  }
}

// 清理过期向量
const cleanupVectors = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清理过期的向量数据吗？此操作不可逆。',
      '确认清理',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    cleaning.value = true
    const response = await cleanupExpiredVectors()
    if (response.code === 200) {
      ElMessage.success(`清理完成，共清理 ${response.data} 个过期向量`)
      addLog(`清理过期向量完成，清理数量: ${response.data}`)
      // 刷新统计信息
      await refreshStats()
    } else {
      ElMessage.error('清理失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理过期向量失败:', error)
      ElMessage.error('清理失败')
    }
  } finally {
    cleaning.value = false
  }
}

// 添加操作日志
const addLog = (message: string) => {
  const now = new Date()
  const time = now.toLocaleString()
  operationLogs.value.unshift({
    time,
    message
  })
  // 只保留最近50条日志
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(0, 50)
  }
}

// 页面加载时获取统计信息
onMounted(() => {
  refreshStats()
})
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-row {
  margin-bottom: 20px;
}

.stat-card {
  text-align: center;
}

.stat-content {
  padding: 20px;
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.log-card {
  margin-top: 20px;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #999;
  font-size: 12px;
  width: 150px;
  flex-shrink: 0;
}

.log-message {
  color: #333;
  flex: 1;
}

.no-logs {
  text-align: center;
  color: #999;
  padding: 20px;
}
</style> 