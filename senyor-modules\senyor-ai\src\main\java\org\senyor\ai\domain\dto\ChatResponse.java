package org.senyor.ai.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 聊天响应DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "聊天响应结果")
public class ChatResponse implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID
     */
    @Schema(description = "会话ID")
    private String conversationId;

    /**
     * 消息ID
     */
    @Schema(description = "消息ID")
    private Long messageId;

    /**
     * 助手回复内容
     */
    @Schema(description = "助手回复内容")
    private String content;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String model;

    /**
     * 生成令牌数
     */
    @Schema(description = "生成令牌数")
    private Integer tokens;

    /**
     * 完成原因
     */
    @Schema(description = "完成原因")
    private String finishReason;

    /**
     * 是否新会话
     */
    @Schema(description = "是否新会话")
    private Boolean isNewConversation;

    /**
     * AI思考过程内容
     */
    @Schema(description = "AI思考过程内容")
    private String reasoningContent;
}
