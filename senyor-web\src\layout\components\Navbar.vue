<template>
    <div class="navbar">
        <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggle-click="toggleSideBar" />
        <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" />
        <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />
        <div class="center-menu">
            <template v-if="appStore.device !== 'mobile'"> </template>
        </div>
        <div class="right-menu flex align-center">
            <template v-if="appStore.device !== 'mobile'">
                <!-- v-if="userStore.isSuperAdmin && tenantEnabled"-->
                <!--                <el-select
                    v-model="companyName"
                    v-hasPermi="['system:tenant:dynamic']"
                    class="min-w-244px"
                    clearable
                    filterable
                    reserve-keyword
                    :placeholder="$t('navbar.selectTenant')"
                    @change="dynamicTenantEvent"
                    @clear="dynamicClearEvent"
                >
                    <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.companyName" :value="item.tenantId"> </el-option>
                    <template #prefix><svg-icon icon-class="company" class="el-input__icon input-icon" /></template>
    </el-select>-->

                <div class="company">
                    <el-tree-select
                        v-model="companyName"
                        v-hasPermi="['system:tenant:dynamic']"
                        :data="tenantOptions"
                        :props="{ value: 'tenantId', label: 'companyName', children: 'children' }"
                        value-key="tenantId"
                        class="min-w-244px"
                        clearable
                        filterable
                        reserve-keyword
                        check-strictly
                        :placeholder="$t('navbar.selectTenant')"
                        @change="dynamicTenantEvent"
                        @clear="dynamicClearEvent"
                    />
                </div>

                <el-tooltip v-hasPermi="['system:data:dashboard']" content="数据大屏" effect="dark" placement="bottom">
                    <div v-hasPermi="['system:data:dashboard']" class="right-menu-item hover-effect" @click="dataScreen">
                        <el-button type="primary" icon="monitor" round>数据大屏</el-button>
                    </div>
                </el-tooltip>

                <!-- <header-search id="header-search" class="right-menu-item" /> -->
                <search-menu ref="searchMenuRef" />
                <el-tooltip content="搜索" effect="dark" placement="bottom">
                    <div class="right-menu-item hover-effect" @click="openSearchMenu">
                        <svg-icon class-name="search-icon" icon-class="search" />
                    </div>
                </el-tooltip>
                <!-- 消息 -->
                <el-tooltip :content="$t('navbar.message')" effect="dark" placement="bottom">
                    <div>
                        <el-popover placement="bottom" trigger="click" transition="el-zoom-in-top" :width="300" :persistent="false">
                            <template #reference>
                                <el-badge :value="newNotice > 0 ? newNotice : ''" :max="99">
                                    <svg-icon icon-class="message" />
                                </el-badge>
                            </template>
                            <template #default>
                                <notice></notice>
                            </template>
                        </el-popover>
                    </div>
                </el-tooltip>

                <!--<el-tooltip :content="$t('navbar.document')" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip>-->

                <el-tooltip :content="$t('navbar.full')" effect="dark" placement="bottom">
                    <screenfull id="screenfull" class="right-menu-item hover-effect" />
                </el-tooltip>

                <!--<el-tooltip :content="$t('navbar.language')" effect="dark" placement="bottom">
          <lang-select id="lang-select" class="right-menu-item hover-effect" />
        </el-tooltip>-->

                <!--<el-tooltip :content="$t('navbar.layoutSize')" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>-->
            </template>
            <div class="avatar-container">
                <el-dropdown class="right-menu-item hover-effect" trigger="click" @command="handleCommand">
                    <div class="avatar-wrapper">
                        <img :src="userStore.avatar" class="user-avatar" />
                        <el-icon><caret-bottom /></el-icon>
                    </div>
                    <template #dropdown>
                        <el-dropdown-menu>
                            <router-link v-if="!dynamic" to="/user/profile">
                                <el-dropdown-item>{{ $t('navbar.personalCenter') }}</el-dropdown-item>
                            </router-link>
                            <el-dropdown-item v-if="settingsStore.showSettings" command="setLayout">
                                <span>{{ $t('navbar.layoutSetting') }}</span>
                            </el-dropdown-item>
                            <el-dropdown-item divided command="logout">
                                <span>{{ $t('navbar.logout') }}</span>
                            </el-dropdown-item>
                        </el-dropdown-menu>
                    </template>
                </el-dropdown>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SearchMenu from './TopBar/search.vue';
import DashBoard from '@/components/DataScreen/dashboard.vue';
import useAppStore from '@/store/modules/app';
import useUserStore from '@/store/modules/user';
import useTenantStore from '@/store/modules/tenant';
import useSettingsStore from '@/store/modules/settings';
import useNoticeStore from '@/store/modules/notice';
import { dynamicClear, dynamicTenant, listTenant } from '@/api/system/tenant';
import { TenantVO } from '@/api/types';
import notice from './notice/index.vue';
import { checkPermi } from '@/utils/permission';

const appStore = useAppStore();
const userStore = useUserStore();
const tenantStore = useTenantStore();
const settingsStore = useSettingsStore();
const noticeStore = storeToRefs(useNoticeStore());
const newNotice = ref(<number>0);

const { proxy } = getCurrentInstance() as ComponentInternalInstance;

const companyName = ref(undefined);
const tenantList = ref<TenantVO[]>([]);
const tenantOptions = ref<TenantOption[]>([]);
// 是否切换了租户
const dynamic = ref(false);
// 租户开关
const tenantEnabled = ref(true);
// 搜索菜单
const searchMenuRef = ref<InstanceType<typeof SearchMenu>>();

type TenantOption = {
    tenantId: number;
    companyName: string;
    children?: TenantOption[];
};

const dataScreen = () => {
    // proxy?.$router.push('/bigData');
    //searchMenuRef.value?.openSearch();
    const routeUrl = proxy?.$router.resolve({ path: '/bigData' }).href; // 获取完整的 URL
    window.open(routeUrl, '_blank');
};

const openSearchMenu = () => {
    searchMenuRef.value?.openSearch();
};

// 动态切换
const dynamicTenantEvent = async (tenantId: string) => {
    if (companyName.value != null && companyName.value !== '') {
        await dynamicTenant(tenantId);
        console.log(`dynamicTenantEvent:${tenantId}`);

        await tenantStore.getTenant(tenantId);
        console.log(`userStore:${tenantStore.tenantId}`);
        dynamic.value = true;
        await proxy?.$tab.closeAllPage();
        await proxy?.$tab.refreshPage();
        await proxy?.$router.push('/');
    }
};

const dynamicClearEvent = async () => {
    await dynamicClear();
    dynamic.value = false;
    proxy?.$tab.closeAllPage();
    await proxy?.$tab.refreshPage();
    proxy?.$router.push('/');
};

/** 租户列表 */
const initTenantList = async () => {
    console.log(`checkPermi:${checkPermi('system:tenant:dynamic')}`);
    if (!checkPermi(['system:tenant:dynamic'])) {
        return;
    }
    const { data } = await listTenant({});
    // tenantList.value = data;

    tenantOptions.value = proxy?.handleTree<TenantOption>(data, 'tenantId', 'parentTenantId');
    // const data: TenantOption = { tenantId: 0, companyName: '顶级节点', children: [] };
    // data.children =
    // tenantOptions.value.push(data);
    // tenantEnabled.value = data.tenantEnabled === undefined ? true : data.tenantEnabled;
    // if (tenantEnabled.value) {
    //
    // }
};

defineExpose({
    initTenantList
});

const toggleSideBar = () => {
    appStore.toggleSideBar(false);
};

const logout = async () => {
    await ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    });
    const tenantId = userStore.tenantId;
    await userStore.logout();
    location.href = import.meta.env.VITE_APP_CONTEXT_PATH + 'index' + '&tenantId=' + tenantId;
};

const emits = defineEmits(['setLayout']);
const setLayout = () => {
    emits('setLayout');
};
// 定义Command方法对象 通过key直接调用方法
const commandMap: { [key: string]: any } = {
    setLayout,
    logout
};
const handleCommand = (command: string) => {
    // 判断是否存在该方法
    if (commandMap[command]) {
        commandMap[command]();
    }
};
//用深度监听 消息
watch(
    () => noticeStore.state.value.notices,
    (newVal) => {
        newNotice.value = newVal.filter((item: any) => !item.read).length;
    },
    { deep: true }
);
</script>

<style lang="scss" scoped>
:deep(.el-select .el-input__wrapper) {
    height: 30px;
}

:deep(.el-badge__content.is-fixed) {
    top: 12px;
}

.flex {
    display: flex;
}

.align-center {
    align-items: center;
}

.navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

    .hamburger-container {
        line-height: 46px;
        height: 100%;
        float: left;
        cursor: pointer;
        transition: background 0.3s;
        -webkit-tap-highlight-color: transparent;

        &:hover {
            background: rgba(0, 0, 0, 0.025);
        }
    }

    .breadcrumb-container {
        float: left;
    }

    .topmenu-container {
        position: absolute;
        left: 50px;
    }

    .errLog-container {
        display: inline-block;
        vertical-align: top;
    }

    .center-menu {
        left: 50px;
    }

    .right-menu {
        float: right;
        height: 100%;
        line-height: 50px;
        display: flex;

        &:focus {
            outline: none;
        }

        .company {
            margin-right: 50px;
        }

        .right-menu-item {
            display: inline-block;
            padding: 0 8px;
            height: 100%;
            font-size: 18px;
            color: #5a5e66;
            vertical-align: text-bottom;

            &.hover-effect {
                cursor: pointer;
                transition: background 0.3s;

                &:hover {
                    background: rgba(0, 0, 0, 0.025);
                }
            }
        }

        .avatar-container {
            margin-right: 40px;

            .avatar-wrapper {
                margin-top: 5px;
                position: relative;

                .user-avatar {
                    cursor: pointer;
                    width: 40px;
                    height: 40px;
                    border-radius: 10px;
                    margin-top: 10px;
                }

                i {
                    cursor: pointer;
                    position: absolute;
                    right: -20px;
                    top: 25px;
                    font-size: 12px;
                }
            }
        }
    }
}
</style>
