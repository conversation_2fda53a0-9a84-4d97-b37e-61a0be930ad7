package org.senyor.web.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.senyor.common.core.constant.UserConstants;
import org.senyor.common.core.domain.R;
import org.senyor.common.core.domain.model.LoginBody;
import org.senyor.common.core.domain.model.RegisterBody;
import org.senyor.common.core.domain.model.SocialLoginBody;
import org.senyor.common.core.utils.*;
import org.senyor.common.encrypt.annotation.ApiEncrypt;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.oss.core.OssClient;
import org.senyor.common.oss.factory.OssFactory;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.social.config.properties.SocialLoginConfigProperties;
import org.senyor.common.social.config.properties.SocialProperties;
import org.senyor.common.social.utils.SocialUtils;
import org.senyor.common.tenant.helper.TenantHelper;
import org.senyor.common.websocket.dto.WebSocketMessageDto;
import org.senyor.common.websocket.utils.WebSocketUtils;
import org.senyor.system.domain.bo.SysTenantBo;
import org.senyor.system.domain.vo.SysClientVo;
import org.senyor.system.domain.vo.SysOssVo;
import org.senyor.system.domain.vo.SysTenantVo;
import org.senyor.system.service.*;
import org.senyor.web.domain.vo.LoginTenantVo;
import org.senyor.web.domain.vo.LoginVo;
import org.senyor.web.domain.vo.TenantListVo;
import org.senyor.web.service.IAuthStrategy;
import org.senyor.web.service.SysLoginService;
import org.senyor.web.service.SysRegisterService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 认证
 *
 * <AUTHOR> Li
 */
@Slf4j
@SaIgnore
@RequiredArgsConstructor
@RestController
@RequestMapping("/auth")
public class AuthController {

    private final SocialProperties socialProperties;
    private final SysLoginService loginService;
    private final SysRegisterService registerService;
    private final ISysConfigService configService;
    private final ISysTenantService tenantService;
    private final ISysSocialService socialUserService;
    private final ISysClientService clientService;
    private final ScheduledExecutorService scheduledExecutorService;
    private final ISysOssService ossService;


    /**
     * 登录方法
     *
     * @param body 登录信息
     * @return 结果
     */
    @ApiEncrypt
    @PostMapping("/login")
    public R<LoginVo> login(@RequestBody String body) {
        LoginBody loginBody = JsonUtils.parseObject(body, LoginBody.class);
        ValidatorUtils.validate(loginBody);
        // 授权类型和客户端id
        String clientId = loginBody.getClientId();
        String grantType = loginBody.getGrantType();
        SysClientVo client = clientService.queryByClientId(clientId);
        // 查询不到 client 或 client 内不包含 grantType
        if (ObjectUtil.isNull(client) || !StringUtils.contains(client.getGrantType(), grantType)) {
            log.info("客户端id: {} 认证类型：{} 异常!.", clientId, grantType);
            return R.fail(MessageUtils.message("auth.grant.type.error"));
        } else if (!UserConstants.NORMAL.equals(client.getStatus())) {
            return R.fail(MessageUtils.message("auth.grant.type.blocked"));
        }

        // 校验机构
        loginService.checkAndGetTenant(loginBody.getTenantId());

        // 登录
        LoginVo loginVo = IAuthStrategy.login(body, client, grantType);


        Long userId = LoginHelper.getUserId();
        String nickname = LoginHelper.getNickname();
        scheduledExecutorService.schedule(() -> {
            WebSocketMessageDto dto = new WebSocketMessageDto();
            dto.setMessage(nickname + " 欢迎登录！");
            dto.setSessionKeys(List.of(userId));
            WebSocketUtils.publishMessage(dto);
        }, 3, TimeUnit.SECONDS);
        return R.ok(loginVo);
    }

    /**
     * 登录方法
     *
     * @param body 登录信息
     * @return 结果
     */
    @PostMapping("/vrLogin")
    public R<LoginVo> vrLogin(@RequestBody String body) {
        log.info("body:{}", body);
        LoginBody loginBody = JsonUtils.parseObject(body, LoginBody.class);
        ValidatorUtils.validate(loginBody);
        // 授权类型和客户端id
        String clientId = loginBody.getClientId();
        String grantType = loginBody.getGrantType();
        SysClientVo client = clientService.queryByClientId(clientId);
        // 查询不到 client 或 client 内不包含 grantType
        if (ObjectUtil.isNull(client) || !StringUtils.contains(client.getGrantType(), grantType)) {
            log.info("客户端id: {} 认证类型：{} 异常!.", clientId, grantType);
            return R.fail(MessageUtils.message("auth.grant.type.error"));
        } else if (!UserConstants.NORMAL.equals(client.getStatus())) {
            return R.fail(MessageUtils.message("auth.grant.type.blocked"));
        }

        // 校验机构
        loginService.checkAndGetTenant(loginBody.getTenantId());

        // 登录
        LoginVo loginVo = IAuthStrategy.login(body, client, grantType);
        return R.ok(loginVo);
    }

    /**
     * 获取跳转URL
     *
     * @param source 登录来源
     * @return 结果
     */
    @GetMapping("/binding/{source}")
    public R<String> authBinding(@PathVariable("source") String source,
                                 @RequestParam String tenantId, @RequestParam String domain) {
        SocialLoginConfigProperties obj = socialProperties.getType().get(source);
        if (ObjectUtil.isNull(obj)) {
            return R.fail(source + "平台账号暂不支持");
        }
        AuthRequest authRequest = SocialUtils.getAuthRequest(source, socialProperties,tenantId);
        Map<String, String> map = new HashMap<>();
        map.put("tenantId", tenantId);
        map.put("domain", domain);
        map.put("state", AuthStateUtils.createState());
        String authorizeUrl = authRequest.authorize(Base64.encode(JsonUtils.toJsonString(map), StandardCharsets.UTF_8));
        log.info("authorizeUrl:{}", authorizeUrl);
        return R.ok("操作成功", authorizeUrl);
    }

    /**
     * 第三方登录请求
     *
     * @return 结果
     */
    @GetMapping("/wx")
    public String wx(@RequestParam("echostr") String echostr,
                        @RequestParam("nonce") String nonce,
                        @RequestParam("signature") String signature,
                        @RequestParam("timestamp") String timestamp) {
        log.info("echostr:{},nonce:{},signature:{},timestamp:{}", echostr, nonce, signature, timestamp);

        return echostr;
    }

    /**
     * 前端回调绑定授权(需要token)
     *
     * @param loginBody 请求体
     * @return 结果
     */
    @PostMapping("/social/callback")
    public R<Void> socialCallback(@RequestBody SocialLoginBody loginBody) {
        // 校验token
//        StpUtil.checkLogin();
        // 获取第三方登录信息
        AuthResponse<AuthUser> response = SocialUtils.loginAuth(
                loginBody.getSource(), loginBody.getSocialCode(),
                loginBody.getSocialState(), socialProperties,loginBody.getTenantId());
        AuthUser authUserData = response.getData();
        log.info("authUserData: ${}", JsonUtils.toJsonString(authUserData));
        // 判断授权响应是否成功
        if (!response.ok()) {
            return R.fail(response.getMsg());
        }
        loginService.socialRegister(authUserData);
        return R.ok();
    }

    /**
     * 取消授权
     *
     * @param socialId socialId
     */
    @DeleteMapping(value = "/unlock/{socialId}")
    public R<Void> unlockSocial(@PathVariable Long socialId) {
        // 校验token
        StpUtil.checkLogin();
        Boolean rows = socialUserService.deleteWithValidById(socialId);
        return rows ? R.ok() : R.fail("取消授权失败");
    }


    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public R<Void> logout() {
        loginService.logout();
        return R.ok("退出成功");
    }

    /**
     * 用户注册
     */
    @ApiEncrypt
    @PostMapping("/register")
    public R<Void> register(@Validated @RequestBody RegisterBody user) {
        if (!configService.selectRegisterEnabled(user.getTenantId())) {
            return R.fail("当前系统没有开启注册功能！");
        }
        registerService.register(user);
        return R.ok();
    }

    /**
     * 用户注册, 不需要参数加密
     */
    @PostMapping("/registerUser")
    public R<Void> registerUser(@Validated @RequestBody RegisterBody user) {
        if (!configService.selectRegisterEnabled(user.getTenantId())) {
            return R.fail("当前系统没有开启注册功能！");
        }
        registerService.register(user);
        return R.ok();
    }

    /**
     * 登录页面租户下拉框
     *
     * @return 租户列表
     */
    //@GetMapping("/tenant/list")
    public R<LoginTenantVo> tenantList(HttpServletRequest request) throws Exception {
        List<SysTenantVo> tenantList = tenantService.queryList(new SysTenantBo());
        List<TenantListVo> voList = MapstructUtils.convert(tenantList, TenantListVo.class);
        // 获取域名
        String host;
        String referer = request.getHeader("referer");
        if (StringUtils.isNotBlank(referer)) {
            // 这里从referer中取值是为了本地使用hosts添加虚拟域名，方便本地环境调试
            host = referer.split("//")[1].split("/")[0];
        } else {
            host = new URL(request.getRequestURL().toString()).getHost();
        }
        // 根据域名进行筛选
        List<TenantListVo> list = StreamUtils.filter(voList, vo ->
                StringUtils.equals(vo.getDomain(), host));
        // 返回对象
        LoginTenantVo vo = new LoginTenantVo();
        vo.setVoList(CollUtil.isNotEmpty(list) ? list : voList);
        vo.setTenantEnabled(TenantHelper.isEnable());
        return R.ok(vo);
    }

    /**
     * 登录页面租户下拉框
     *
     * @return 租户列表
     */
    @GetMapping("/tenant/{id}")
    public R<TenantListVo> getTenantInfo(@NotNull(message = "机构id不能为空")
                                  @PathVariable String id) {
        SysTenantVo sysTenantVo = tenantService.queryByTenantId(id);
        log.info("sysTenantVo:{}", JsonUtils.toJsonString(sysTenantVo));
        if (sysTenantVo == null) {
            return R.fail(MessageUtils.message("tenant.not.exists"));
        }
        TenantListVo vo = new TenantListVo();
        vo.setTenantId(sysTenantVo.getTenantId());
        vo.setCompanyName(sysTenantVo.getCompanyName());
        vo.setLogo(sysTenantVo.getLogo());
        vo.setLogoHeight(sysTenantVo.getLogoHeight());
        vo.setLogoWidth(sysTenantVo.getLogoWidth());
        vo.setCompanyShortName(sysTenantVo.getCompanyShortName());
        vo.setSystemBg(sysTenantVo.getSystemBg());
        vo.setSystemName(sysTenantVo.getSystemName());
        vo.setEnglishName(sysTenantVo.getEnglishName());
        vo.setParentTenantId(sysTenantVo.getParentTenantId());
        if (sysTenantVo.getLogo()== null && sysTenantVo.getSystemBg() == null) {
            return R.ok(vo);
        }
        List<Long> ossIds = new ArrayList<>();
        if (sysTenantVo.getLogo() != null) {
            ossIds.add(sysTenantVo.getLogo());
        }
        if (sysTenantVo.getSystemBg() != null) {
            ossIds.add(sysTenantVo.getSystemBg());
        }
        List<SysOssVo> sysOssVos = ossService.listByIds(ossIds);
        if (CollUtil.isEmpty(sysOssVos)) {
            return R.ok(vo);
        }
        // 返回信息加入图片访问前缀
        OssClient instance = OssFactory.instance();
        if (instance == null) {
            return R.ok(vo);
        }
        sysOssVos.forEach(ossVo -> {
            if (sysTenantVo.getLogo() != null && ossVo.getOssId().equals(sysTenantVo.getLogo())) {
                vo.setLogoUrl(instance.getUrl() + StringUtils.SLASH + ossVo.getFileName());
            } else if (ossVo.getOssId().equals(sysTenantVo.getSystemBg())) {
                vo.setSystemBgUrl(instance.getUrl() + StringUtils.SLASH + ossVo.getFileName());
            }
        });
        return R.ok(vo);
    }
}
