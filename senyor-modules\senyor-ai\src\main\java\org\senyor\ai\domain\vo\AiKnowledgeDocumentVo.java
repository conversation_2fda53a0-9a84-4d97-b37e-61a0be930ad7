package org.senyor.ai.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.senyor.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * AI知识库文档视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI知识库文档视图对象")
public class AiKnowledgeDocumentVo extends BaseEntity {

    /**
     * 文档ID
     */
    @Schema(description = "文档ID")
    private Long documentId;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String knowledgeName;

    /**
     * 文档名称
     */
    @Schema(description = "文档名称")
    private String documentName;

    /**
     * 文档类型（text:文本, pdf:PDF, docx:Word, txt:纯文本）
     */
    @Schema(description = "文档类型")
    private String documentType;

    /**
     * 文档内容
     */
    @Schema(description = "文档内容")
    private String content;

    /**
     * 文档大小（字节）
     */
    @Schema(description = "文档大小")
    private Long fileSize;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文档状态（0:待处理 1:处理中 2:已完成 3:失败）
     */
    @Schema(description = "文档状态")
    private String status;

    /**
     * 处理进度（0-100）
     */
    @Schema(description = "处理进度")
    private Integer progress;

    /**
     * 向量化状态（0:未向量化 1:向量化中 2:已完成 3:失败）
     */
    @Schema(description = "向量化状态")
    private String vectorStatus;

    /**
     * 分块数量
     */
    @Schema(description = "分块数量")
    private Integer chunkCount;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private Long userId;

    /**
     * 创建用户名
     */
    @Schema(description = "创建用户名")
    private String userName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
