// AI配置对象类型
export interface AiConfig {
  // 配置ID
  configId?: number;
  // 配置名称
  configName: string;
  // 配置描述
  configDesc?: string;
  // 模型名称
  model: string;
  // 温度参数(0-1之间)
  temperature: number;
  // 最大令牌数
  maxTokens: number;
  // 系统提示词
  systemPrompt?: string;
  // 绑定知识库ID
  knowledgeId?: number;
  // 创建用户ID
  userId?: number;
  // 创建用户名
  userName?: string;
  // 是否默认配置(Y是 N否)
  isDefault?: string;
  // 是否系统级配置(Y是 N否)
  isSystem?: string;
  // 是否启用(0启用 1禁用)
  status?: string;
  // 备注
  remark?: string;
  // 创建时间
  createTime?: string;
  // 更新时间
  updateTime?: string;
}

// AI模型选项
export const modelOptions = [
  { label: 'Qwen Max', value: 'qwen-max' },
  { label: 'Qwen Plus', value: 'qwen-plus' },
  { label: 'Qwen Turbo', value: 'qwen-turbo' },
  { label: 'DeepSeek R1', value: 'deepseek-r1' },
  { label: 'LLaMa 3', value: 'llama3-8b-chat' }
]; 