package org.senyor.ai.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.senyor.ai.domain.AiConversation;
import org.senyor.ai.domain.AiMessage;
import org.senyor.ai.domain.dto.ChatRequest;
import org.senyor.ai.domain.dto.ChatResponse;
import org.senyor.ai.domain.dto.FeedbackRequest;
import org.senyor.ai.service.IAiChatService;
import org.senyor.ai.service.IAiConversationService;
import org.senyor.ai.service.IVectorizationService;
import org.senyor.ai.service.impl.RedisVectorStoreServiceImpl;
import org.senyor.common.core.domain.R;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import io.reactivex.Flowable;

/**
 * AI聊天控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/chat")
@Tag(name = "AI聊天API", description = "AI聊天相关接口")
public class AiChatController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(AiChatController.class);

    private final IAiChatService aiChatService;
    private final IAiConversationService aiConversationService;
    private final RedisVectorStoreServiceImpl redisVectorStoreService;
    private final IVectorizationService vectorizationService;

    /**
     * 创建新会话
     */
    @PostMapping("/create")
    @Operation(summary = "创建会话", description = "创建新的AI对话会话")
    @SaCheckPermission("ai:chat:create")
    public R<String> createConversation(@RequestBody AiConversation conversation) {
        // 设置当前用户信息
        Long userId = LoginHelper.getUserId();
        String userName = LoginHelper.getUsername();
        conversation.setUserId(userId);
        conversation.setUserName(userName);

        // 创建会话
        String conversationId = aiConversationService.createAiConversation(conversation);
        return R.ok(conversationId);
    }

    /**
     * 流式发送消息并获取AI回复
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式聊天", description = "流式发送消息并获取AI回复")
    @SaCheckPermission("ai:chat:stream")
    public Flowable<ChatResponse> streamChat(@RequestBody @Validated ChatRequest chatRequest) {
        return aiChatService.streamChat(chatRequest);
    }

    /**
     * 获取向量存储统计信息
     */
    @GetMapping("/vector/stats")
    @Operation(summary = "获取向量统计", description = "获取Redis向量存储统计信息")
    @SaCheckPermission("ai:chat:vector:stats")
    public R<Map<String, Object>> getVectorStats() {
        try {
            String indexName = "ai_knowledge_vectors";
            Map<String, Object> stats = redisVectorStoreService.getVectorStats(indexName);
            return R.ok(stats);
        } catch (Exception e) {
            log.error("获取向量统计信息失败", e);
            return R.fail("获取向量统计信息失败");
        }
    }

    /**
     * 清理过期向量数据
     */
    @PostMapping("/vector/cleanup")
    @Operation(summary = "清理过期向量", description = "清理过期的向量数据")
    @SaCheckPermission("ai:chat:vector:cleanup")
    public R<Integer> cleanupExpiredVectors() {
        try {
            String indexName = "ai_knowledge_vectors";
            int cleanedCount = redisVectorStoreService.cleanupExpiredVectors(indexName);
            return R.ok(cleanedCount);
        } catch (Exception e) {
            log.error("清理过期向量数据失败", e);
            return R.fail("清理过期向量数据失败");
        }
    }



    /**
     * 手动触发文档向量化
     */
    @PostMapping("/vector/vectorize/{documentId}")
    @Operation(summary = "文档向量化", description = "手动触发文档向量化")
    @SaCheckPermission("ai:chat:vector:vectorize")
    public R<String> vectorizeDocument(@PathVariable("documentId") Long documentId) {
        try {
            boolean success = vectorizationService.vectorizeDocument(documentId);
            if (success) {
                log.info("向量化任务已启动，文档ID: {}", documentId);
                return R.ok("向量化任务已启动，请稍后查看状态");
            } else {
                return R.fail("向量化任务启动失败，请检查文档状态");
            }
        } catch (Exception e) {
            log.error("文档向量化失败，文档ID: {}", documentId, e);
            return R.fail("文档向量化失败");
        }
    }

    /**
     * 批量向量化文档
     */
    @PostMapping("/vector/batch-vectorize")
    @Operation(summary = "批量向量化", description = "批量向量化多个文档")
    @SaCheckPermission("ai:chat:vector:vectorize")
    public R<Map<String, Object>> batchVectorizeDocuments(@RequestBody List<Long> documentIds) {
        try {
            int successCount = vectorizationService.batchVectorizeDocuments(documentIds);
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("totalCount", documentIds.size());
            result.put("message", String.format("批量向量化任务已启动，共 %d 个文档", successCount));
            
            log.info("批量向量化任务已启动，成功: {}/{}", successCount, documentIds.size());
            return R.ok(result);
        } catch (Exception e) {
            log.error("批量向量化失败", e);
            return R.fail("批量向量化失败");
        }
    }

    /**
     * 向量化知识库下的所有文档
     */
    @PostMapping("/vector/vectorize-knowledge/{knowledgeId}")
    @Operation(summary = "知识库向量化", description = "向量化知识库下的所有文档")
    @SaCheckPermission("ai:chat:vector:vectorize")
    public R<Map<String, Object>> vectorizeKnowledgeBase(@PathVariable("knowledgeId") Long knowledgeId) {
        try {
            int successCount = vectorizationService.vectorizeKnowledgeBase(knowledgeId);
            Map<String, Object> result = new HashMap<>();
            result.put("successCount", successCount);
            result.put("knowledgeId", knowledgeId);
            result.put("message", String.format("知识库向量化任务已启动，共 %d 个文档", successCount));
            
            log.info("知识库向量化任务已启动，知识库ID: {}, 成功: {}", knowledgeId, successCount);
            return R.ok(result);
        } catch (Exception e) {
            log.error("知识库向量化失败，知识库ID: {}", knowledgeId, e);
            return R.fail("知识库向量化失败");
        }
    }

    /**
     * 重新向量化文档
     */
    @PostMapping("/vector/re-vectorize/{documentId}")
    @Operation(summary = "重新向量化", description = "重新向量化文档")
    @SaCheckPermission("ai:chat:vector:vectorize")
    public R<String> reVectorizeDocument(@PathVariable("documentId") Long documentId) {
        try {
            boolean success = vectorizationService.reVectorizeDocument(documentId);
            if (success) {
                log.info("重新向量化任务已启动，文档ID: {}", documentId);
                return R.ok("重新向量化任务已启动，请稍后查看状态");
            } else {
                return R.fail("重新向量化任务启动失败");
            }
        } catch (Exception e) {
            log.error("重新向量化失败，文档ID: {}", documentId, e);
            return R.fail("重新向量化失败");
        }
    }

    /**
     * 获取向量化进度
     */
    @GetMapping("/vector/progress/{documentId}")
    @Operation(summary = "获取向量化进度", description = "获取文档向量化进度")
    @SaCheckPermission("ai:chat:vector:stats")
    public R<Integer> getVectorizationProgress(@PathVariable("documentId") Long documentId) {
        try {
            int progress = vectorizationService.getVectorizationProgress(documentId);
            return R.ok(progress);
        } catch (Exception e) {
            log.error("获取向量化进度失败，文档ID: {}", documentId, e);
            return R.fail("获取向量化进度失败");
        }
    }

    /**
     * 调试向量数据
     */
    @GetMapping("/vector/debug")
    @Operation(summary = "调试向量数据", description = "检查Redis中的向量数据")
    @SaCheckPermission("ai:chat:vector:stats")
    public R<Map<String, Object>> debugVectorData() {
        try {
            Map<String, Object> debugInfo = redisVectorStoreService.debugVectorData("ai_knowledge_vectors");
            return R.ok(debugInfo);
        } catch (Exception e) {
            log.error("调试向量数据失败", e);
            return R.fail("调试向量数据失败");
        }
    }

    /**
     * 清理旧的向量数据
     */
    @PostMapping("/vector/cleanup-old")
    @Operation(summary = "清理旧向量数据", description = "清理使用旧key格式的向量数据")
    @SaCheckPermission("ai:chat:vector:vectorize")
    public R<String> cleanupOldVectorData() {
        try {
            // 清理旧的向量数据（使用租户前缀的key）
            int cleanedCount = redisVectorStoreService.cleanupOldVectorData();
            return R.ok("清理完成，共清理 " + cleanedCount + " 个旧向量数据");
        } catch (Exception e) {
            log.error("清理旧向量数据失败", e);
            return R.fail("清理旧向量数据失败");
        }
    }

    /**
     * 获取会话历史消息
     */
    @GetMapping("/history/{conversationId}")
    @Operation(summary = "获取历史消息", description = "根据会话ID获取历史消息")
    @SaCheckPermission("ai:chat:history")
    public R<List<AiMessage>> getHistoryMessages(@PathVariable String conversationId) {
        return R.ok(aiChatService.getHistoryMessages(conversationId));
    }

    /**
     * 获取用户的会话列表
     */
    @GetMapping("/conversations")
    @Operation(summary = "获取会话列表", description = "获取当前用户的所有会话")
    @SaCheckPermission("ai:chat:list")
    public R<List<AiConversation>> getConversations() {
        return R.ok(aiConversationService.getAiConversationsByUserId(LoginHelper.getUserId()));
    }

    /**
     * 删除会话
     */
    @PostMapping("/delete/{conversationId}")
    @Operation(summary = "删除会话", description = "删除指定会话及其所有消息")
    @SaCheckPermission("ai:chat:delete")
    public R<Integer> deleteConversation(@PathVariable String conversationId) {
        return R.ok(aiConversationService.deleteAiConversationById(conversationId));
    }

    /**
     * 清空会话消息
     */
    @PostMapping("/clear/{conversationId}")
    @Operation(summary = "清空会话消息", description = "清空指定会话的所有消息")
    @SaCheckPermission("ai:chat:clear")
    public R<Integer> clearConversationMessages(@PathVariable String conversationId) {
        return R.ok(aiChatService.clearMessages(conversationId));
    }

    /**
     * 更新会话标题
     */
    @PostMapping("/update/title/{conversationId}/{title}")
    @Operation(summary = "更新会话标题", description = "更新指定会话的标题")
    @SaCheckPermission("ai:chat:update")
    public R<Integer> updateConversationTitle(@PathVariable String conversationId, @PathVariable String title) {
        AiConversation conversation = new AiConversation();
        conversation.setConversationId(conversationId);
        conversation.setTitle(title);
        return R.ok(aiConversationService.updateAiConversation(conversation));
    }

    /**
     * 更新系统提示词
     */
    @PostMapping("/update/prompt/{conversationId}")
    @Operation(summary = "更新系统提示词", description = "更新指定会话的系统提示词")
    @SaCheckPermission("ai:chat:update")
    public R<Integer> updateSystemPrompt(@PathVariable String conversationId, @RequestBody AiConversation conversation) {
        conversation.setConversationId(conversationId);
        return R.ok(aiConversationService.updateAiConversation(conversation));
    }

    /**
     * AI消息点赞/点踩
     */
    @PostMapping("/feedback")
    @Operation(summary = "AI消息点赞/点踩", description = "对AI消息进行点赞或点踩")
    @SaCheckPermission("ai:chat:feedback")
    public R<Void> feedback(@RequestBody FeedbackRequest req) {
        aiChatService.feedback(req.getConversationId(), req.getMessageId(), req.getLikeStatus());
        return R.ok();
    }
}
