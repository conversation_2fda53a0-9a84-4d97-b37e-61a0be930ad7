package org.senyor.ai.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.senyor.ai.domain.AiConversation;
import org.senyor.ai.domain.AiMessage;
import org.senyor.ai.domain.dto.ChatRequest;
import org.senyor.ai.domain.dto.FeedbackRequest;
import org.senyor.ai.service.IAiChatService;
import org.senyor.ai.service.IAiConversationService;
import org.senyor.common.core.domain.R;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;

import io.reactivex.schedulers.Schedulers;

/**
 * AI聊天控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/chat")
@Tag(name = "AI聊天API", description = "AI聊天相关接口")
public class AiChatController extends BaseController {

    private static final Logger log = LoggerFactory.getLogger(AiChatController.class);

    private final IAiChatService aiChatService;
    private final IAiConversationService aiConversationService;

    /**
     * 创建新会话
     */
    @PostMapping("/create")
    @Operation(summary = "创建会话", description = "创建新的AI对话会话")
    @SaCheckPermission("ai:chat:create")
    public R<String> createConversation(@RequestBody AiConversation conversation) {
        // 设置当前用户信息
        Long userId = LoginHelper.getUserId();
        String userName = LoginHelper.getUsername();
        conversation.setUserId(userId);
        conversation.setUserName(userName);

        // 创建会话
        String conversationId = aiConversationService.createAiConversation(conversation);
        return R.ok(conversationId);
    }

    /**
     * 流式发送消息并获取AI回复
     */
    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Operation(summary = "流式发送消息", description = "流式发送消息并实时获取AI回复")
    @SaCheckPermission("ai:chat:send")
    public SseEmitter streamMessage(@RequestBody ChatRequest chatRequest) {
        // 设置长超时时间，确保流可以保持打开状态
        SseEmitter emitter = new SseEmitter(180000L); // 3分钟超时

        log.info("开始流式聊天，会话ID: {}", chatRequest.getConversationId());

        // 设置SSE连接成功的回调
        emitter.onCompletion(() -> log.info("SSE完成，会话ID: {}", chatRequest.getConversationId()));
        emitter.onTimeout(() -> log.warn("SSE超时，会话ID: {}", chatRequest.getConversationId()));
        emitter.onError((ex) -> log.error("SSE错误，会话ID: {}，错误: {}", chatRequest.getConversationId(), ex.getMessage()));

        // 设置响应头
        SseEmitter.SseEventBuilder eventBuilder = SseEmitter.event();

        // 发送初始化事件
        try {
            emitter.send(SseEmitter.event()
                .name("init")
                .data("连接已建立")
                .id("0"));
        } catch (IOException e) {
            log.error("发送初始化事件失败", e);
            emitter.completeWithError(e);
            return emitter;
        }

        // 异步处理流式响应
        aiChatService.streamChat(chatRequest)
            .subscribeOn(Schedulers.io())
            .subscribe(
                // 成功处理消息
                chatResponse -> {
                    try {
                        // 设置data格式，确保遵循SSE规范
                        String jsonData = objectToJson(chatResponse);
                        log.debug("发送流式数据，会话ID: {}, 内容: {}",
                                chatRequest.getConversationId(),
                                jsonData);

                        emitter.send(SseEmitter.event()
                            .data(jsonData)
                            .id(String.valueOf(System.currentTimeMillis())));
                    } catch (IOException e) {
                        log.error("发送流式数据失败，会话ID: {}，错误: {}", chatRequest.getConversationId(), e.getMessage());
                        emitter.completeWithError(e);
                    }
                },
                // 处理错误
                error -> {
                    log.error("处理流式响应出错，会话ID: {}，错误: {}", chatRequest.getConversationId(), error.getMessage());
                    emitter.completeWithError(error);
                },
                // 处理完成
                () -> {
                    try {
                        // 发送完成事件
                        emitter.send(SseEmitter.event()
                            .name("done")
                            .data("[DONE]")
                            .id("last"));

                        log.info("流式响应完成，会话ID: {}", chatRequest.getConversationId());
                        emitter.complete();
                    } catch (IOException e) {
                        log.error("发送完成事件失败", e);
                        emitter.completeWithError(e);
                    }
                }
            );

        return emitter;
    }

    /**
     * 对象转JSON字符串
     */
    private String objectToJson(Object obj) {
        try {
            return new com.fasterxml.jackson.databind.ObjectMapper().writeValueAsString(obj);
        } catch (Exception e) {
            log.error("JSON转换失败", e);
            return "{}";
        }
    }

    /**
     * 获取会话历史消息
     */
    @GetMapping("/history/{conversationId}")
    @Operation(summary = "获取历史消息", description = "根据会话ID获取历史消息")
    @SaCheckPermission("ai:chat:history")
    public R<List<AiMessage>> getHistoryMessages(@PathVariable String conversationId) {
        return R.ok(aiChatService.getHistoryMessages(conversationId));
    }

    /**
     * 获取用户的会话列表
     */
    @GetMapping("/conversations")
    @Operation(summary = "获取会话列表", description = "获取当前用户的所有会话")
    @SaCheckPermission("ai:chat:list")
    public R<List<AiConversation>> getConversations() {
        return R.ok(aiConversationService.getAiConversationsByUserId(LoginHelper.getUserId()));
    }

    /**
     * 删除会话
     */
    @PostMapping("/delete/{conversationId}")
    @Operation(summary = "删除会话", description = "删除指定会话及其所有消息")
    @SaCheckPermission("ai:chat:delete")
    public R<Integer> deleteConversation(@PathVariable String conversationId) {
        return R.ok(aiConversationService.deleteAiConversationById(conversationId));
    }

    /**
     * 清空会话消息
     */
    @PostMapping("/clear/{conversationId}")
    @Operation(summary = "清空会话消息", description = "清空指定会话的所有消息")
    @SaCheckPermission("ai:chat:clear")
    public R<Integer> clearConversationMessages(@PathVariable String conversationId) {
        return R.ok(aiChatService.clearMessages(conversationId));
    }

    /**
     * 更新会话标题
     */
    @PostMapping("/update/title/{conversationId}/{title}")
    @Operation(summary = "更新会话标题", description = "更新指定会话的标题")
    @SaCheckPermission("ai:chat:update")
    public R<Integer> updateConversationTitle(@PathVariable String conversationId, @PathVariable String title) {
        AiConversation conversation = new AiConversation();
        conversation.setConversationId(conversationId);
        conversation.setTitle(title);
        return R.ok(aiConversationService.updateAiConversation(conversation));
    }

    /**
     * 更新系统提示词
     */
    @PostMapping("/update/prompt/{conversationId}")
    @Operation(summary = "更新系统提示词", description = "更新指定会话的系统提示词")
    @SaCheckPermission("ai:chat:update")
    public R<Integer> updateSystemPrompt(@PathVariable String conversationId, @RequestBody AiConversation conversation) {
        conversation.setConversationId(conversationId);
        return R.ok(aiConversationService.updateAiConversation(conversation));
    }

    /**
     * AI消息点赞/点踩
     */
    @PostMapping("/feedback")
    @Operation(summary = "AI消息点赞/点踩", description = "对AI消息进行点赞或点踩")
    @SaCheckPermission("ai:chat:feedback")
    public R<Void> feedback(@RequestBody FeedbackRequest req) {
        aiChatService.feedback(req.getConversationId(), req.getMessageId(), req.getLikeStatus());
        return R.ok();
    }
}
