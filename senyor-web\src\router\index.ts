import { createWebHistory, createRouter, RouteRecordRaw } from 'vue-router';
/* Layout */
import Layout from '@/layout/index.vue';
import Watermark from '@/utils/watermark';
import { useUserStore } from '@/store/modules/user';

/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

// 公共路由
export const constantRoutes: RouteRecordRaw[] = [
    {
        path: '/redirect',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '/redirect/:path(.*)',
                component: () => import('@/views/redirect/index.vue')
            }
        ]
    },
    {
        path: '/bigData',
        hidden: true,
        component: () => import('@/components/DataScreen/dashboard.vue')
    },
    {
        path: '/social-callback',
        hidden: true,
        component: () => import('@/layout/components/SocialCallback/index.vue')
    },
    {
        path: '/login',
        component: () => import('@/views/login.vue'),
        hidden: true
    },
    {
        path: '/register',
        component: () => import('@/views/register.vue'),
        hidden: true
    },
    {
        path: '/application',
        component: () => import('@/views/application.vue'),
        hidden: true
    },
    {
        path: '/talentApplication',
        component: () => import('@/views/talentApplication.vue'),
        hidden: true
    },
    {
        path: '/:pathMatch(.*)*',
        component: () => import('@/views/error/404.vue'),
        hidden: true
    },
    {
        path: '/401',
        component: () => import('@/views/error/401.vue'),
        hidden: true
    },
    {
        path: '',
        component: Layout,
        redirect: '/index',
        children: [
            {
                path: '/index',
                component: () => import('@/views/home/<USER>'),
                name: 'Index',
                meta: { title: '首页', icon: 'dashboard', affix: true }
            }
        ]
    },
    {
        path: '/user',
        component: Layout,
        hidden: true,
        redirect: 'noredirect',
        children: [
            {
                path: 'profile',
                component: () => import('@/views/system/user/profile/index.vue'),
                name: 'Profile',
                meta: { title: '个人中心', icon: 'user' }
            }
        ]
    },
    // 文档管理
    {
        path: '/ai/knowledge/documents/:knowledgeId',
        component: Layout,
        hidden: true,
        children: [
            {
                path: '',
                component: () => import('@/views/ai/knowledge/documents.vue'),
                name: 'AiDocuments',
                meta: { 
                    title: '文档管理', 
                    icon: 'document',
                    hidden: true
                }
            }
        ]
    }
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes: RouteRecordRaw[] = [
    {
        path: '/base/user-auth',
        component: Layout,
        hidden: true,
        permissions: ['system:user:edit'],
        children: [
            {
                path: 'role/:userId(\\d+)',
                component: () => import('@/views/system/user/authRole.vue'),
                name: 'AuthRole',
                meta: { title: '分配角色', activeMenu: '/base/user', icon: '' }
            }
        ]
    },
    {
        path: '/system/cooperation_application',
        component: Layout,
        hidden: true,
        permissions: ['system:cooperation_application:list'],
        children: [
            {
                path: 'tenant-list',
                component: () => import('@/views/system/cooperation_application/tenant-list.vue'),
                name: 'TenantList',
                meta: { title: '寻找合作单位', activeMenu: '/system/cooperation_application/index', icon: '' }
            }
        ]
    },
    {
        path: '/base/role-auth',
        component: Layout,
        hidden: true,
        permissions: ['system:role:edit'],
        children: [
            {
                path: 'user/:roleId(\\d+)',
                component: () => import('@/views/system/role/authUser.vue'),
                name: 'AuthUser',
                meta: { title: '分配用户', activeMenu: '/base/role', icon: '' }
            }
        ]
    },
    {
        path: '/system/dict-data',
        component: Layout,
        hidden: true,
        permissions: ['system:dict:list'],
        children: [
            {
                path: 'dict/:dictId(\\d+)',
                component: () => import('@/views/system/dict/data.vue'),
                name: 'Data',
                meta: { title: '字典数据', activeMenu: '/system/dict', icon: '' }
            }
        ]
    },
    {
        path: '/scale/censusReport',
        component: Layout,
        permissions: ['scale:assessPlan:record'],
        hidden: true,
        children: [
            {
                path: 'index/:planId(\\d+)',
                component: () => import('@/views/scale/assessPlan/report.vue'),
                name: 'censusReport',
                meta: { title: '普查报告', activeMenu: '/scale/assessPlan', icon: '' }
            }
        ]
    },
    //     {
    //     path: 'userCenter/userProfile/electronicRecord',
    //     component: Layout,
    //     permissions: ['scale:assessPlan:record'],
    //     hidden: true,
    //     children: [
    //         {
    //             path: 'index',
    //             component: () => import('@/views/app/userProfile/electronicRecords.vue'),
    //             name: 'electronicRecord',
    //             meta: { title: '电子档案', activeMenu: '/scale/assessPlan', icon: '' }
    //         }
    //     ]
    // },
    {
        path: '/system/oss-config',
        component: Layout,
        hidden: true,
        permissions: ['system:ossConfig:list'],
        children: [
            {
                path: 'index',
                component: () => import('@/views/system/oss/config.vue'),
                name: 'OssConfig',
                meta: { title: '配置管理', activeMenu: '/system/oss', icon: '' }
            }
        ]
    },
    {
        path: '/app/text-data',
        component: Layout,
        hidden: true,
        permissions: ['app:textInfo:list'],
        children: [
            {
                path: 'text/:id(\\d+)',
                component: () => import('@/views/app/textInfo/data.vue'),
                name: 'textData', // 不能重复
                meta: { title: '知识内容', activeMenu: '/app/textInfo', icon: '' }
            }
        ]
    },
    {
        path: '/tool/gen-edit',
        component: Layout,
        hidden: true,
        permissions: ['tool:gen:edit'],
        children: [
            {
                path: 'index/:tableId(\\d+)',
                component: () => import('@/views/tool/gen/editTable.vue'),
                name: 'GenEdit',
                meta: { title: '修改生成配置', activeMenu: '/tool/gen', icon: '' }
            }
        ]
    },
    {
        path: '/demo/leaveEdit',
        component: Layout,
        hidden: true,
        permissions: ['demo:leave:edit'],
        children: [
            {
                path: 'index',
                component: () => import('@/views/workflow/leave/leaveEdit.vue'),
                name: 'leaveEdit',
                meta: { title: '请假申请', activeMenu: '/demo/leave', noCache: true }
            }
        ]
    }
];

/**
 * 创建路由
 */
const router = createRouter({
    history: createWebHistory(import.meta.env.VITE_APP_CONTEXT_PATH),
    //history: createWebHashHistory(),
    routes: constantRoutes,
    // 刷新时，滚动条位置还原
    scrollBehavior(to, from, savedPosition) {
        if (savedPosition) {
            return savedPosition;
        } else {
            return { top: 0 };
        }
    }
});

router.afterEach((to, from, next) => {
    if (['/login', '/bigData'].indexOf(to.path as string) !== -1) {
        // 登录页不加水印
        Watermark.out(); // 清除水印
    } else {
        // 获取pinia 中存储的用户信息
        const userStore = useUserStore();
        //从缓存中获取需要配置的水印信息
        Watermark.set(`${userStore.nickname}`);
    }
});

export default router;
