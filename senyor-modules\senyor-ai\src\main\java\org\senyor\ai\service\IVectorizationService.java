package org.senyor.ai.service;

import java.util.List;

/**
 * 向量化服务接口
 *
 * <AUTHOR>
 */
public interface IVectorizationService {

    /**
     * 向量化单个文档
     *
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean vectorizeDocument(Long documentId);

    /**
     * 批量向量化文档
     *
     * @param documentIds 文档ID列表
     * @return 成功数量
     */
    int batchVectorizeDocuments(List<Long> documentIds);

    /**
     * 向量化知识库下的所有文档
     *
     * @param knowledgeId 知识库ID
     * @return 成功数量
     */
    int vectorizeKnowledgeBase(Long knowledgeId);

    /**
     * 重新向量化文档
     *
     * @param documentId 文档ID
     * @return 是否成功
     */
    boolean reVectorizeDocument(Long documentId);

    /**
     * 获取向量化进度
     *
     * @param documentId 文档ID
     * @return 进度百分比
     */
    int getVectorizationProgress(Long documentId);
} 