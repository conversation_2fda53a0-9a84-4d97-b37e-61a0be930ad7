package org.senyor.ai.config;

import com.alibaba.dashscope.aigc.generation.Generation;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.senyor.ai.domain.AiConfig;
import org.senyor.ai.service.IAiConfigService;

/**
 * DashScope配置类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Configuration
public class DashScopeConfig {

    private static final Logger log = LoggerFactory.getLogger(DashScopeConfig.class);

    private final ApiKeyConfig apiKeyConfig;
    private final IAiConfigService aiConfigService;

    /**
     * 默认模型
     */
    @Getter
    @Value("${ai.dashscope.model:deepseek-r1}")
    private String defaultModel;

    /**
     * 温度参数
     */
    @Getter
    @Value("${ai.dashscope.temperature:0.7}")
    private Float temperature;

    /**
     * 最大令牌数
     */
    @Getter
    @Value("${ai.dashscope.max-tokens:1500}")
    private Integer maxTokens;

    /**
     * 获取DashScope API密钥
     *
     * @return API密钥
     */
    public String getDashscopeApiKey() {
        return apiKeyConfig.getDashscopeApiKey();
    }

    /**
     * 获取配置（优先使用数据库配置，没有则使用默认配置）
     *
     * @return 配置参数
     */
    public AiConfig getConfig() {
        // 尝试从数据库获取默认配置
        AiConfig config = aiConfigService.getDefaultConfig();
        if (config != null) {
            return config;
        }

        // 如果没有数据库配置，使用默认配置
        config = new AiConfig();
        config.setModel(defaultModel);
        config.setTemperature(temperature);
        config.setMaxTokens(maxTokens);

        return config;
    }

    /**
     * 配置DashScope客户端
     */
    @Bean(name = "dashScopeClient")
    public Generation dashScopeClient() {
        // 设置API密钥
        String apiKey = apiKeyConfig.getDashscopeApiKey();
        if (apiKey == null || apiKey.isEmpty()) {
            log.error("DashScope API密钥未配置，请检查环境变量或配置文件");
            throw new RuntimeException("DashScope API密钥未配置");
        }

        System.setProperty("DASHSCOPE_API_KEY", apiKey);

        // 创建Generation实例
        Generation generation = new Generation();

        return generation;
    }
}

