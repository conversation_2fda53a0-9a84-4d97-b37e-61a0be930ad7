package org.senyor.ai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.domain.AiKnowledgeChunk;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.mapper.AiKnowledgeChunkMapper;
import org.senyor.ai.mapper.AiKnowledgeDocumentMapper;
import org.senyor.ai.service.IEmbeddingService;
import org.senyor.ai.service.IVectorizationService;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 向量化服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VectorizationServiceImpl implements IVectorizationService {

    private final AiKnowledgeDocumentMapper aiKnowledgeDocumentMapper;
    private final AiKnowledgeChunkMapper aiKnowledgeChunkMapper;
    private final IEmbeddingService embeddingService;
    private final IVectorStoreService vectorStoreService;
    private final ObjectMapper objectMapper;

    // 向量索引名称
    private static final String VECTOR_INDEX_NAME = "ai_knowledge_vectors";

    // 线程池用于异步向量化
    private final ExecutorService vectorizationExecutor = Executors.newFixedThreadPool(3);

    @Override
    @Transactional
    public boolean vectorizeDocument(Long documentId) {
        try {
            log.info("开始向量化文档，文档ID: {}", documentId);

            // 获取文档信息
            AiKnowledgeDocument document = aiKnowledgeDocumentMapper.selectById(documentId);
            if (document == null) {
                log.error("文档不存在，文档ID: {}", documentId);
                return false;
            }

            // 检查文档状态
            if (!"2".equals(document.getStatus())) {
                log.error("文档状态不正确，无法向量化，文档ID: {}, 状态: {}", documentId, document.getStatus());
                return false;
            }

            // 更新文档向量化状态为处理中
            document.setVectorStatus("1");
            aiKnowledgeDocumentMapper.updateById(document);

            // 获取文档的所有分块
            List<AiKnowledgeChunk> chunks = aiKnowledgeChunkMapper.selectList(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getDocumentId, documentId)
                    .orderByAsc(AiKnowledgeChunk::getChunkIndex)
            );

            if (chunks.isEmpty()) {
                log.error("文档没有分块，无法向量化，文档ID: {}", documentId);
                document.setVectorStatus("3"); // 失败
                aiKnowledgeDocumentMapper.updateById(document);
                return false;
            }

            // 异步执行向量化
            CompletableFuture.runAsync(() -> {
                try {
                    performVectorization(document, chunks);
                } catch (Exception e) {
                    log.error("向量化处理失败，文档ID: {}", documentId, e);
                    // 更新失败状态
                    document.setVectorStatus("3");
                    aiKnowledgeDocumentMapper.updateById(document);
                }
            }, vectorizationExecutor);

            return true;
        } catch (Exception e) {
            log.error("向量化文档失败，文档ID: {}", documentId, e);
            return false;
        }
    }

    @Override
    @Transactional
    public int batchVectorizeDocuments(List<Long> documentIds) {
        int successCount = 0;
        for (Long documentId : documentIds) {
            if (vectorizeDocument(documentId)) {
                successCount++;
            }
        }
        log.info("批量向量化完成，成功: {}/{}", successCount, documentIds.size());
        return successCount;
    }

    @Override
    @Transactional
    public int vectorizeKnowledgeBase(Long knowledgeId) {
        // 获取知识库下所有已完成的文档
        List<AiKnowledgeDocument> documents = aiKnowledgeDocumentMapper.selectList(
            new LambdaQueryWrapper<AiKnowledgeDocument>()
                .eq(AiKnowledgeDocument::getKnowledgeId, knowledgeId)
                .eq(AiKnowledgeDocument::getStatus, "2") // 已完成的文档
                .ne(AiKnowledgeDocument::getVectorStatus, "2") // 未向量化的文档
        );

        List<Long> documentIds = documents.stream()
            .map(AiKnowledgeDocument::getDocumentId)
            .toList();

        return batchVectorizeDocuments(documentIds);
    }

    @Override
    @Transactional
    public boolean reVectorizeDocument(Long documentId) {
        try {
            log.info("开始重新向量化文档，文档ID: {}", documentId);

            // 删除现有的向量数据
            deleteExistingVectors(documentId);

            // 重新向量化
            return vectorizeDocument(documentId);
        } catch (Exception e) {
            log.error("重新向量化文档失败，文档ID: {}", documentId, e);
            return false;
        }
    }

    @Override
    public int getVectorizationProgress(Long documentId) {
        try {
            // 获取文档信息
            AiKnowledgeDocument document = aiKnowledgeDocumentMapper.selectById(documentId);
            if (document == null) {
                return 0;
            }

            // 根据向量化状态返回进度
            switch (document.getVectorStatus()) {
                case "0": return 0;    // 未向量化
                case "1": return 50;   // 向量化中
                case "2": return 100;  // 已完成
                case "3": return 0;    // 失败
                default: return 0;
            }
        } catch (Exception e) {
            log.error("获取向量化进度失败，文档ID: {}", documentId, e);
            return 0;
        }
    }

    /**
     * 执行向量化处理
     */
    private void performVectorization(AiKnowledgeDocument document, List<AiKnowledgeChunk> chunks) {
        try {
            log.info("开始执行向量化，文档ID: {}, 文档名称: {}, 分块数量: {}", 
                document.getDocumentId(), document.getDocumentName(), chunks.size());

            // 准备向量化数据 - 将文档名称与内容结合
            List<String> contents = new ArrayList<>();
            String documentName = document.getDocumentName();
            
            for (AiKnowledgeChunk chunk : chunks) {
                // 将文档名称作为关键元素与内容结合
                String enhancedContent = buildEnhancedContent(documentName, chunk.getContent());
                contents.add(enhancedContent);
            }

            // 调用嵌入服务获取向量
            List<List<Float>> embeddings = embeddingService.getEmbeddings(contents);

            if (embeddings.size() != chunks.size()) {
                throw new RuntimeException("向量化结果数量与分块数量不匹配");
            }

            // 准备向量存储数据
            List<IVectorStoreService.VectorData> vectorDataList = new ArrayList<>();

            for (int i = 0; i < chunks.size(); i++) {
                AiKnowledgeChunk chunk = chunks.get(i);
                List<Float> embedding = embeddings.get(i);

                // 更新分块的向量状态和向量数据
                chunk.setVectorStatus("2"); // 已完成
                chunk.setEmbedding(convertEmbeddingToString(embedding));
                aiKnowledgeChunkMapper.updateById(chunk);

                // 准备向量存储数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("documentId", chunk.getDocumentId());
                metadata.put("knowledgeId", chunk.getKnowledgeId());
                metadata.put("chunkIndex", chunk.getChunkIndex());
                metadata.put("chunkSize", chunk.getChunkSize());
                metadata.put("documentName", documentName);
                metadata.put("originalContent", chunk.getContent()); // 保存原始内容
                metadata.put("enhancedContent", contents.get(i)); // 保存增强内容

                String metadataJson = objectMapper.writeValueAsString(metadata);

                IVectorStoreService.VectorData vectorData = new IVectorStoreService.VectorData(
                    chunk.getChunkId(), chunk.getContent(), embedding, metadataJson
                );
                vectorDataList.add(vectorData);
            }

            // 批量存储向量到Redis
            int successCount = vectorStoreService.batchStoreVectors(VECTOR_INDEX_NAME, vectorDataList);
            log.info("向量存储完成，成功: {}/{}", successCount, chunks.size());

            // 更新文档向量化状态
            document.setVectorStatus("2"); // 已完成
            aiKnowledgeDocumentMapper.updateById(document);

            log.info("文档向量化完成，文档ID: {}, 文档名称: {}", document.getDocumentId(), documentName);

        } catch (Exception e) {
            log.error("向量化处理失败，文档ID: {}", document.getDocumentId(), e);

            // 更新失败状态
            document.setVectorStatus("3");
            aiKnowledgeDocumentMapper.updateById(document);

            // 更新分块状态
            for (AiKnowledgeChunk chunk : chunks) {
                chunk.setVectorStatus("3");
                aiKnowledgeChunkMapper.updateById(chunk);
            }

            throw new RuntimeException("向量化处理失败", e);
        }
    }

    /**
     * 构建增强内容，将文档名称作为关键元素
     * 
     * @param documentName 文档名称
     * @param content 原始内容
     * @return 增强后的内容
     */
    private String buildEnhancedContent(String documentName, String content) {
        if (documentName == null || documentName.trim().isEmpty()) {
            return content;
        }
        
        // 构建增强内容，将文档名称放在前面作为关键元素
        StringBuilder enhancedContent = new StringBuilder();
        enhancedContent.append("文档名称：").append(documentName).append("\n");
        enhancedContent.append("文档内容：\n").append(content);
        
        log.debug("构建增强内容 - 文档名称: {}, 原始内容长度: {}, 增强内容长度: {}", 
            documentName, content.length(), enhancedContent.length());
        
        return enhancedContent.toString();
    }

    /**
     * 删除现有的向量数据
     */
    private void deleteExistingVectors(Long documentId) {
        try {
            // 获取文档的所有分块
            List<AiKnowledgeChunk> chunks = aiKnowledgeChunkMapper.selectList(
                new LambdaQueryWrapper<AiKnowledgeChunk>()
                    .eq(AiKnowledgeChunk::getDocumentId, documentId)
            );

            // 从向量存储中删除
            for (AiKnowledgeChunk chunk : chunks) {
                if (chunk.getVectorId() != null) {
                    vectorStoreService.deleteVector(VECTOR_INDEX_NAME, Long.valueOf(chunk.getVectorId()));
                }
            }

            // 重置分块的向量状态
            for (AiKnowledgeChunk chunk : chunks) {
                chunk.setVectorStatus("0");
                chunk.setVectorId(null);
                chunk.setEmbedding(null);
                aiKnowledgeChunkMapper.updateById(chunk);
            }

            log.info("已删除文档的现有向量数据，文档ID: {}", documentId);
        } catch (Exception e) {
            log.error("删除现有向量数据失败，文档ID: {}", documentId, e);
        }
    }

    /**
     * 将向量转换为字符串存储
     */
    private String convertEmbeddingToString(List<Float> embedding) {
        try {
            return objectMapper.writeValueAsString(embedding);
        } catch (Exception e) {
            log.error("向量转字符串失败", e);
            return "[]";
        }
    }
}
