<template>
    <div class="report-wrapper">
        <!-- 导航栏 -->
        <div class="report-nav">
            <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" @select="handleSelect">
                <el-menu-item index="1">综合报告</el-menu-item>
                <el-menu-item index="2">个体报告</el-menu-item>
                <el-menu-item index="3">活动进度</el-menu-item>
                <el-menu-item index="4">活动信息</el-menu-item>
            </el-menu>
        </div>

        <!-- 综合报告内容区域 -->
        <div v-if="activeIndex === '1'" v-loading="loading" class="report-container">
            <!-- 报告基本信息卡片 -->
            <div class="report-card">
                <!-- 报告标题和导出按钮 -->
                <div class="title-container">
                    <h2 class="report-title">心理测评团体报告</h2>
                    <div class="export-buttons">
                        <el-button
                            v-hasPermi="['scale:assessPlan:exportPdf']"
                            plain
                            color="#578BF9"
                            :dark="isDark"
                            size="small"
                            class="export-btn"
                            @click="exportPdf"
                        >
                            导出PDF
                        </el-button>
                        <el-button
                            v-hasPermi="['scale:assessPlan:exportWord']"
                            plain
                            color="#57C9F9"
                            :dark="isDark"
                            size="small"
                            class="export-btn"
                            @click="exportWord"
                        >
                            导出Word
                        </el-button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="info-card">
                        <div class="info-card-content">
                            <div class="info-card-icon">
                                <img src="@/assets/images/book2.x.png" alt="活动图标" />
                            </div>
                            <div class="info-card-details">
                                <div class="info-row info-first-row">
                                    <div class="info-item">
                                        <span class="info-label">活动名称：</span>
                                        <span class="info-content">{{ reportData.planName }}</span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">活动周期：</span>
                                        <span class="info-content"
                                            >{{ formatDate(reportData.startTime) }} 至 {{ formatDate(reportData.endTime) }}</span
                                        >
                                    </div>
                                </div>
                                <div class="info-row">
                                    <span class="info-label">量表名称：</span>
                                    <span class="info-content">{{ reportData.scaleNames }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 心理测评活动引导卡片 -->
            <div class="report-card">
                <div class="info-card">
                    <div class="guide-title-container">
                        <div class="guide-title">心理测评活动引导</div>
                    </div>
                    <div class="card-body guide-card-body">
                        <div class="guide-content">
                            <!-- 使用el-row和el-col实现左右布局 -->
                            <el-row :gutter="20">
                                <!-- 左侧描述文本 -->
                                <el-col :lg="15" :md="14" :sm="24" :xs="24">
                                    <div class="guide-text">
                                        <p class="guide-paragraph">
                                            本次测评活动活动下的参与者有 {{ reportData.participantsNumber }} 人，实际完成人数
                                            {{ reportData.completerNumber }} 人，占总测试人数的
                                            {{ reportData.completionRate }}%。本测评报告针对《症状自评量表（SCL-90
                                            成人版）》《卡特尔十六种人格因素测验(16PF)》,《MBTI性格类型测试问卷》进行心理筛查。所有量表的结果仅供团体辅导等心理工作使用。
                                        </p>

                                        <p class="guide-paragraph">
                                            系统根据测评结果将测评者的心理状态划分为<span class="level-normal">正常</span>、<span
                                                class="level-attention"
                                                >关注</span
                                            >、<span class="level-tracking">追踪</span>、<span class="level-danger">高危</span>、<span
                                                class="level-warning"
                                                >警戒</span
                                            >五个等级，但各量表测评内容和评分标准存在差异，部分量表测评结果仅需一般关注或追踪，无需重点关注。请结合个体报告理解查看数据。
                                        </p>
                                    </div>
                                </el-col>

                                <!-- 右侧数据统计卡片 -->
                                <el-col :lg="9" :md="10" :sm="24" :xs="24" class="stats-col">
                                    <div class="stats-container">
                                        <div class="stat-card">
                                            <div class="stat-value-container">{{ reportData.participantsNumber }}</div>
                                            <div class="stat-label-container">参与人数</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value-container">{{ reportData.completerNumber }}</div>
                                            <div class="stat-label-container">完成人数</div>
                                        </div>
                                        <div class="stat-card">
                                            <div class="stat-value-container">{{ reportData.completionRate }}%</div>
                                            <div class="stat-label-container">完成率</div>
                                        </div>
                                    </div>
                                    <div class="stats-note">
                                        本报告为对测评者在本次活动中所填量表的测评结果进行的总体分析，请注意测评的结果可能会受到多种因素影响。
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 量表测评结果卡片 -->
            <div class="report-card">
                <div class="info-card">
                    <div class="guide-title-container">
                        <div class="guide-title">量表测评结果</div>
                    </div>
                    <div class="card-body">
                        <!-- 量表卡片区域 -->
                        <div class="scale-cards-container">
                            <div class="scale-horizontal-container" style="height: 70px; padding: 10px 0; margin-bottom: 20px">
                                <div
                                    v-for="(scale, index) in reportData.scaleInfoList"
                                    :key="index"
                                    class="scale-horizontal-item"
                                    :class="{ 'scale-horizontal-active': currentScaleId === scale.scaleId }"
                                    style="min-width: 180px; height: 50px; margin: 0 10px"
                                    @click="selectScale(scale)"
                                >
                                    <img
                                        v-if="currentScaleId === scale.scaleId"
                                        class="bg-image"
                                        src="@/assets/images/nav-bar-active.png"
                                        alt="背景"
                                    />
                                    <img v-else class="bg-image" src="@/assets/images/nav-bar.png" alt="背景" />
                                    <span class="item-text" style="font-size: 16px">{{ scale.scaleName }}</span>
                                </div>
                            </div>

                            <!-- 量表详情区域 -->
                            <div v-if="currentScale.scaleId" class="scale-detail-section">
                                <el-row>
                                    <!-- 左侧量表图片卡片 -->
                                    <el-col :span="9">
                                        <div class="scale-image-card">
                                            <div class="scale-image-container">
                                                <ImageDisplay
                                                    v-if="currentScale.scaleIcon"
                                                    v-model="currentScale.scaleIcon"
                                                    :width="335"
                                                    :height="335"
                                                />
                                                <div v-else class="scale-placeholder-image">
                                                    <img src="@/assets/images/book2.x.png" alt="量表图标" />
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>

                                    <!-- 右侧量表详情卡片 -->
                                    <el-col :span="15">
                                        <div class="scale-info-card">
                                            <div class="detail-header">
                                                <div class="detail-title">
                                                    <i class="detail-icon"></i>
                                                    <span>{{ currentScale.scaleName }} 量表简介</span>
                                                </div>
                                            </div>

                                            <div class="scale-detail-content">
                                                <p v-if="currentScale.introduction" class="detail-paragraph">{{ currentScale.introduction }}</p>
                                                <p v-else class="detail-paragraph">
                                                    {{ currentScale.scaleName }}是一种心理测量工具，用于评估个体的心理特征和状态。
                                                </p>

                                                <!-- <div v-if="currentScale.description" class="scale-description">
                                                    <p>{{ currentScale.description }}</p>
                                                </div> -->
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>
                                <el-row :gutter="20" class="additional-cards-row">
                                    <!-- 左侧注意事项卡片 -->
                                    <el-col :span="12">
                                        <div class="attention-card">
                                            <img src="@/assets/images/warning.png" alt="警告图标" class="warning-icon-top" />
                                            <div class="attention-header">
                                                <div class="attention-title">
                                                    <i class="attention-icon"></i>
                                                    <span>引导语</span>
                                                </div>
                                            </div>
                                            <div class="attention-content">
                                                <div class="safety-tip">
                                                    <div v-if="currentScale.description">
                                                        <p>{{ currentScale.description }}</p>
                                                    </div>
                                                    <div v-else>
                                                        <p>
                                                            <strong
                                                                >下面是对您可能存在的一些感受的描述，请仔细阅读每一条，然后根据您最近一个星期以内的实际感受，选择最符合内容的一项描述。</strong
                                                            >
                                                        </p>
                                                        <ol class="attention-list">
                                                            <li>如果你发现题目中所描述的情形很少出现，请选择A选项；</li>
                                                            <li>如果题目中描述的情形仅在有些时候出现，则选择B选项；</li>
                                                            <li>如果题目中描述的情形在大部分时间里出现，请选择C选项；</li>
                                                            <li>如果题目中的描述在绝大部分时间里都会出现，则选择D选项。</li>
                                                        </ol>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>

                                    <!-- 右侧完成情况卡片 -->
                                    <el-col :span="12">
                                        <div class="completion-card">
                                            <div class="completion-header">
                                                <div class="completion-title">
                                                    <i class="completion-icon"></i>
                                                    <span>完成情况</span>
                                                </div>
                                            </div>
                                            <div class="completion-content">
                                                <div class="completion-chart">
                                                    <div class="custom-table">
                                                        <table>
                                                            <thead>
                                                                <tr class="header-row">
                                                                    <th>机构/部门</th>
                                                                    <th>活动人数</th>
                                                                    <th>有效人数</th>
                                                                    <th>完成率</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                <tr
                                                                    v-for="(item, index) in completionData"
                                                                    :key="index"
                                                                    :class="{ 'warning-row': index === 1 }"
                                                                >
                                                                    <td class="first-column">{{ item.group }}</td>
                                                                    <td>{{ item.total }}</td>
                                                                    <td>{{ item.valid }}</td>
                                                                    <td>{{ item.rate }}%</td>
                                                                </tr>
                                                                <tr v-if="completionData.length === 0">
                                                                    <td colspan="4" class="no-data">暂无数据</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>

                                <!-- 总体预警卡片 -->
                                <el-row :gutter="20" class="warning-overview-row">
                                    <el-col :span="24">
                                        <div class="warning-overview-card">
                                            <div class="warning-header">
                                                <div class="warning-title">
                                                    <i class="warning-icon"></i>
                                                    <span>总体预警</span>
                                                </div>
                                            </div>
                                            <div class="warning-content">
                                                <el-row :gutter="20">
                                                    <!-- 左侧部分：统计数据、图表和小贴士 -->
                                                    <el-col :span="12">
                                                        <!-- 左侧统计数据 -->
                                                        <div class="warning-stats">
                                                            <!-- 图例部分 -->
                                                            <div class="warning-legend">
                                                                <div class="legend-row">
                                                                    <span class="legend-item"
                                                                        ><span class="legend-dot" style="background-color: #409eff"></span>
                                                                        已测人数({{ reportData.completerNumber || 0 }})</span
                                                                    >
                                                                    <span class="legend-item"
                                                                        ><span class="legend-dot" style="background-color: #c0c4cc"></span>
                                                                        未测人数({{
                                                                            reportData.participantsNumber - reportData.completerNumber || 0
                                                                        }})</span
                                                                    >
                                                                    <template v-if="warningLevelConfig.length > 0">
                                                                        <span
                                                                            v-for="level in warningLevelConfig"
                                                                            :key="level.warningLevelId"
                                                                            class="legend-item"
                                                                        >
                                                                            <span
                                                                                class="legend-dot"
                                                                                :style="{ backgroundColor: level.warningColor }"
                                                                            ></span>
                                                                            {{ level.warningName }}({{
                                                                                getWarningDataByLevelId(level.warningLevelId)
                                                                            }})
                                                                        </span>
                                                                    </template>
                                                                    <!-- <template v-else>
                                                                        <span class="legend-item"><span
                                                                                class="legend-dot"
                                                                                style="background-color: #42A246;"></span>
                                                                            正常({{ warningData.normal || 0 }})</span>
                                                                        <span class="legend-item"><span
                                                                                class="legend-dot"
                                                                                style="background-color: #1E90FF;"></span>
                                                                            关注({{ warningData.attention || 0 }})</span>
                                                                        <span class="legend-item"><span
                                                                                class="legend-dot"
                                                                                style="background-color: #FFFF00;"></span>
                                                                            追踪({{ warningData.tracking || 0 }})</span>
                                                                        <span class="legend-item"><span
                                                                                class="legend-dot"
                                                                                style="background-color: #FF8000;"></span>
                                                                            高危({{ warningData.danger || 0 }})</span>
                                                                        <span class="legend-item"><span
                                                                                class="legend-dot"
                                                                                style="background-color: #FF0000;"></span>
                                                                            警戒({{ warningData.warning || 0 }})</span>
                                                                    </template> -->
                                                                </div>
                                                            </div>
                                                        </div>

                                                        <!-- 图表和小贴士并排显示 -->
                                                        <div class="chart-tips-row">
                                                            <!-- 中间南丁格尔玫瑰图 -->
                                                            <div class="chart-container-half">
                                                                <div id="warningChart" ref="warningChartRef" class="warning-chart"></div>
                                                            </div>

                                                            <!-- 右侧预警描述卡片 -->
                                                            <div class="warning-description-card-half">
                                                                <div class="warning-desc-title">小贴士</div>
                                                                <div class="warning-desc-content">
                                                                    <template v-if="warningLevelConfig.length > 0">
                                                                        <div
                                                                            v-for="level in warningLevelConfig"
                                                                            :key="level.warningLevelId"
                                                                            class="warning-desc-item"
                                                                        >
                                                                            <div class="warning-desc-header">
                                                                                <span
                                                                                    class="warning-desc-name"
                                                                                    :style="{ color: level.warningColor }"
                                                                                    >{{ level.warningName }}</span
                                                                                >
                                                                                <span class="warning-desc-text">{{ level.warningIntro }}</span>
                                                                            </div>
                                                                        </div>
                                                                    </template>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </el-col>

                                                    <!-- 右侧部分：年级/班级预警率表格 -->
                                                    <el-col :span="12">
                                                        <div class="grade-warning-table">
                                                            <!-- <div class="grade-warning-title">测评统计</div> -->
                                                            <table class="custom-grade-table">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="background-color: #98b9ff">机构/部门</th>
                                                                        <template v-if="warningLevelConfig.length > 0">
                                                                            <th
                                                                                v-for="level in warningLevelConfig"
                                                                                :key="level.warningLevelId"
                                                                                :style="{ backgroundColor: level.warningColor }"
                                                                            >
                                                                                {{ level.warningName }}
                                                                            </th>
                                                                        </template>
                                                                        <template v-else>
                                                                            <th style="background-color: #42a246">正常</th>
                                                                            <th style="background-color: #1e90ff">关注</th>
                                                                            <th style="background-color: #ffff00">追踪</th>
                                                                            <th style="background-color: #ff8000">高危</th>
                                                                            <th style="background-color: #ff0000">警戒</th>
                                                                        </template>
                                                                        <th style="background-color: #e1e1e1">预警率</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr v-for="(item, index) in gradeWarningData" :key="index">
                                                                        <td style="background-color: #e0e9ff">{{ item.deptName }}</td>
                                                                        <template v-if="warningLevelConfig.length > 0">
                                                                            <td
                                                                                v-for="level in warningLevelConfig"
                                                                                :key="level.warningLevelId"
                                                                                :style="{ backgroundColor: getLighterColor(level.warningColor) }"
                                                                            >
                                                                                {{ item.warningLevels[level.warningLevelId] || 0 }}
                                                                            </td>
                                                                        </template>
                                                                        <template v-else>
                                                                            <td style="background-color: #e0ffe0">
                                                                                {{ item.warningLevels['1'] || 0 }}
                                                                            </td>
                                                                            <td style="background-color: #e0e9ff">
                                                                                {{ item.warningLevels['2'] || 0 }}
                                                                            </td>
                                                                            <td style="background-color: #ffffdc">
                                                                                {{ item.warningLevels['3'] || 0 }}
                                                                            </td>
                                                                            <td style="background-color: #ffe9dc">
                                                                                {{ item.warningLevels['4'] || 0 }}
                                                                            </td>
                                                                            <td style="background-color: #ffdcdc">
                                                                                {{ item.warningLevels['5'] || 0 }}
                                                                            </td>
                                                                        </template>
                                                                        <td style="background-color: #f5f5f5">{{ item.warningRate }}</td>
                                                                    </tr>
                                                                    <tr v-if="gradeWarningData.length === 0">
                                                                        <td colspan="7" class="no-data">暂无数据</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                            <div class="table-note">
                                                                <span style="font-weight: bold; color: #ff0000">注:</span>
                                                                预警率是指高危及警戒状态人群所占比例
                                                            </div>
                                                        </div>
                                                    </el-col>
                                                </el-row>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>

                                <!-- 因子预警（人数）模块 -->
                                <el-row :gutter="20" class="factor-warning-row">
                                    <el-col :span="24">
                                        <div class="factor-warning-card">
                                            <div class="factor-warning-header">
                                                <div class="factor-warning-title">
                                                    <i class="factor-warning-icon"></i>
                                                    <span>因子预警（人数）</span>
                                                </div>
                                            </div>
                                            <div class="factor-warning-content">
                                                <el-row :gutter="20">
                                                    <!-- 左侧表格 -->
                                                    <el-col :span="14">
                                                        <div class="factor-table-container">
                                                            <table class="factor-table">
                                                                <thead>
                                                                    <tr>
                                                                        <th
                                                                            v-for="(header, index) in factorData.headers"
                                                                            :key="index"
                                                                            style="background-color: #98b9ff"
                                                                        >
                                                                            {{ header }}
                                                                        </th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr
                                                                        v-for="(row, rowIndex) in factorData.rows"
                                                                        :key="rowIndex"
                                                                        :class="{ 'total-row': row.grade === '总计' }"
                                                                    >
                                                                        <td style="background-color: #e0e9ff">{{ row.grade }}</td>
                                                                        <td
                                                                            v-for="(header, headerIndex) in factorData.headers.slice(1)"
                                                                            :key="headerIndex"
                                                                        >
                                                                            {{ getRowValue(row, header) }}
                                                                        </td>
                                                                    </tr>
                                                                    <tr v-if="factorData.rows.length === 0">
                                                                        <td :colspan="factorData.headers.length" class="no-data">暂无数据</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </el-col>
                                                    <!-- 右侧折线图 -->
                                                    <el-col :span="10">
                                                        <div class="factor-chart-container">
                                                            <div id="factorChart" ref="factorChartRef" class="factor-chart"></div>
                                                        </div>
                                                    </el-col>
                                                </el-row>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>

                                <!-- 水平分析模块 -->
                                <el-row :gutter="20" class="horizontal-analysis-row">
                                    <el-col :span="24">
                                        <div class="horizontal-analysis-card">
                                            <div class="horizontal-analysis-header">
                                                <div class="horizontal-analysis-title">
                                                    <i class="horizontal-analysis-icon"></i>
                                                    <span>水平分析</span>
                                                </div>
                                            </div>
                                            <div class="horizontal-analysis-content">
                                                <el-row :gutter="20">
                                                    <!-- 左侧部分：上部分为柱状图，下部分为性别分析表格 -->
                                                    <el-col :span="12">
                                                        <!-- 上部分：柱状图 -->
                                                        <div class="horizontal-chart-container">
                                                            <div id="horizontalChart" ref="horizontalChartRef" class="horizontal-chart"></div>
                                                        </div>

                                                        <!-- 下部分：性别分析表格 -->
                                                        <div class="gender-analysis-container">
                                                            <div class="horizontal-analysis-title">
                                                                <i class="horizontal-analysis-icon"></i>
                                                                <span>性别分析</span>
                                                            </div>
                                                            <table class="gender-analysis-table">
                                                                <thead>
                                                                    <tr>
                                                                        <th style="background-color: #98b9ff">性别</th>
                                                                        <template v-if="warningLevelConfig && warningLevelConfig.length > 0">
                                                                            <th
                                                                                v-for="level in warningLevelConfig"
                                                                                :key="level.warningLevelId"
                                                                                :style="{ backgroundColor: level.warningColor }"
                                                                            >
                                                                                {{ level.warningName }}
                                                                            </th>
                                                                        </template>
                                                                        <template v-else>
                                                                            <th style="background-color: #42a246">正常</th>
                                                                            <th style="background-color: #1e90ff">关注</th>
                                                                            <th style="background-color: #ffff00">追踪</th>
                                                                            <th style="background-color: #ff8000">高危</th>
                                                                            <th style="background-color: #ff0000">警戒</th>
                                                                        </template>
                                                                        <th style="background-color: #e1e1e1">预警率</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr v-for="(item, index) in genderAnalysisData" :key="index">
                                                                        <td>{{ item.gender }}</td>
                                                                        <template v-if="warningLevelConfig && warningLevelConfig.length > 0">
                                                                            <td
                                                                                v-for="level in warningLevelConfig"
                                                                                :key="level.warningLevelId"
                                                                                :style="{ backgroundColor: getLighterColor(level.warningColor) }"
                                                                            >
                                                                                {{ getGenderWarningCount(item, level.warningLevelId) }}
                                                                            </td>
                                                                        </template>
                                                                        <template v-else>
                                                                            <td>{{ item.normal }}</td>
                                                                            <td>{{ item.attention }}</td>
                                                                            <td>{{ item.tracking }}</td>
                                                                            <td>{{ item.danger }}</td>
                                                                            <td>{{ item.warning }}</td>
                                                                        </template>
                                                                        <td>{{ item.warningRate }}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </el-col>

                                                    <!-- 右侧部分：大表格 -->
                                                    <el-col :span="12">
                                                        <div class="factor-level-container">
                                                            <table class="factor-level-table">
                                                                <thead>
                                                                    <tr>
                                                                        <th>因子名称</th>
                                                                        <th>最小值</th>
                                                                        <th>最大值</th>
                                                                        <th>平均分</th>
                                                                        <th>标准差</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <tr v-for="(item, index) in factorLevelData" :key="index">
                                                                        <td>{{ item.name }}</td>
                                                                        <td>{{ item.min }}</td>
                                                                        <td>{{ item.max }}</td>
                                                                        <td>{{ item.avg }}</td>
                                                                        <td>{{ item.std }}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </el-col>
                                                </el-row>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>

                                <!-- 心理健康指导建议模块 -->
                                <el-row :gutter="20" class="health-guidance-row">
                                    <el-col :span="24">
                                        <div class="health-guidance-card">
                                            <div class="health-guidance-header">
                                                <div class="health-guidance-title">
                                                    <i class="health-guidance-icon"></i>
                                                    <span>心理健康指导建议</span>
                                                </div>
                                            </div>
                                            <div class="health-guidance-content">
                                                <div class="health-guidance-text">
                                                    <p class="guidance-intro guidance-first-para">
                                                        心理健康的基本含义是指拥有心理的各个方面及活动过程处于一种良好正常的状态。心理健康的理想状态是保持性格完美、智力正常、认知正确、情感适当、意志合理、态度积极、行为恰当、适应良好的状态。当前社会生活节奏较快，各类人群都面临着来自生活、学习/工作、人际关系等方面的不同压力，容易出现心理困扰，对心理健康产生的需求也随之增加。
                                                    </p>
                                                    <p class="guidance-intro">建议开展心理工作可以从下几个方面入手：</p>
                                                    <ol class="guidance-list">
                                                        <li>
                                                            加强心理知识普及，增加心理咨询、评估、宣传外载体，提高心理知识，提升人们关注、维护自身心理健康的意识；
                                                        </li>
                                                        <li>
                                                            培养专业心理人才，创造条件发展心理问题定期训练体，注重实用性和实效性，提升开展心理工作的能力；
                                                        </li>
                                                        <li>定期组织心理健康讲座，分析需求、总结经验、传授方法，提高人们自助和心理调适的能力；</li>
                                                        <li>
                                                            多样化开展心理活动，如团体辅导、心理剧、减压训练等，提高活动的丰富性和趣味性，提高人们积极参与；
                                                        </li>
                                                        <li>提供心理咨询等各项专题服务，如心理评估、心理危机、解决问题等，提升心灵的舒适和能力。</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </el-col>
                                </el-row>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 个体报告内容区域 -->
        <div v-if="activeIndex === '2'" v-loading="loading" class="report-container">
            <div>
                <div class="title-container">
                    <h2 class="report-title">心理测评个体报告</h2>
                </div>

                <!-- 左右布局容器 -->
                <el-row :gutter="24">
                    <!-- 左侧量表卡片区域 -->
                    <el-col :span="7">
                        <div class="scale-cards-container-vertical">
                            <div class="scale-list-title">量表列表</div>
                            <div class="scale-list-desc">点击选择要查看的量表</div>
                            <div class="scale-cards-vertical">
                                <div
                                    v-for="(scale, index) in reportData.scaleInfoList"
                                    :key="index"
                                    class="scale-card-vertical"
                                    :class="{ 'active': currentIndividualScaleId === scale.scaleId }"
                                    @click="selectScaleForIndividual(scale)"
                                >
                                    <div class="scale-card-image-vertical">
                                        <ImageDisplay v-model="scale.scaleIcon" :width="350" :height="330" />
                                    </div>
                                    <div class="scale-card-title-vertical" :class="{ 'active-text': currentIndividualScaleId === scale.scaleId }">
                                        {{ scale.scaleName }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </el-col>

                    <!-- 右侧筛选和列表区域 -->
                    <el-col :span="17">
                        <!-- 个体筛选区域 -->
                        <div class="filter-area right-content">
                            <el-form :inline="true" class="filter-form" label-width="100px">
                                <el-form-item label="姓名/学号">
                                    <el-input v-model="individualFilter.keyword" placeholder="请输入姓名或学号" clearable class="rounded-input" />
                                </el-form-item>
                                <el-form-item label="预警等级">
                                    <el-select v-model="individualFilter.warningLevel" placeholder="请选择" clearable class="rounded-input">
                                        <el-option label="正常" value="normal" />
                                        <el-option label="关注" value="attention" />
                                        <el-option label="追踪" value="tracking" />
                                        <el-option label="高危" value="danger" />
                                        <el-option label="警戒" value="warning" />
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    <el-button type="primary" class="search-btn" :icon="Search" @click="searchIndividual"> 搜索</el-button>
                                    <el-button :icon="Refresh" @click="resetIndividualFilter">重置</el-button>
                                </el-form-item>
                            </el-form>
                        </div>

                        <!-- 个体列表区域 -->
                        <div class="individual-list right-content">
                            <el-table :data="filteredIndividualReports" style="width: 100%">
                                <el-table-column prop="nickName" label="姓名" align="center" />
                                <el-table-column prop="userId" label="用户ID" align="center" />
                                <el-table-column prop="createTime" label="测评时间" align="center" />
                                <el-table-column label="得分" align="center">
                                    <template #default="scope">
                                        {{ getScoreFromDetail(scope.row.scoreDetail) }}
                                    </template>
                                </el-table-column>
                                <el-table-column label="预警等级" align="center">
                                    <template #default="scope">
                                        <el-tag :type="getWarningTagTypeFromScore(getScoreFromDetail(scope.row.scoreDetail))">
                                            {{ getWarningLevelFromScore(getScoreFromDetail(scope.row.scoreDetail)) }}
                                        </el-tag>
                                    </template>
                                </el-table-column>
                                <el-table-column label="操作" align="center" width="180">
                                    <template #default="scope">
                                        <el-button
                                            style="background-color: #01bb9f; color: #ffffff; border-radius: 5px"
                                            size="small"
                                            @click="viewIndividualReport(scope.row)"
                                            >查看报告</el-button
                                        >
                                        <el-button
                                            style="background-color: #578bf9; color: #ffffff; border-radius: 5px"
                                            size="small"
                                            @click="exportIndividualReport(scope.row)"
                                            >导出</el-button
                                        >
                                    </template>
                                </el-table-column>
                            </el-table>

                            <div class="pagination-container">
                                <el-pagination
                                    :current-page="individualPagination.currentPage"
                                    :page-size="individualPagination.pageSize"
                                    :total="filteredIndividualReports.length"
                                    layout="total, prev, pager, next"
                                    @current-change="handleIndividualPageChange"
                                />
                            </div>
                        </div>
                    </el-col>
                </el-row>

                <!-- 个体报告详情弹窗 -->
                <el-dialog v-model="personReportVisible" title="个体报告详情" width="90%" :before-close="closePersonReport">
                    <PersonReport :user-id="personUserId" :answer-id="personAnswerId" :scale-id="personScaleId" />
                </el-dialog>
            </div>
        </div>

        <!-- 活动进度内容区域 -->
        <div v-if="activeIndex === '3'" v-loading="loading" class="report-container">
            <div class="title-container">
                <h2 class="report-title">活动进度情况</h2>
            </div>

            <!-- 基本信息 -->
            <div class="progress-section">
                <div class="progress-section-title">
                    <i class="section-icon"></i>
                    <span>基本信息</span>
                </div>

                <!-- 活动进度统计 - 三个卡片横向排列 -->
                <div class="progress-cards">
                    <div class="progress-card-item blue">
                        <div class="card-content">
                            <div class="progress-card-value">{{ reportData.participantsNumber || 4580 }}</div>
                            <div class="progress-card-label">邀请人数</div>
                        </div>
                    </div>
                    <div class="progress-card-item green">
                        <div class="card-content">
                            <div class="progress-card-value">{{ reportData.completerNumber || 4000 }}</div>
                            <div class="progress-card-label">完成人数</div>
                        </div>
                    </div>
                    <div class="progress-card-item light-blue">
                        <div class="card-content">
                            <div class="progress-card-value">{{ reportData.completionRate || 99 }}%</div>
                            <div class="progress-card-label">完成率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活动时间线 -->
            <div class="progress-section">
                <div class="progress-section-title">
                    <i class="section-icon"></i>
                    <span>活动时间线</span>
                </div>

                <div class="timeline-container">
                    <div class="timeline-steps">
                        <!-- 活动创建 -->
                        <div class="timeline-step completed">
                            <div class="timeline-card arrow-right">
                                <div style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%">
                                    <div style="margin-bottom: 15px">
                                        <div class="timeline-icon" style="position: static; margin: 0; background: transparent">
                                            <img
                                                src="@/assets/images/first-icon.png"
                                                alt="活动创建"
                                                style="width: 100%; height: 100%; object-fit: contain"
                                            />
                                        </div>
                                    </div>
                                    <div class="timeline-content" style="padding-left: 0; text-align: center; padding-top: 0">
                                        <div class="timeline-title" style="margin-bottom: 8px">活动创建完成</div>
                                        <div class="timeline-date">创建时间: {{ formatDate(reportData.startTime) || '2023-06-12' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-connector">
                                <div class="connector-line"></div>
                                <div class="connector-dot completed"></div>
                                <div class="connector-text">活动创建</div>
                            </div>
                        </div>

                        <!-- 邀请发送 -->
                        <div class="timeline-step completed">
                            <div class="timeline-card arrow-right">
                                <div style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%">
                                    <div style="margin-bottom: 15px">
                                        <div class="timeline-icon" style="position: static; margin: 0; background: transparent">
                                            <img
                                                src="@/assets/images/second-icon.png"
                                                alt="邀请发送"
                                                style="width: 100%; height: 100%; object-fit: contain"
                                            />
                                        </div>
                                    </div>
                                    <div class="timeline-content" style="padding-left: 0; text-align: center; padding-top: 0">
                                        <div class="timeline-title" style="margin-bottom: 8px">邀请已发送</div>
                                        <div class="timeline-date">共发送 {{ reportData.participantsNumber || 4580 }} 份邀请</div>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-connector">
                                <div class="connector-line"></div>
                                <div class="connector-dot completed"></div>
                                <div class="connector-text">邀请发送</div>
                            </div>
                        </div>

                        <!-- 进行中 -->
                        <div class="timeline-step active">
                            <div class="timeline-card arrow-right">
                                <div style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%">
                                    <div style="margin-bottom: 15px">
                                        <div class="timeline-icon" style="position: static; margin: 0; background: transparent">
                                            <img
                                                src="@/assets/images/third-icon.png"
                                                alt="进行中"
                                                style="width: 100%; height: 100%; object-fit: contain"
                                            />
                                        </div>
                                    </div>
                                    <div class="timeline-content" style="padding-left: 0; text-align: center; padding-top: 0">
                                        <div class="timeline-title" style="margin-bottom: 8px">活动进行中</div>
                                        <div class="timeline-date">当前完成率: {{ reportData.completionRate || 96 }}%</div>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-connector">
                                <div class="connector-line"></div>
                                <div class="connector-dot active"></div>
                                <div class="connector-text">进行中</div>
                            </div>
                        </div>

                        <!-- 已结束 -->
                        <div class="timeline-step" :class="{ completed: isActivityCompleted }">
                            <div class="timeline-card arrow-right">
                                <div style="display: flex; flex-direction: column; align-items: center; width: 100%; height: 100%">
                                    <div style="margin-bottom: 15px">
                                        <div class="timeline-icon" style="position: static; margin: 0; background: transparent">
                                            <img
                                                src="@/assets/images/last-icon.png"
                                                alt="已结束"
                                                style="width: 100%; height: 100%; object-fit: contain"
                                            />
                                        </div>
                                    </div>
                                    <div class="timeline-content" style="padding-left: 0; text-align: center; padding-top: 0">
                                        <div class="timeline-title" style="margin-bottom: 8px">活动已结束</div>
                                        <div class="timeline-date">结束时间: {{ formatDate(reportData.endTime) || '2023-07-12' }}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="timeline-connector">
                                <div class="connector-line"></div>
                                <div class="connector-dot" :class="{ completed: isActivityCompleted }"></div>
                                <div class="connector-text">已结束</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 活动信息内容区域 -->
        <div v-if="activeIndex === '4'" v-loading="loading" class="report-container">
            <div class="section-title">
                <i class="section-icon"></i>
                <span>基本信息</span>
            </div>

            <el-descriptions :column="2" border>
                <el-descriptions-item label="活动名称">{{ reportData.planName }}</el-descriptions-item>
                <el-descriptions-item label="活动ID">{{ reportData.id }}</el-descriptions-item>
                <el-descriptions-item label="开始时间">{{ formatDate(reportData.startTime) }}</el-descriptions-item>
                <el-descriptions-item label="结束时间">{{ formatDate(reportData.endTime) }}</el-descriptions-item>
                <el-descriptions-item label="创建人">{{ activityInfo.creator || '未知' }}</el-descriptions-item>
                <el-descriptions-item label="所属部门">{{ activityInfo.department || '未知' }}</el-descriptions-item>
                <el-descriptions-item label="活动说明" :span="2">
                    {{ activityInfo.description || '暂无说明' }}
                </el-descriptions-item>
            </el-descriptions>

            <div class="section-title">
                <i class="section-icon"></i>
                <span>使用量表</span>
            </div>

            <el-table :data="reportData.scaleInfoList" style="width: 100%" border>
                <el-table-column prop="scaleName" label="量表名称" />
                <el-table-column prop="version" label="版本" />
                <el-table-column prop="questionCount" label="题目数量" />
                <el-table-column prop="estimatedTime" label="预计用时">
                    <template #default="scope"> {{ scope.row.estimatedTime || '10-15' }} 分钟 </template>
                </el-table-column>
            </el-table>
        </div>
    </div>
</template>

<script setup lang="ts" name="Report">
import { reactive, ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import { getScaleAssessPlanRecord, getScaleInstitutionScaleStatus } from '@/api/scale/assessPlan';
import { listScaleAnswer } from '@/api/scale/scaleAnswer';
import { listEarlyWarningLevelConfig } from '@/api/scale/earlyWarningLevelConfig';
import { getWarningLevelStats, getDeptWarningStats, getFactorWarningStats, getGenderWarningStats } from '@/api/statistics/riskWarning';
import { getFactorScoreStatistics } from '@/api/statistics/scaleRecord';
import { ScaleFactorStatisticsVO } from '@/api/statistics/scaleRecord/types';
import { ElMessage } from 'element-plus';
import PersonReport from '@/components/PersonReport/index.vue';
import ImageDisplay from '@/components/ImageDisplay/index.vue';
import * as echarts from 'echarts';
import { dataScope } from '@/api/system/role';

interface CompletionData {
    group: string;
    total: string | number;
    valid: string | number;
    rate: string;
}

interface IndividualFilter {
    keyword: string;
    warningLevel: string;
}

interface IndividualPagination {
    currentPage: number;
    pageSize: number;
}

// 路由参数
const route = useRoute();
const loading = ref<boolean>(false);
const isDark = ref<boolean>(false); // 添加isDark变量解决模板中的类型错误

const personAnswerId = ref<string>('');
const personUserId = ref<string>('');
const personScaleId = ref<string>('');

// 导航菜单激活项
const activeIndex = ref<string>('1');

// 当前选中的量表
const currentScale = ref<any>({});
const currentScaleId = ref<string | null>(null);

// 个体报告中当前选中的量表
const currentIndividualScaleId = ref<string | null>(null);

// 选择量表
const selectScale = (scale: any): void => {
    currentScale.value = scale;
    currentScaleId.value = scale.scaleId;

    // 获取量表完成情况数据
    if (scale.scaleId && reportData.planId) {
        getScaleCompletion(reportData.planId, scale.scaleId);

        // 在选择量表后，重新初始化预警图表
        initWarningChart();

        // 加载部门预警数据
        loadDeptWarningStats();

        // 加载因子预警数据
        getFactorData();

        // 加载水平分析数据
        getHorizontalAnalysisData();
    } else {
        console.warn('无法获取量表完成情况：', {
            scaleId: scale.scaleId,
            reportId: reportData.id
        });
        // 如果ID不完整，清空数据
        completionData.value = [];
    }
};

// 获取量表完成情况
const getScaleCompletion = async (planId: string, scaleId: string): Promise<void> => {
    loading.value = true;
    try {
        console.log('调用量表完成情况接口，参数：', { planId, scaleId });
        // 根据API需要传递3个参数，添加第三个参数（根据实际情况可能需要调整）
        const res = await getScaleInstitutionScaleStatus(planId, scaleId, '');
        console.log('量表完成情况接口返回数据：', res);

        if (res.data && res.data.length > 0) {
            // 将API返回的数据转换为表格所需的格式，确保rate是字符串类型
            completionData.value = res.data.map((item) => ({
                group: item.deptName,
                total: item.eventAttendance,
                valid: item.validParticipants,
                rate: String(item.completionRate) // 转换为字符串
            }));
            console.log('处理后的完成情况数据：', completionData.value);
        } else {
            console.log('量表完成情况接口返回空数据');
            completionData.value = [];
        }
    } catch (error: any) {
        console.error('获取量表完成情况失败', error);
        ElMessage.error('获取量表完成情况失败: ' + (error.message || '未知错误'));
        completionData.value = [];
    } finally {
        loading.value = false;
    }
};

// 导航菜单选择处理
const handleSelect = (key: string): void => {
    console.log(`选中了菜单项: ${key}`);
    activeIndex.value = key;

    // 如果是第一次切换到某个标签页，可以在这里加载相应的数据
    if (key === '2' && !hasLoadedIndividualData.value) {
        // 如果有量表数据，默认选中第一个
        if (reportData.scaleInfoList && reportData.scaleInfoList.length > 0) {
            selectScaleForIndividual(reportData.scaleInfoList[0]);
        } else {
            loadIndividualData();
        }
    } else if (key === '4' && !hasLoadedActivityInfo.value) {
        loadActivityInfo();
    }
};

// 报告数据
const reportData = reactive<any>({
    id: '',
    planName: '',
    startTime: '',
    endTime: '',
    scaleNames: '',
    completionRate: '',
    completerNumber: '',
    participantsNumber: '',
    planId: '',
    scaleInfoList: []
});

// 完成情况数据
const completionData = ref<CompletionData[]>([]);

// 表格行类名处理函数
const tableRowClassName = ({ row, rowIndex }: { row: any; rowIndex: number }): string => {
    if (rowIndex === 1) {
        return 'warning-row';
    }
    return '';
};

// 格式化日期函数
const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
};

// 判断活动是否已结束
const isActivityCompleted = computed((): boolean => {
    if (!reportData.endTime) return false;
    return new Date(reportData.endTime) < new Date();
});

// 个体报告相关
const individualReportData = ref<any[]>([]);
const hasLoadedIndividualData = ref<boolean>(false);
const individualFilter = reactive<IndividualFilter>({
    keyword: '',
    warningLevel: ''
});
const individualPagination = reactive<IndividualPagination>({
    currentPage: 1,
    pageSize: 10
});

// 个体报告弹窗控制
const personReportVisible = ref<boolean>(false);
const currentPersonReport = ref<any | null>(null);

// 关闭个体报告弹窗
const closePersonReport = (): void => {
    personReportVisible.value = false;
    currentPersonReport.value = null;
};

// 从scoreDetail中提取得分
const getScoreFromDetail = (scoreDetail: any): number => {
    if (!scoreDetail) return 0;

    try {
        const scoreData = typeof scoreDetail === 'string' ? JSON.parse(scoreDetail) : scoreDetail;
        if (Array.isArray(scoreData) && scoreData.length > 0) {
            // 找到总分项
            const totalScore = scoreData.find((item) => item.factor === '总分');
            if (totalScore) {
                return totalScore.score;
            }
            // 如果没有总分项，返回第一项的分数
            return scoreData[0].score;
        }
    } catch (error) {
        console.error('解析得分数据失败:', error);
    }
    return 0;
};

// 根据得分获取预警等级
const getWarningLevelFromScore = (score: number): string => {
    // 这里根据实际业务逻辑调整分数区间
    if (score >= 60) return '高危';
    if (score >= 55) return '警戒';
    if (score >= 50) return '追踪';
    if (score >= 45) return '关注';
    return '正常';
};

// 根据得分获取预警等级标签类型
const getWarningTagTypeFromScore = (score: number): 'success' | 'warning' | 'info' | 'danger' | 'primary' => {
    if (score >= 60) return 'danger';
    if (score >= 55) return 'primary';
    if (score >= 50) return 'warning';
    if (score >= 45) return 'info';
    return 'success';
};

// 筛选后的个体报告数据
const filteredIndividualReports = computed((): any[] => {
    let result = [...individualReportData.value];

    if (individualFilter.keyword) {
        const keyword = individualFilter.keyword.toLowerCase();
        result = result.filter(
            (item) => (item.nickName && item.nickName.toLowerCase().includes(keyword)) || (item.userId && item.userId.toString().includes(keyword))
        );
    }

    if (individualFilter.warningLevel) {
        result = result.filter((item) => {
            const score = getScoreFromDetail(item.scoreDetail);
            const level = getWarningLevelFromScore(score);
            return level === individualFilter.warningLevel;
        });
    }

    return result;
});

// 为个体报告选择量表
const selectScaleForIndividual = (scale: any): void => {
    console.log('选择个体报告量表:', scale);
    currentIndividualScaleId.value = scale.scaleId;

    // 加载该量表的个体报告数据
    loadIndividualData(scale.scaleId);
};

// 加载个体报告数据
const loadIndividualData = async (scaleId: string | null = null): Promise<void> => {
    loading.value = true;
    try {
        // 从reportData中获取planId
        const planId = reportData.planId;
        if (!planId) {
            ElMessage.error('缺少必要的计划ID参数');
            return;
        }

        // 使用传入的scaleId或当前选中的量表ID
        const useScaleId =
            scaleId ||
            currentIndividualScaleId.value ||
            (reportData.scaleInfoList && reportData.scaleInfoList.length > 0 ? reportData.scaleInfoList[0].scaleId : null);

        if (!useScaleId) {
            ElMessage.warning('未找到有效的量表ID');
            return;
        }

        console.log('获取个体报告数据，参数：', { planId, scaleId: useScaleId });
        // 构建查询参数
        const queryParams = {
            planId,
            scaleId: useScaleId,
            pageNum: 1,
            pageSize: 10,
            orderByColumn: '',
            isAsc: 'asc'
        };
        const res = await listScaleAnswer(queryParams);

        console.log('个体报告数据返回结果：', res);

        if (res.rows && res.rows.length > 0) {
            // 直接使用API返回的数据
            individualReportData.value = res.rows;
        } else {
            console.log('个体报告数据为空');
            individualReportData.value = [];
        }

        hasLoadedIndividualData.value = true;
    } catch (error: any) {
        console.error('获取个体报告数据失败', error);
        ElMessage.error('获取个体报告数据失败: ' + (error.message || '未知错误'));
        individualReportData.value = [];
    } finally {
        loading.value = false;
    }
};

// 搜索个体报告
const searchIndividual = (): void => {
    individualPagination.currentPage = 1;
    // 过滤逻辑由computed自动处理
};

// 重置个体报告筛选条件
const resetIndividualFilter = (): void => {
    individualFilter.keyword = '';
    individualFilter.warningLevel = '';
    individualPagination.currentPage = 1;
};

// 个体报告分页处理
const handleIndividualPageChange = (page: number): void => {
    individualPagination.currentPage = page;
};

// 根据预警等级获取标签类型
const getWarningTagType = (level: string): 'success' | 'info' | 'warning' | 'danger' | '' => {
    const map: Record<string, 'success' | 'info' | 'warning' | 'danger' | ''> = {
        '正常': 'success',
        '关注': 'info',
        '追踪': 'warning',
        '高危': 'danger',
        '警戒': ''
    };
    return map[level] || '';
};

// 查看个体报告
const viewIndividualReport = (row: any): void => {
    personAnswerId.value = row.answerId;
    personUserId.value = row.userId;
    personScaleId.value = row.scaleId;
    personReportVisible.value = true;
};

// 导出个体报告
const exportIndividualReport = (row: any): void => {
    ElMessage.success(`导出${row.nickName}的个体报告成功`);
    // 实际项目中应调用导出API
};

// 活动信息相关
const hasLoadedActivityInfo = ref<boolean>(false);
const activityInfo = reactive<any>({
    creator: '系统管理员',
    department: '心理健康中心',
    description: '本次测评活动旨在了解学生心理健康状况，及时发现并干预可能存在的心理问题。'
});

// 加载活动信息
const loadActivityInfo = (): void => {
    loading.value = true;

    // 模拟数据加载，实际项目中应从API获取
    setTimeout(() => {
        // 已在reactive对象中设置了默认值
        hasLoadedActivityInfo.value = true;
        loading.value = false;
    }, 300);
};

// 获取报告数据
const getReportData = async (): Promise<void> => {
    // 从路由参数中获取ID
    const id = route.params.planId as string;
    const publishTenantId = route.query.tenantId;
    console.log('路由参数ID:', id);
    console.log('发布机构id:', publishTenantId);
    if (!id) {
        ElMessage.error('缺少必要的ID参数');
        return;
    }

    loading.value = true;
    try {
        console.log('开始获取报告数据，ID:', id);
        const res = await getScaleAssessPlanRecord(id);
        console.log('获取到的报告数据:', res);

        if (res.data) {
            Object.assign(reportData, res.data);
            console.log('报告数据已更新:', reportData);

            // 如果有量表数据，默认选中第一个
            if (reportData.scaleInfoList && reportData.scaleInfoList.length > 0) {
                console.log('选择第一个量表:', reportData.scaleInfoList[0]);
                selectScale(reportData.scaleInfoList[0]);
            } else {
                console.warn('报告中没有量表数据');
                // 即使没有量表数据，也尝试加载部门预警数据
                if (reportData.planId) {
                    loadDeptWarningStats();
                }
            }
        } else {
            console.warn('API返回的数据为空');
        }
    } catch (error: any) {
        console.error('获取报告数据失败', error);
        ElMessage.error('获取报告数据失败: ' + (error.message || '未知错误'));
    } finally {
        loading.value = false;
    }
};

// 导出PDF方法
const exportPdf = (): void => {
    console.log('导出PDF');
    ElMessage.success('PDF导出成功');
    // 实际项目中应调用PDF导出API
};

// 导出Word方法
const exportWord = (): void => {
    console.log('导出word');
    ElMessage.success('Word导出成功');
};

// 预警等级配置
const warningLevelConfig = ref<any[]>([]);

// 预警数据
const warningData = reactive({
    normal: 45,
    normalRate: '45%',
    attention: 25,
    attentionRate: '25%',
    tracking: 15,
    trackingRate: '15%',
    danger: 10,
    dangerRate: '10%',
    warning: 5,
    warningRate: '5%'
});

// 年级/班级预警率表格数据
const gradeWarningData = ref<any[]>([]);

// 加载部门预警数据
const loadDeptWarningStats = async () => {
    if (!reportData.planId || !currentScaleId.value) {
        console.warn('缺少必要的计划ID或量表ID，无法获取部门预警统计数据');
        return;
    }

    try {
        loading.value = true;
        console.log('开始获取部门预警统计数据，参数:', {
            planId: reportData.planId,
            scaleId: currentScaleId.value
        });

        const res = await getDeptWarningStats(reportData.planId, currentScaleId.value);
        console.log('获取到的部门预警统计数据:', res);

        if (res.data && res.data.length > 0) {
            // 直接使用API返回的数据
            gradeWarningData.value = res.data;
        } else {
            console.warn('获取部门预警统计数据为空');
            // 使用默认数据
            gradeWarningData.value = [
                {
                    deptName: '一年级',
                    warningRate: '6.32%',
                    warningLevels: {
                        '1': 422,
                        '2': 422,
                        '3': 422,
                        '4': 422,
                        '5': 422
                    }
                },
                {
                    deptName: '二年级',
                    warningRate: '4.7%',
                    warningLevels: {
                        '1': 437,
                        '2': 437,
                        '3': 437,
                        '4': 437,
                        '5': 437
                    }
                },
                {
                    deptName: '三年级',
                    warningRate: '6.17%',
                    warningLevels: {
                        '1': 491,
                        '2': 491,
                        '3': 491,
                        '4': 491,
                        '5': 491
                    }
                },
                {
                    deptName: '四年级',
                    warningRate: '5.71%',
                    warningLevels: {
                        '1': 444,
                        '2': 444,
                        '3': 444,
                        '4': 444,
                        '5': 444
                    }
                },
                {
                    deptName: '五年级',
                    warningRate: '40.0%',
                    warningLevels: {
                        '1': 1,
                        '2': 1,
                        '3': 1,
                        '4': 1,
                        '5': 1
                    }
                }
            ];
        }
    } catch (error) {
        console.error('获取部门预警统计数据失败:', error);
        // 出错时使用默认数据
        gradeWarningData.value = [
            {
                deptName: '一年级',
                warningRate: '6.32%',
                warningLevels: {
                    '1': 422,
                    '2': 422,
                    '3': 422,
                    '4': 422,
                    '5': 422
                }
            },
            {
                deptName: '二年级',
                warningRate: '4.7%',
                warningLevels: {
                    '1': 437,
                    '2': 437,
                    '3': 437,
                    '4': 437,
                    '5': 437
                }
            }
        ];
    } finally {
        loading.value = false;
    }
};

// 获取预警等级配置
const getWarningLevelConfig = async () => {
    try {
        console.log('开始获取预警等级配置');
        const res = await listEarlyWarningLevelConfig();
        console.log('预警等级配置接口返回:', res);

        if (res && res.code === 200 && res.rows && res.rows.length > 0) {
            // 直接使用接口返回的数据，不需要转换
            warningLevelConfig.value = res.rows;
            console.log('获取到预警等级配置:', warningLevelConfig.value);
        } else {
            console.warn('获取预警等级配置失败或为空:', res);
            warningLevelConfig.value = [];
        }
    } catch (error) {
        console.error('获取预警等级配置出错:', error);
        warningLevelConfig.value = [];
    } finally {
        // 无论成功或失败，都初始化图表
        // 调用API获取预警数据并初始化图表
        initWarningChart();
    }
};

const warningChartRef = ref(null);
let warningChart = null;
const factorChartRef = ref(null);
let factorChart = null;
const horizontalChartRef = ref(null);
let horizontalChart = null;

// 初始化南丁格尔玫瑰图
const initWarningChart = async () => {
    // 确保DOM元素已经渲染
    const chartContainer = document.getElementById('warningChart');
    if (!chartContainer) {
        console.warn('图表容器DOM元素未找到，将在300ms后重试');
        setTimeout(() => {
            initWarningChart();
        }, 300);
        return;
    }

    // 如果已经有图表实例，先销毁
    if (warningChart) {
        warningChart.dispose();
        warningChart = null;
    }

    console.log('初始化图表，容器元素:', chartContainer);
    try {
        warningChart = echarts.init(chartContainer);

        // 准备图表数据
        let chartData = [];
        let legendData = [];

        // 检查是否有必要的ID
        if (!reportData.planId || !currentScaleId.value) {
            console.warn('缺少必要的计划ID或量表ID，无法获取预警统计数据');
            return;
        }

        try {
            // 调用API获取预警等级统计数据
            const res = await getWarningLevelStats(reportData.planId, currentScaleId.value);
            console.log('获取到的预警等级统计数据:', res);

            if (res.data && res.data.length > 0) {
                // 使用API返回的数据
                chartData = res.data;
                legendData = res.data.map((item) => item.name);

                // 使用API返回的数据更新预警数据
                warningData.normal = 0;
                warningData.attention = 0;
                warningData.tracking = 0;
                warningData.danger = 0;
                warningData.warning = 0;

                // 根据返回的数据更新预警数据
                res.data.forEach((item) => {
                    if (item.name === '正常') {
                        warningData.normal = item.value || 0;
                        warningData.normalRate = `${((item.value / reportData.completerNumber) * 100).toFixed(2)}%`;
                    } else if (item.name === '关注') {
                        warningData.attention = item.value || 0;
                        warningData.attentionRate = `${((item.value / reportData.completerNumber) * 100).toFixed(2)}%`;
                    } else if (item.name === '追踪') {
                        warningData.tracking = item.value || 0;
                        warningData.trackingRate = `${((item.value / reportData.completerNumber) * 100).toFixed(2)}%`;
                    } else if (item.name === '高危') {
                        warningData.danger = item.value || 0;
                        warningData.dangerRate = `${((item.value / reportData.completerNumber) * 100).toFixed(2)}%`;
                    } else if (item.name === '警戒') {
                        warningData.warning = item.value || 0;
                        warningData.warningRate = `${((item.value / reportData.completerNumber) * 100).toFixed(2)}%`;
                    }
                });
            } else {
                console.warn('获取预警等级统计数据为空，将使用默认数据');
                // 使用默认数据
                chartData = [
                    { value: warningData.normal || 0, name: '正常', itemStyle: { color: '#42A246' } },
                    { value: warningData.attention || 0, name: '关注', itemStyle: { color: '#1E90FF' } },
                    { value: warningData.tracking || 0, name: '追踪', itemStyle: { color: '#FFFF00' } },
                    { value: warningData.danger || 0, name: '高危', itemStyle: { color: '#FF8000' } },
                    { value: warningData.warning || 0, name: '警戒', itemStyle: { color: '#FF0000' } }
                ];
                legendData = ['正常', '关注', '追踪', '高危', '警戒'];
            }
        } catch (error) {
            console.error('获取预警等级统计数据失败:', error);
            // 出错时使用默认数据
            chartData = [
                { value: warningData.normal || 0, name: '正常', itemStyle: { color: '#42A246' } },
                { value: warningData.attention || 0, name: '关注', itemStyle: { color: '#1E90FF' } },
                { value: warningData.tracking || 0, name: '追踪', itemStyle: { color: '#FFFF00' } },
                { value: warningData.danger || 0, name: '高危', itemStyle: { color: '#FF8000' } },
                { value: warningData.warning || 0, name: '警戒', itemStyle: { color: '#FF0000' } }
            ];
            legendData = ['正常', '关注', '追踪', '高危', '警戒'];
        }

        const option = {
            backgroundColor: '#fff',
            title: {
                // text: '预警分布',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                },
                top: 10
            },
            tooltip: {
                trigger: 'item',
                formatter: '{a} <br/>{b} : {c}人 ({d}%)'
            },
            legend: {
                bottom: 10,
                left: 'center',
                itemWidth: 10,
                itemHeight: 10,
                textStyle: {
                    fontSize: 12
                },
                data: legendData
            },
            series: [
                {
                    name: '预警状态',
                    type: 'pie',
                    radius: ['10%', '75%'],
                    center: ['50%', '50%'],
                    roseType: 'area',
                    avoidLabelOverlap: true,
                    // startAngle: 270, // 从顶部开始
                    clockwise: false, // 逆时针旋转
                    sort: 'none', // 不排序，保持数据原始顺序
                    // itemStyle: {
                    //     borderRadius: 5,
                    //     borderColor: '#fff',
                    //     borderWidth: 2
                    // },
                    label: {
                        show: false
                    },
                    emphasis: {
                        label: {
                            show: true,
                            fontSize: 14,
                            fontWeight: 'bold',
                            formatter: '{b}: {c}人 ({d}%)'
                        },
                        itemStyle: {
                            shadowBlur: 10,
                            shadowOffsetX: 0,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    },
                    data: chartData
                }
            ]
        };

        console.log('设置图表配置:', option);
        warningChart.setOption(option);

        // 手动触发一次resize确保图表正确渲染
        setTimeout(() => {
            if (warningChart) {
                warningChart.resize();
            }
        }, 200);
    } catch (error) {
        console.error('初始化图表失败:', error);
    }
};

// 初始化因子预警折线图
const initFactorChart = () => {
    // 确保DOM元素已经渲染
    const chartContainer = document.getElementById('factorChart');
    if (!chartContainer) {
        console.warn('因子图表容器DOM元素未找到，将在300ms后重试');
        setTimeout(() => {
            initFactorChart();
        }, 300);
        return;
    }

    // 如果已经有图表实例，先销毁
    if (factorChart) {
        factorChart.dispose();
        factorChart = null;
    }

    console.log('初始化因子预警图表');
    try {
        factorChart = echarts.init(chartContainer);

        const option = {
            backgroundColor: '#fff',
            title: {
                text: '各因子预警人数统计',
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                },
                top: 10
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: '{b}: {c}人'
            },
            grid: {
                left: '8%',
                right: '5%',
                bottom: '15%',
                top: '60px',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: factorChartData.value.categories,
                axisLabel: {
                    interval: 0,
                    rotate: 45,
                    fontSize: 11,
                    margin: 15
                },
                axisLine: {
                    lineStyle: {
                        color: '#999'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '人数',
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#999'
                    }
                },
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        color: '#ddd'
                    }
                }
            },
            series: [
                {
                    data: factorChartData.value.series,
                    type: 'line',
                    smooth: false,
                    areaStyle: {
                        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                            { offset: 0, color: 'rgba(87, 139, 249, 0.5)' },
                            { offset: 1, color: 'rgba(87, 139, 249, 0.1)' }
                        ])
                    }
                }
            ]
        };

        factorChart.setOption(option);

        // 手动触发一次resize确保图表正确渲染
        setTimeout(() => {
            if (factorChart) {
                factorChart.resize();
            }
        }, 200);
    } catch (error) {
        console.error('初始化因子预警图表失败:', error);
    }
};

// 初始化水平分析柱状图
const initHorizontalChart = () => {
    // 确保DOM元素已经渲染
    const chartContainer = document.getElementById('horizontalChart');
    if (!chartContainer) {
        console.warn('水平分析图表容器DOM元素未找到，将在300ms后重试');
        setTimeout(() => {
            initHorizontalChart();
        }, 300);
        return;
    }

    // 如果已经有图表实例，先销毁
    if (horizontalChart) {
        horizontalChart.dispose();
        horizontalChart = null;
    }

    console.log('初始化水平分析图表');
    try {
        horizontalChart = echarts.init(chartContainer);

        // 使用 factorLevelData 作为数据源
        const data = factorLevelData.value;

        const option = {
            backgroundColor: '#fff',
            title: {
                left: 'center',
                textStyle: {
                    fontSize: 16,
                    fontWeight: 'normal'
                },
                top: 10
            },
            tooltip: {
                trigger: 'axis',
                axisPointer: {
                    type: 'shadow'
                },
                formatter: function (params) {
                    const dataIndex = params[0].dataIndex;
                    const dataItem = data[dataIndex];
                    return `${dataItem.name}<br/>最小值: ${dataItem.min}<br/>最大值: ${dataItem.max}<br/>平均分: ${dataItem.avg}<br/>标准差: ${dataItem.std}`;
                }
            },
            grid: {
                left: '3%',
                right: '4%',
                bottom: '15%',
                top: '60px',
                containLabel: true
            },
            xAxis: {
                type: 'category',
                data: data.map((item) => item.name),
                axisLabel: {
                    interval: 0,
                    rotate: 45,
                    fontSize: 12,
                    margin: 15
                },
                axisLine: {
                    lineStyle: {
                        color: '#999'
                    }
                }
            },
            yAxis: {
                type: 'value',
                name: '分值',
                axisLine: {
                    show: true,
                    lineStyle: {
                        color: '#999'
                    }
                },
                splitLine: {
                    lineStyle: {
                        type: 'dashed',
                        color: '#ddd'
                    }
                }
            },
            series: [
                {
                    name: '最大值',
                    type: 'bar',
                    barWidth: 10,
                    itemStyle: {
                        color: '#409EFF',
                        opacity: 0.8
                    },
                    data: data.map((item) => item.max),
                    label: {
                        show: false
                    },
                    z: 10,
                    barGap: '0%'
                },
                {
                    name: '最小值',
                    type: 'bar',
                    barWidth: 10,
                    itemStyle: {
                        color: '#FFA500',
                        opacity: 0.8
                    },
                    data: data.map((item) => item.min),
                    label: {
                        show: false
                    },
                    z: 10,
                    barGap: '0%'
                },
                {
                    name: '平均分',
                    type: 'bar',
                    barWidth: 20,
                    itemStyle: {
                        color: '#67C23A',
                        opacity: 0.8
                    },
                    data: data.map((item) => item.avg),
                    label: {
                        show: true,
                        position: 'top',
                        formatter: '{c}'
                    },
                    z: 10
                }
            ],
            legend: {
                data: ['最大值', '最小值', '平均分'],
                bottom: 0
            }
        };

        horizontalChart.setOption(option);

        // 手动触发一次resize确保图表正确渲染
        setTimeout(() => {
            if (horizontalChart) {
                horizontalChart.resize();
            }
        }, 200);
    } catch (error) {
        console.error('初始化水平分析图表失败:', error);
    }
};

// 窗口大小变化时重绘图表
const handleResize = () => {
    if (warningChart) {
        warningChart.resize();
    }
    if (factorChart) {
        factorChart.resize();
    }
    if (horizontalChart) {
        horizontalChart.resize();
    }
};

// 页面加载时获取数据
onMounted(() => {
    console.log('组件已挂载，开始初始化');

    // 给window添加resize事件监听
    window.addEventListener('resize', handleResize);

    // 先获取报告数据
    getReportData()
        .then(() => {
            // 报告数据获取完成后，获取预警等级配置
            getWarningLevelConfig();
            // 获取因子数据
            getFactorData();
            // 获取水平分析数据
            getHorizontalAnalysisData();
            // 初始化因子预警图表
            initFactorChart();
            // 初始化水平分析柱状图
            initHorizontalChart();
        })
        .catch((error) => {
            console.error('获取报告数据失败:', error);
            // 即使报告数据获取失败，也尝试获取预警等级配置和初始化因子预警图表
            getWarningLevelConfig();
            getFactorData();
            getHorizontalAnalysisData();
            initFactorChart();
            initHorizontalChart();
        });
});

// 在组件销毁前移除事件监听
const beforeUnmount = () => {
    window.removeEventListener('resize', handleResize);
    if (warningChart) {
        warningChart.dispose();
        warningChart = null;
    }
    if (factorChart) {
        factorChart.dispose();
        factorChart = null;
    }
    if (horizontalChart) {
        horizontalChart.dispose();
        horizontalChart = null;
    }
};

// 计算预警率
const getWarningRate = () => {
    const normal = Number(warningData.normal || 0);
    const attention = Number(warningData.attention || 0);
    const tracking = Number(warningData.tracking || 0);
    const danger = Number(warningData.danger || 0);
    const warning = Number(warningData.warning || 0);

    const total = normal + attention + tracking + danger + warning;
    if (total <= 0) return '0';

    return (((attention + tracking) / total) * 100).toFixed(2);
};

// 计算百分比
const getWarningPercent = (key: string) => {
    const normal = Number(warningData.normal || 0);
    const attention = Number(warningData.attention || 0);
    const tracking = Number(warningData.tracking || 0);
    const danger = Number(warningData.danger || 0);
    const warning = Number(warningData.warning || 0);

    const total = normal + attention + tracking + danger + warning;
    if (total <= 0) return '0';

    // @ts-ignore
    const value = Number(warningData[key] || 0);
    return ((value / total) * 100).toFixed(2);
};

// 获取预警数据
const getWarningDataByLevelId = (levelId: number): number => {
    switch (levelId) {
        case 1:
            return warningData.normal || 0;
        case 2:
            return warningData.attention || 0;
        case 3:
            return warningData.tracking || 0;
        case 4:
            return warningData.danger || 0;
        case 5:
            return warningData.warning || 0;
        default:
            return 0;
    }
};

// 初始化图表
const initChart = () => {
    if (!warningChartRef.value) return;

    const chartInstance = echarts.init(warningChartRef.value);

    const option = {
        backgroundColor: '#fff',
        title: {
            text: '预警分布',
            left: 'center',
            top: 10,
            textStyle: {
                color: '#333',
                fontSize: 16,
                fontWeight: 'normal'
            }
        },
        tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: 10,
            data: legendData
        },
        series: [
            {
                name: '预警分布',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '50%'],
                avoidLabelOverlap: false,
                startAngle: 270, // 从顶部开始
                clockwise: true, // 顺时针旋转
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 20,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: chartData
            }
        ]
    };

    chartInstance.setOption(option);
    window.addEventListener('resize', () => {
        chartInstance.resize();
    });
};

// 获取更浅的颜色
const getLighterColor = (color: string): string => {
    try {
        // 如果颜色不是有效的十六进制颜色，返回默认浅色
        if (!color || !color.startsWith('#') || color.length !== 7) {
            return '#F5F5F5';
        }

        const hex = color.replace('#', '');
        // 解析RGB值
        const r = parseInt(hex.substr(0, 2), 16);
        const g = parseInt(hex.substr(2, 2), 16);
        const b = parseInt(hex.substr(4, 2), 16);

        // 混合白色以获得更浅的颜色 (混合比例85%白色)
        const mixRatio = 0.85;
        const newR = Math.min(255, Math.round(r + (255 - r) * mixRatio));
        const newG = Math.min(255, Math.round(g + (255 - g) * mixRatio));
        const newB = Math.min(255, Math.round(b + (255 - b) * mixRatio));

        // 转换回十六进制
        return `#${newR.toString(16).padStart(2, '0')}${newG.toString(16).padStart(2, '0')}${newB.toString(16).padStart(2, '0')}`;
    } catch (error) {
        console.error('颜色转换错误:', error);
        return '#F5F5F5'; // 返回默认浅灰色
    }
};

const factorData = ref({
    // 表头配置
    headers: ['机构/部门', '总症状', '敏化', '强迫症', '人际关系', '抑郁', '焦虑', '敌对'],
    // 因子数据
    rows: []
});

// 性别分析表格数据
const genderAnalysisData = ref([]);

// 因子水平分析表格数据
const factorLevelData = ref([]);

// 为图表准备数据的计算属性
const factorChartData = computed(() => {
    // 如果没有数据，返回空数据
    if (!factorData.value.rows || factorData.value.rows.length === 0) {
        return {
            categories: [],
            series: []
        };
    }

    // 查找总计行（如果有）
    const totalRow = factorData.value.rows.find((row) => row.grade === '总计');

    // 获取因子名称（去掉第一个"机构/部门"）
    // 考虑到"总计"可能作为因子名存在，要确保它不影响图表
    const categories = factorData.value.headers.slice(1).filter((header) => header !== '总计');

    // 如果有总计行，使用总计行的数据
    if (totalRow) {
        return {
            categories: categories,
            series: categories.map((header) => {
                const value = totalRow[header];
                // 如果值的格式是"n/m"，提取n
                if (typeof value === 'string' && value.includes('/')) {
                    return parseInt(value.split('/')[0]) || 0;
                }
                return 0;
            })
        };
    }

    // 如果没有总计行，则使用所有行数据的第一个值之和
    return {
        categories: categories,
        series: categories.map((header) => {
            // 计算所有行该因子的合计值
            let total = 0;
            factorData.value.rows.forEach((row) => {
                const value = row[header];
                if (typeof value === 'string' && value.includes('/')) {
                    total += parseInt(value.split('/')[0]) || 0;
                }
            });
            return total;
        })
    };
});

// 获取因子数据的函数（将来可以连接API）
const getFactorData = async () => {
    try {
        // 检查是否有必要的ID
        if (!reportData.planId || !currentScaleId.value) {
            console.warn('缺少必要的计划ID或量表ID，无法获取因子预警统计数据');
            return;
        }

        loading.value = true;
        console.log('开始获取因子预警统计数据，参数:', {
            planId: reportData.planId,
            scaleId: currentScaleId.value
        });

        // 调用API获取因子预警统计数据
        const res = await getFactorWarningStats(reportData.planId, currentScaleId.value);
        console.log('获取到的因子预警统计数据:', res);

        if (res.data && res.data.factors && res.data.statistics) {
            // 只包含非total因子的表头
            const factorHeaders = ['机构/部门'];

            // 添加因子名称，完全排除total因子
            res.data.factors.forEach((factor) => {
                if (factor.factorKey !== 'total') {
                    factorHeaders.push(factor.factorName);
                }
            });

            factorData.value.headers = factorHeaders;

            // 构建行数据，将总计行单独处理
            const normalRows = [];
            let totalRow = null;

            res.data.statistics.forEach((item) => {
                // 如果是总计行，单独处理
                if (item.deptName === '总计') {
                    totalRow = {
                        grade: item.deptName
                    };

                    // 只添加非total因子的统计值
                    res.data.factors.forEach((factor) => {
                        if (factor.factorKey !== 'total') {
                            totalRow[factor.factorName] = item.factorStats[factor.factorKey] || '0/0';
                        }
                    });
                } else {
                    // 创建普通行对象，设置年级/班级名称
                    const row = {
                        grade: item.deptName
                    };

                    // 只添加非total因子的统计值
                    res.data.factors.forEach((factor) => {
                        if (factor.factorKey !== 'total') {
                            row[factor.factorName] = item.factorStats[factor.factorKey] || '0/0';
                        }
                    });

                    normalRows.push(row);
                }
            });

            // 确保总计行在最后
            const allRows = [...normalRows];
            if (totalRow) {
                allRows.push(totalRow);
            }

            // 更新行数据
            factorData.value.rows = allRows;

            // 初始化因子图表
            initFactorChart();
        } else {
            console.warn('获取因子预警统计数据返回格式不正确:', res);
            // 使用默认数据，不包含总计列
            factorData.value = {
                headers: ['机构/部门', '蓝色预警', '黄色预警'],
                rows: [
                    {
                        grade: '心理学院',
                        '蓝色预警': '2/2',
                        '黄色预警': '0/2'
                    },
                    {
                        grade: '计算机学院',
                        '蓝色预警': '0/3',
                        '黄色预警': '3/3'
                    },
                    {
                        grade: '总计',
                        '蓝色预警': '2/5',
                        '黄色预警': '3/5'
                    }
                ]
            };
        }
    } catch (error) {
        console.error('获取因子预警统计数据失败:', error);
        // 出错时使用默认数据，不包含总计列
        factorData.value = {
            headers: ['机构/部门', '蓝色预警', '黄色预警'],
            rows: [
                {
                    grade: '心理学院',
                    '蓝色预警': '2/2',
                    '黄色预警': '0/2'
                },
                {
                    grade: '计算机学院',
                    '蓝色预警': '0/3',
                    '黄色预警': '3/3'
                },
                {
                    grade: '总计',
                    '蓝色预警': '2/5',
                    '黄色预警': '3/5'
                }
            ]
        };
    } finally {
        loading.value = false;
    }
};

// 获取水平分析数据的函数（将来可以连接API）
const getHorizontalAnalysisData = async () => {
    try {
        // 检查是否有必要的ID
        if (!reportData.planId || !currentScaleId.value) {
            console.warn('缺少必要的计划ID或量表ID，无法获取水平分析数据');
            return;
        }

        loading.value = true;
        console.log('开始获取水平分析数据，参数:', {
            planId: reportData.planId,
            scaleId: currentScaleId.value
        });

        // 获取性别预警统计数据
        try {
            const genderStatsRes = await getGenderWarningStats(reportData.planId, currentScaleId.value);
            console.log('获取到的性别预警统计数据:', genderStatsRes);

            if (genderStatsRes && genderStatsRes.data && genderStatsRes.data.length > 0) {
                // 将API返回的性别预警数据转换为组件所需格式
                genderAnalysisData.value = genderStatsRes.data.map((item) => {
                    // 创建新的数据对象，保留原始的warningLevels数据
                    const genderData = {
                        gender: item.genderDesc, // 使用性别描述而不是代码
                        warningRate: item.warningRate,
                        warningLevels: item.warningLevels // 保留原始的预警等级数据
                    };

                    // 为了兼容旧的模板，也添加各个预警等级的具体数值
                    if (typeof item.warningLevels === 'object' && !Array.isArray(item.warningLevels)) {
                        genderData.normal = item.warningLevels['1'] || 0;
                        genderData.attention = item.warningLevels['2'] || 0;
                        genderData.tracking = item.warningLevels['3'] || 0;
                        genderData.danger = item.warningLevels['4'] || 0;
                        genderData.warning = item.warningLevels['5'] || 0;
                    }

                    return genderData;
                });
            } else {
                console.warn('获取性别预警统计数据为空，将使用默认数据');
            }
        } catch (error) {
            console.error('获取性别预警统计数据失败:', error);
            // 出错时使用默认数据
        }

        // 获取因子分数统计数据
        try {
            const factorStatsRes = await getFactorScoreStatistics(reportData.planId, currentScaleId.value);
            console.log('获取到的因子分数统计数据:', factorStatsRes);

            if (factorStatsRes && factorStatsRes.data && factorStatsRes.data.length > 0) {
                // 将API返回的因子分数统计数据转换为组件所需格式
                factorLevelData.value = factorStatsRes.data.map((item) => ({
                    name: item.factor,
                    min: parseFloat(item.minScore),
                    max: parseFloat(item.maxScore),
                    avg: parseFloat(item.avgScore),
                    std: parseFloat(item.stdDeviation)
                }));

                // 初始化水平分析图表
                initHorizontalChart();
            } else {
                console.warn('获取因子分数统计数据为空，将使用默认数据');
                // 使用默认数据，无需操作，已在 ref 中定义了默认数据
            }
        } catch (error) {
            console.error('获取因子分数统计数据失败:', error);
            // 出错时使用默认数据，无需操作，已在 ref 中定义了默认数据
        }
    } catch (error) {
        console.error('获取水平分析数据失败:', error);
        // 出错时使用默认数据
        // 已在 ref 中定义了默认数据，无需再次设置
    } finally {
        loading.value = false;
    }
};

const getRowValue = (row: any, header: string): string => {
    const value = row[header];
    if (typeof value === 'string') {
        return value;
    } else if (typeof value === 'number') {
        return value.toString();
    } else {
        console.warn(`未知的数据格式: ${header}`, row);
        return '';
    }
};

/**
 * 获取性别预警数据中特定预警等级的数量
 * @param {Object} genderItem 性别预警数据项
 * @param {string|number} warningLevelId 预警等级ID
 * @returns {number} 对应预警等级的数量
 */
const getGenderWarningCount = (genderItem, warningLevelId) => {
    if (!genderItem || !genderItem.warningLevels) {
        return 0;
    }

    // 检查warningLevels是否为数组
    if (Array.isArray(genderItem.warningLevels)) {
        const levelData = genderItem.warningLevels.find((level) => level.warningLevelId === warningLevelId);
        return levelData ? levelData.count : 0;
    }
    // 检查warningLevels是否为对象
    else if (typeof genderItem.warningLevels === 'object') {
        return genderItem.warningLevels[warningLevelId] || 0;
    }

    return 0;
};
</script>

<style scoped>
.report-wrapper {
    width: 100%;
    background-color: #ccdbf1;
    min-height: calc(100vh - 60px);
    padding-bottom: 40px;
}

/* 导航栏样式 */
.report-nav {
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 10;
}

.el-menu-demo {
    border-bottom: none;
    padding: 0 20px;
}

.el-menu-demo .el-menu-item {
    font-size: 15px;
    height: 50px;
    line-height: 50px;
}

.el-menu-demo .el-menu-item.is-active {
    font-weight: 600;
    color: #409eff;
}

/* 报告容器样式 */
.report-container {
    padding: 20px;
    margin: 0 auto;
    max-width: 1550px;
    margin-top: 20px;
    background-color: #fff;
}

/* 报告卡片样式 */
.report-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 20px;
    overflow: hidden;
    max-width: 1550px;
    margin: 0 auto 20px;
    padding: 20px;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
    background-color: #f8f9fa;
}

.card-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    position: relative;
    padding-left: 12px;
}

.card-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    border-radius: 2px;
}

.card-body {
    /* padding: 20px; */
}

/* 标题和导出按钮样式 */
.title-container {
    text-align: center;
    margin: 15px auto 25px;
    position: relative;
    padding-bottom: 15px;
    border-bottom: 4.5px solid #578bf9;
    max-width: 1550px;
}

.report-title {
    font-size: 24px;
    font-weight: normal;
    color: #333;
    margin: 0;
}

.export-buttons {
    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    gap: 10px;
}

.export-btn {
    padding: 8px 16px;
}

/* 基本信息样式 */
.info-card {
    background-color: #e7efff;
    border-radius: 8px;
    padding: 10px;
    box-shadow: none;
    position: relative;
    padding-top: 60px;
    padding-bottom: 20px;
    overflow: hidden;
}

.info-card-content {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    padding-left: 40px;
}

.info-card-icon {
    width: 120px;
    height: 120px;
    margin-right: 20px;
    flex-shrink: 0;
}

.info-card-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.info-card-details {
    flex: 1;
    padding-top: 25px;
}

.info-row {
    display: flex;
    margin-bottom: 20px;
    font-size: 14px;
    color: #333;
    line-height: 1.6;
}

.info-first-row {
    display: flex;
    justify-content: flex-start;
    gap: 60px;
    margin-bottom: 25px;
}

.info-item {
    display: flex;
}

.info-row:last-child {
    margin-bottom: 0;
}

.info-label {
    width: 80px;
    text-align: right;
    margin-right: 12px;
    flex-shrink: 0;
    color: #578bf9;
    font-weight: 550;
}

.info-content {
    flex: 1;
    word-break: break-all;
}

/* 心理测评活动引导标题 */
.section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin: 30px auto 15px;
    position: relative;
    padding-left: 12px;
    max-width: 1550px;
}

.section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    border-radius: 2px;
}

/* 引导内容样式 */
.guide-content {
    color: #333;
    font-size: 14px;
    background-color: transparent;
    padding-bottom: 10px;
}

.guide-paragraph {
    margin: 10px 0;
    line-height: 1.8;
    text-align: justify;
    text-indent: 2em;
}

/* 统计数据展示 */
.stats-box {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
    background-color: #f0f8ff;
    border-radius: 8px;
    padding: 20px;
}

/* 左右布局中的统计卡片样式 */
.el-col .stats-box {
    height: 100%;
    margin: 0;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #f0f8ff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    border-radius: 8px;
}

.guide-text {
    padding-right: 15px;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    box-shadow: -5px 5px 0 0 #578bf9;
    border: #578bf9 solid 1px;
    margin-top: 15px;
    margin-bottom: 15px;
}

.stat-item {
    text-align: center;
    padding: 0 10px;
    flex: 1;
}

.stat-value {
    font-size: 42px;
    font-weight: bold;
    color: #409eff;
    margin-bottom: 10px;
}

.stat-label {
    font-size: 14px;
    color: #606266;
}

/* 心理状态等级样式 */
.level-normal {
    color: #52c41a;
    font-weight: bold;
    padding: 0 3px;
}

.level-attention {
    color: #1890ff;
    font-weight: bold;
    padding: 0 3px;
}

.level-tracking {
    color: #fa8c16;
    font-weight: bold;
    padding: 0 3px;
}

.level-danger {
    color: #f5222d;
    font-weight: bold;
    padding: 0 3px;
}

.level-warning {
    color: #722ed1;
    font-weight: bold;
    padding: 0 3px;
}

/* 量表卡片与详情布局 */
.scale-cards-container {
    margin-top: 50px;
    max-width: 1550px;
    margin: 0 auto;
}

/* 量表卡片样式 - 模仿图片中的格式 */
.scale-cards {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 25px;
}

.scale-card {
    width: calc(33.33% - 14px);
    min-width: 280px;
    border: 1px solid #578bf9;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 5px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.3s;
    cursor: pointer;
    background-color: #fff;
    position: relative;
}

.scale-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.scale-card.active {
    border: 2px solid #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.scale-card-image {
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 10px;
}

.scale-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.scale-card-title {
    padding: 12px 15px;
    text-align: center;
    font-size: 16px;
    color: #333;
    background-color: #fff;
}

/* 量表详情区域样式 */
.scale-detail-section {
    border-radius: 8px;
    padding: 15px;
    margin-top: 70px;
    margin-bottom: 30px;
    transition: all 0.3s ease;
    min-height: 360px;
}

/* 新增：量表图片卡片样式 */
.scale-image-card {
    background-color: transparent;
    border-radius: 12px;
    padding: 0;
    height: 100%;
    box-shadow: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 340px;
}

.scale-image-container {
    width: auto;
    height: auto;
    margin-bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    border-radius: 12px;
    background-color: transparent;
    box-shadow: -5px 5px 0 0 #578bf9;
    border: #578bf9 solid 1px;
}

.scale-image-container img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 12px;
}

.scale-placeholder-image {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f4ff;
    border-radius: 12px;
}

.scale-placeholder-image img {
    width: 60%;
    height: auto;
    opacity: 0.7;
    border-radius: 12px;
}

.scale-name {
    font-size: 18px;
    font-weight: 500;
    color: #409eff;
    text-align: center;
    margin-top: 10px;
}

/* 新增：量表详情卡片样式 */
.scale-info-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: #578bf9 solid 1px;
    min-height: 340px;
}

.detail-header {
    margin-bottom: 20px;
}

.detail-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.detail-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    border-radius: 2px;
}

.scale-detail-content {
    line-height: 1.8;
    color: #555;
    font-size: 15px;
    padding: 0 10px;
}

.detail-paragraph {
    margin-bottom: 15px;
    text-indent: 2em;
}

.scale-description {
    margin-top: 16px;
    background-color: #fff;
    padding: 15px;
    border-radius: 4px;
    border-left: 4px solid #1890ff;
}

/* 安全提示样式 */
.safety-tip {
    margin-top: 24px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 4px;
    padding: 15px;
}

.tip-title {
    font-weight: 500;
    color: #d48806;
    margin-bottom: 8px;
}

.safety-tip p {
    font-size: 13px;
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* 完成情况表格样式 */
.completion-table {
    margin-top: 15px;
}

/* 响应式设计 */
@media screen and (max-width: 1200px) {
    .scale-card {
        width: calc(50% - 10px);
    }

    .report-card {
        padding: 15px;
    }
}

@media screen and (max-width: 768px) {
    .scale-card {
        width: 100%;
    }

    .stats-box {
        flex-direction: column;
        gap: 15px;
    }

    .info-row {
        flex-direction: column;
    }

    .info-label {
        width: 100%;
        text-align: left;
        margin-bottom: 5px;
    }

    .export-buttons {
        position: static;
        justify-content: center;
        margin-top: 15px;
    }

    .title-container {
        text-align: center;
    }
}

/* 进度卡片样式 */
.progress-overview {
    margin-bottom: 30px;
    max-width: 1550px;
    margin: 0 auto 30px;
}

.progress-card {
    display: flex;
    align-items: center;
    padding: 20px;
}

.progress-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 24px;
    color: white;
    margin-right: 15px;
}

.icon-invited {
    background-color: #409eff;
}

.icon-completed {
    background-color: #67c23a;
}

.icon-rate {
    background-color: #e6a23c;
}

.progress-info {
    flex: 1;
}

.progress-title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 5px;
}

.progress-value {
    font-size: 24px;
    font-weight: bold;
    color: #303133;
}

/* 个体报告筛选区域 */
.filter-area {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.rounded-input {
    border-radius: 8px !important;
    overflow: hidden;
}

.rounded-input :deep(.el-input__wrapper),
.rounded-input :deep(.el-select__wrapper) {
    border-radius: 8px !important;
}

.search-btn {
    background-color: #578bf9 !important;
    border-color: #578bf9 !important;
    border-radius: 8px !important;
}

/* 分页容器 */
.pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}

/* 空状态占位 */
.empty-placeholder {
    padding: 40px 0;
    display: flex;
    justify-content: center;
}

/* 时间线样式 */
.timeline-container {
    padding: 20px 0;
    max-width: 1400px;
    margin: 0 auto;
}

.timeline-steps {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;
}

.timeline-step {
    flex: 1;
    position: relative;
    padding-top: 0;
    max-width: 250px;
    margin: 0 10px;
}

.timeline-card {
    background: #e0e9ff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 60px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    height: 140px;
    border: none;
    width: 100%;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
    align-items: center;
}

.timeline-card.arrow-right:after {
    content: none;
}

.timeline-step.completed .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/first-step.png');
    background-size: 100% 100%;
    border: none;
    color: #333;
}

.timeline-step.active .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/third_step.png');
    background-size: 100% 100%;
    border: none;
    color: #333;
}

.timeline-step:last-child .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/last-step.png');
    background-size: 100% 100%;
    border: none;
    color: #999;
}

.timeline-icon {
    position: static;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #97b8ff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    margin: 0 auto 15px;
}

.timeline-step.completed .timeline-icon {
    background: #97b8ff;
}

.timeline-step.active .timeline-icon {
    background: #4cd964;
}

.timeline-step:last-child .timeline-icon {
    background: #cccccc;
}

.timeline-content {
    padding-left: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
    align-items: center;
    text-align: center;
    color: #333;
}

.timeline-step.active .connector-text {
    color: #333;
}

.timeline-step:last-child .connector-text {
    color: #999;
}

@media (max-width: 768px) {
    .timeline-steps {
        flex-direction: column;
        align-items: center;
    }

    .timeline-step {
        margin-bottom: 50px;
        width: 80%;
        max-width: none;
    }

    .timeline-card {
        width: 100%;
    }

    .connector-line {
        display: none;
    }
}

/* 活动详细信息 */
.el-descriptions {
    max-width: 1550px;
    margin: 0 auto 20px;
}

.el-table {
    max-width: 1550px;
    margin: 0 auto;
}

/* 个体列表区域 */
.individual-list {
    max-width: 1550px;
    margin: 0 auto;
}

/* 分页容器 */
.pagination-container {
    margin-top: 15px;
    display: flex;
    justify-content: flex-end;
}

.guide-title-container {
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 1;
}

.guide-title {
    background-color: #578bf9;
    color: white;
    font-size: 17px;
    font-weight: bold;
    padding: 6px 15px;
    border-radius: 4px 0px 4px 0px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.guide-card-body {
    padding: 20px 20px 20px;
    margin-top: 10px;
}

.guide-content {
    color: #333;
    font-size: 14px;
    background-color: transparent;
}

.guide-paragraph {
    margin: 15px 0;
    line-height: 1.8;
    text-align: justify;
    text-indent: 2em;
}

/* 响应式调整 */
@media screen and (max-width: 992px) {
    .el-col .stats-box {
        margin-top: 15px;
    }

    .guide-text {
        padding-right: 0;
    }
}

@media screen and (max-width: 768px) {
    .stats-box {
        padding: 15px 10px;
    }

    .stat-item {
        padding: 0 5px;
    }

    .stat-value {
        font-size: 36px;
    }

    .stat-label {
        font-size: 12px;
    }

    .guide-paragraph {
        text-align: justify;
    }
}

/* 新的统计卡片容器样式 */
.stats-container {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    gap: 20px;
    height: 140px;
}

.stat-card {
    flex: 0 0 auto;
    margin: 0;
    box-shadow: none;
    border-radius: 6px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    border: none;
    width: 145px;
    margin: 0;
}

.stat-value-container {
    background-color: #a8c6ff;
    /* 默认颜色 */
    padding: 15px 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    font-weight: normal;
    color: #4a73c0;
    line-height: 1;
    flex-grow: 1;
    height: 80px;
}

.stat-label-container {
    background-color: #ffffff;
    padding: 8px 5px;
    font-size: 14px;
    font-weight: 700;
    color: #606266;
    text-align: center;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.stats-note {
    padding: 5px 0;
    border-radius: 4px;
    font-size: 12px;
    color: #909399;
    line-height: 1.5;
    margin-top: 10px;
    background-color: transparent;
}

/* 根据图片设置不同卡片的颜色 */
.stats-container .stat-card:nth-child(1) .stat-value-container {
    background-color: #a8c6ff;
    /* 蓝色 */
    color: #4a73c0;
}

.stats-container .stat-card:nth-child(2) .stat-value-container {
    background-color: #a8e3ff;
    /* 浅蓝色 */
    color: #4a9cc0;
}

.stats-container .stat-card:nth-child(3) .stat-value-container {
    background-color: #d8c6ff;
    /* 紫色 */
    color: #7b73c0;
}

/* 添加新的样式 */
.stats-col {
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-top: 20px;
    padding-bottom: 15px;
}

/* 新的量表卡片样式 */
.scale-horizontal-container {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;
    margin-bottom: 25px;
    padding-bottom: 10px;
    gap: 10px;
}

/* 移除滚动条相关样式 */
.scale-horizontal-container::-webkit-scrollbar {
    display: none;
}

.scale-horizontal-container::-webkit-scrollbar-track {
    display: none;
}

.scale-horizontal-container::-webkit-scrollbar-thumb {
    display: none;
}

.scale-horizontal-container::-webkit-scrollbar-thumb:hover {
    display: none;
}

.scale-horizontal-item {
    min-width: 210px;
    width: 210px;
    flex: 0 0 210px;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    margin-right: 10px;
    margin-bottom: 10px;
    box-shadow: none;
    cursor: pointer;
    position: relative;
    padding: 0;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: #999999;
    /* 未选中状态的文字颜色 */
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60px;
    height: 60px;
}

/* 使用图片元素代替背景图，与容器一起变大 */
.scale-horizontal-item .bg-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 0;
}

.scale-horizontal-item .item-text {
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
    padding: 0;
    width: 100%;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    font-size: 17px;
    color: inherit;
    /* 继承父元素的颜色 */
}

/* 确保两种状态相同尺寸 */
.scale-horizontal-item,
.scale-horizontal-item.scale-horizontal-active {
    box-shadow: none;
    width: 210px;
    height: 60px;
}

/* 选中状态的文字颜色 */
.scale-horizontal-item.scale-horizontal-active {
    color: #ffffff;
    /* 选中状态的文字颜色为白色 */
}

/* 移除之前的伪元素背景 */
.scale-horizontal-item::before {
    content: none;
}

/* 最后一个项目不需要右侧margin */
.scale-horizontal-item:last-child {
    margin-right: 0;
}

.scale-horizontal-item-image {
    height: 180px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
}

.scale-horizontal-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.scale-horizontal-item-title {
    padding: 12px 15px;
    text-align: center;
    font-size: 16px;
    color: #333;
    background-color: #fff;
}

/* 新增样式：注意事项卡片和完成情况卡片 */
.additional-cards-row {
    margin-top: 20px;
}

/* 注意事项卡片样式 */
.attention-card {
    background-color: #fffbe6;
    border-radius: 8px;
    padding: 20px;
    padding-top: 25px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #ffe769;
    min-height: 300px;
    position: relative;
}

.attention-header {
    margin-bottom: 20px;
    padding-top: 5px;
}

.warning-icon-top {
    position: absolute;
    top: -20px;
    left: 93%;
    transform: translateX(-50%);
    width: 50px;
    height: 70px;
    z-index: 2;
}

.warning-icon-top:hover {
    transform: translateX(-50%);
}

.attention-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.attention-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #e6a23c;
    border-radius: 2px;
}

.attention-content {
    line-height: 1.8;
    color: #555;
    font-size: 15px;
    padding: 0 10px;
}

.safety-tip {
    background-color: transparent;
    border: none;
    padding: 0;
}

.safety-tip p {
    line-height: 1.6;
    text-indent: 2em;
    margin-bottom: 15px;
}

.attention-list {
    padding-left: 2em;
    margin: 15px 0;
}

.attention-list li {
    margin-bottom: 12px;
    line-height: 1.6;
}

/* 完成情况卡片样式 */
.completion-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e8f7;
    min-height: 300px;
}

.completion-header {
    margin-bottom: 20px;
}

.completion-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.completion-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #409eff;
    border-radius: 2px;
}

.completion-content {
    padding: 0;
}

.completion-chart {
    width: 100%;
}

.el-table--border {
    border-radius: 4px;
    overflow: hidden;
}

.el-table th {
    background-color: #e1e8f7 !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
    background-color: #f5f7fa;
}

.el-table .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

/* 自定义表格样式 */
.custom-table {
    width: 100%;
    overflow: hidden;
    border-radius: 8px;
    margin-bottom: 20px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 4px;
}

.custom-table table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 2px;
    text-align: center;
    background-color: #ffffff;
    border: none;
    border-radius: 6px;
    overflow: hidden;
}

.custom-table th:first-child {
    border-top-left-radius: 6px;
}

.custom-table th:last-child {
    border-top-right-radius: 6px;
}

.custom-table tr:last-child td:first-child {
    border-bottom-left-radius: 6px;
}

.custom-table tr:last-child td:last-child {
    border-bottom-right-radius: 6px;
}

.custom-table th {
    background-color: #98b9ff;
    padding: 12px 0;
    font-weight: 500;
    color: #333333;
    border: none;
}

.custom-table td {
    padding: 12px 0;
    background-color: #dbe7ff;
    color: #333333;
    border: none;
}

.custom-table tr:hover td {
    background-color: #e6f1ff;
}

.custom-table .warning-row td {
    background-color: var(--el-color-warning-light-9, #fcf6ed);
}

.custom-table .first-row td {
    background-color: #98b9ff;
    color: #333333;
}

.custom-table .first-column {
    background-color: #c4d7ff !important;
}

.custom-table .no-data {
    text-align: center;
    color: #333333;
    padding: 30px 0;
    background-color: #dbe7ff;
}

/* 总体预警卡片样式 */
.warning-overview-row {
    margin-top: 25px;
}

.warning-overview-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e8f7;
    min-height: 400px;
    position: relative;
}

.warning-header {
    margin-bottom: 20px;
}

.warning-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.warning-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #f56c6c;
    border-radius: 2px;
}

.warning-content {
    padding: 10px 0;
}

.chart-container {
    height: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 10px;
    background-color: #fff;
    border-radius: 4px;
}

.warning-chart {
    width: 100%;
    height: 100%;
    min-height: 350px;
    min-width: 300px;
}

/* 左侧统计数据样式 */
.warning-stats {
    background-color: #fff;
    border-radius: 4px;
    padding: 20px;

    overflow-y: auto;
}

.warning-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f8f9fb;
    border-radius: 4px;
}

.stat-title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.stat-value {
    display: flex;
    align-items: center;
}

.stat-number {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-right: 10px;
}

.stat-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
}

.warning-stat-divider {
    height: 1px;
    background-color: #ebeef5;
    margin: 15px 0;
}

/* 右侧预警描述卡片样式 */
.warning-description-card {
    background-color: #eef3ff;
    border-radius: 4px;
    padding: 20px;
    height: 380px;
    overflow-y: auto;
}

.warning-desc-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 20px;
    padding-bottom: 10px;
    text-align: center;
}

.warning-desc-content {
    padding: 0 5px;
}

.warning-desc-item {
    margin-bottom: 15px;
}

.warning-desc-header {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.warning-desc-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
    flex-shrink: 0;
}

.warning-desc-name {
    font-size: 16px;
    font-weight: 600;
    margin-right: 10px;
    flex-shrink: 0;
    width: 40px;
    text-align: center;
}

.warning-desc-text {
    font-size: 13px;
    color: #606266;
    line-height: 1.6;
    flex: 1;
}

/* 总体预警标题 */
.warning-stats-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

/* 图例部分 */
.warning-legend {
    display: flex;
    margin-bottom: 15px;
    background-color: #eef8ff;
    border-radius: 4px;
    padding: 8px;
    overflow-x: auto;
}

.legend-row {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    gap: 10px;
}

.legend-item {
    display: flex;
    align-items: center;
    font-size: 12px;
    color: #606266;
    white-space: nowrap;
    margin-right: 5px;
}

.legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
    display: inline-block;
}

/* 测评总人数和预警率 */
.warning-total-info {
    display: flex;
    justify-content: space-between;
    margin: 15px 0;
    background-color: #f8f9fb;
    border-radius: 4px;
    padding: 12px;
}

.total-item {
    display: flex;
    flex-direction: column;
}

.total-label {
    font-size: 13px;
    color: #606266;
    margin-bottom: 5px;
}

.total-value {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
}

.warning-rate {
    color: #f56c6c;
}

/* 统计项目样式 */
.warning-stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 10px;
    background-color: #f8f9fb;
    border-radius: 4px;
}

.stat-title {
    font-size: 14px;
    color: #606266;
    font-weight: 500;
}

.stat-value {
    display: flex;
    align-items: center;
}

.stat-number {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-right: 5px;
}

.stat-percent {
    font-size: 12px;
    color: #909399;
    margin-right: 5px;
}

.stat-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
}

/* 说明文本 */
.warning-description {
    font-size: 12px;
    color: #333333;
    line-height: 1.5;
    margin-top: 15px;
    padding: 10px;
    background-color: #f8f9fb;
    border-radius: 4px;
    border-left: 3px solid #e6a23c;
}

/* 年级/班级预警率表格样式 */
.grade-warning-table {
    background-color: #fff;
    border-radius: 8px;
    padding: 10px;
    margin-top: 0;
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.grade-warning-title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #dcdfe6;
}

.custom-grade-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 3px;
    /* 增加单元格间隙 */
    text-align: center;
    margin-bottom: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 10px;
    /* 增加表格外边框弧度 */
    overflow: hidden;
}

.custom-grade-table th {
    background-color: #7ab8fc;
    color: #333333;
    padding: 10px 5px;
    font-weight: 500;
    border: none;
    border-radius: 0;
    /* 移除表头单元格弧度 */
}

.custom-grade-table td {
    padding: 8px 5px;
    border: none;
    color: #333333;
    border-radius: 0;
    /* 移除数据单元格弧度 */
}

.custom-grade-table tbody tr:nth-child(even) {
    background-color: #f5f7fa;
}

.custom-grade-table tbody tr:hover {
    background-color: #ecf5ff;
}

.custom-grade-table th:first-child,
.custom-grade-table td:first-child {
    background-color: #98b9ff !important;
    font-weight: 500;
}

.table-note {
    text-align: left;
    font-size: 12px;
    color: #333333;
    margin-top: 5px;
    margin-bottom: 15px;
}

/* 左侧部分样式调整 */
.warning-stats {
    background-color: #fff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

/* 图表和小贴士并排显示的样式 */
.chart-tips-row {
    display: flex;
    flex-direction: row;
    gap: 15px;
    margin-bottom: 15px;
}

.chart-container-half {
    flex: 1;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    background-color: #fff;
    border-radius: 4px;
}

.warning-description-card-half {
    flex: 1;
    background-color: #eef3ff;
    border-radius: 4px;
    padding: 15px;
    height: 300px;
    overflow-y: auto;
}

.chart-container {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 15px;
}

.warning-chart {
    width: 100%;
    height: 100%;
    min-height: 280px;
}

.warning-description-card {
    background-color: #eef3ff;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

/* 右侧统计项目样式 */
.warning-stats-items {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.warning-stats-items .warning-stat-item {
    flex: 0 0 calc(50% - 5px);
    margin-bottom: 5px;
}

.factor-warning-row {
    margin-top: 25px;
}

.factor-warning-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e8f7;
    min-height: 400px;
    position: relative;
}

.factor-warning-header {
    margin-bottom: 20px;
}

.factor-warning-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.factor-warning-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #e6a23c;
    border-radius: 2px;
}

.factor-warning-content {
    padding: 10px 0;
}

.factor-table-container {
    max-width: 1550px;
    margin: 0 auto;
    overflow-x: auto;
    table-layout: fixed;
    font-size: 13px;
}

.factor-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 2px;
    text-align: center;
    background-color: #ffffff;
    border: none;
    border-radius: 6px;
    overflow-x: auto;
    table-layout: fixed;
    font-size: 13px;
}

.factor-table th:first-child {
    border-top-left-radius: 6px;
}

.factor-table th:last-child {
    border-top-right-radius: 6px;
}

.factor-table tr:last-child td:first-child {
    border-bottom-left-radius: 6px;
}

.factor-table tr:last-child td:last-child {
    border-bottom-right-radius: 6px;
}

.factor-table th {
    background-color: #98b9ff;
    padding: 8px 2px;
    font-weight: 500;
    color: #333333;
    border: none;
    white-space: nowrap;
    font-size: 13px;
}

.factor-table td {
    padding: 8px 2px;
    background-color: #dbe7ff;
    color: #333333;
    border: none;
    white-space: nowrap;
    font-size: 13px;
}

.factor-table tr:hover td {
    background-color: #e6f1ff;
}

.factor-table .warning-row {
    --el-table-tr-bg-color: var(--el-color-warning-light-9);
}

.factor-table .first-row td {
    background-color: #98b9ff;
    color: #333333;
}

.factor-table .first-column {
    background-color: #c4d7ff !important;
}

.factor-table .no-data {
    text-align: center;
    color: #333333;
    padding: 30px 0;
    background-color: #dbe7ff;
}

.factor-chart-container {
    flex: 1;
    height: 320px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    background-color: #fff;
    border-radius: 4px;
    margin-left: 10px;
}

.factor-chart {
    width: 100%;
    height: 100%;
    min-height: 280px;
}

.factor-table .total-row td {
    background-color: #c4d7ff !important;
    font-weight: 500;
}

/* 水平分析模块 */
.horizontal-analysis-row {
    margin-top: 25px;
}

.horizontal-analysis-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #e1e8f7;
    min-height: 400px;
    position: relative;
}

.horizontal-analysis-header {
    margin-bottom: 20px;
}

.horizontal-analysis-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.horizontal-analysis-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #e6a23c;
    border-radius: 2px;
}

.horizontal-analysis-content {
    padding: 10px 0;
}

.horizontal-chart-container {
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0;
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 15px;
}

.horizontal-chart {
    width: 100%;
    height: 100%;
    min-height: 280px;
}

.gender-analysis-container {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.gender-analysis-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.gender-analysis-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 3px;
    /* 增加单元格间隙 */
    text-align: center;
    margin-bottom: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 10px;
    /* 增加表格外边框弧度 */
    overflow: hidden;
}

.gender-analysis-table th {
    background-color: #7ab8fc;
    color: #333333;
    padding: 10px 5px;
    font-weight: 500;
    border: none;
    border-radius: 0;
    /* 移除表头单元格弧度 */
}

.gender-analysis-table td {
    padding: 8px 5px;
    border: none;
    color: #333333;
    border-radius: 0;
    /* 移除数据单元格弧度 */
}

.gender-analysis-table tbody tr:nth-child(even) {
    background-color: #f5f7fa;
}

.gender-analysis-table tbody tr:hover {
    background-color: #ecf5ff;
}

.gender-analysis-table th:first-child,
.gender-analysis-table td:first-child {
    background-color: #98b9ff !important;
    font-weight: 500;
}

.factor-level-container {
    margin-top: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.factor-level-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
}

.factor-level-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 3px;
    /* 增加单元格间隙 */
    text-align: center;
    margin-bottom: 8px;
    background-color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    padding: 8px;
    border-radius: 10px;
    /* 增加表格外边框弧度 */
    overflow: hidden;
}

.factor-level-table th {
    background-color: #7ab8fc;
    color: #333333;
    padding: 10px 5px;
    font-weight: 500;
    border: none;
    border-radius: 0;
    /* 移除表头单元格弧度 */
}

.factor-level-table td {
    padding: 8px 5px;
    border: none;
    color: #333333;
    border-radius: 0;
    /* 移除数据单元格弧度 */
}

.factor-level-table tbody tr:nth-child(even) {
    background-color: #f5f7fa;
}

.factor-level-table tbody tr:hover {
    background-color: #ecf5ff;
}

.factor-level-table th:first-child,
.factor-level-table td:first-child {
    background-color: #98b9ff !important;
    font-weight: 500;
}

/* 心理健康指导建议模块 */
.health-guidance-row {
    margin-top: 25px;
    margin-bottom: 25px;
}

.health-guidance-card {
    background-color: #ffffff;
    border-radius: 8px;
    padding: 20px 25px;
    height: 100%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    border: 1px solid #d0e6ff;
    min-height: 300px;
    position: relative;
}

.health-guidance-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #d0e6ff;
    padding-bottom: 10px;
}

.health-guidance-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    position: relative;
    padding-left: 12px;
    display: flex;
    align-items: center;
}

.health-guidance-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #409eff;
    border-radius: 2px;
}

.health-guidance-content {
    line-height: 1.8;
    color: #333;
    font-size: 14px;
    padding: 0 10px;
}

.health-guidance-text {
    margin-bottom: 15px;
}

.guidance-intro {
    margin-bottom: 15px;
    text-indent: 2em;
    line-height: 1.8;
    color: #333;
}

.guidance-list {
    padding-left: 1.5em;
    margin: 15px 0;
    counter-reset: item;
}

.guidance-list li {
    margin-bottom: 15px;
    line-height: 1.7;
    color: #333;
    position: relative;
    padding-left: 1.8em;
    list-style-type: none;
}

.guidance-list li:before {
    content: counter(item) '.';
    counter-increment: item;
    position: absolute;
    left: 0;
    color: #409eff;
    font-weight: bold;
}

/* 垂直量表卡片样式 */
.scale-cards-container-vertical {
    margin-top: 0;
    margin-bottom: 20px;
    width: 100%;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    padding: 15px 12px;
}

.scale-cards-vertical {
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    justify-content: flex-start;
    gap: 15px;
}

.scale-list-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    padding-left: 8px;
    line-height: 1.2;
    margin-top: 0;
    border-left: none;
}

.scale-list-desc {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
    padding-left: 8px;
}

.scale-card-vertical {
    width: 100%;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: none;
    transition: all 0.3s;
    cursor: pointer;
    background-color: #fff;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px;
    margin-bottom: 0;
}

.scale-card-vertical:hover {
    transform: translateY(-5px);
    background-color: #f0f7ff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.scale-card-vertical.active {
    border: none;
    box-shadow: none;
    background-color: #f0f7ff;
    padding: 10px;
}

.scale-card-image-vertical {
    width: 100%;
    height: 150px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    padding: 0;
    margin-bottom: 8px;
    flex-shrink: 0;
    background-color: #e1edff;
    border-radius: 8px;
    position: relative;
}

.scale-card-title-vertical {
    padding: 0;
    text-align: center;
    font-size: 14px;
    color: #333;
    border-top: none;
    width: 100%;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    height: 40px;
    margin-top: 5px;
}

.right-content {
    padding-left: 15px;
}

/* 添加到样式部分 */
.scale-list-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
    padding-left: 8px;
    border-left: 4px solid #578bf9;
    line-height: 1.2;
    margin-top: 5px;
}

.scale-list-desc {
    font-size: 12px;
    color: #999;
    margin-bottom: 15px;
    padding-left: 12px;
}

/* 添加到样式部分 */
.active-text {
    color: #578bf9 !important;
    font-weight: 500;
}

/* 添加粉色标签样式 */
.image-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #ff7b9c;
    color: white;
    font-size: 10px;
    padding: 1px 5px;
    border-radius: 10px;
    font-weight: normal;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.15);
    line-height: 1.2;
}

/* 活动进度样式 */
.progress-section {
    margin-bottom: 30px;
    max-width: 1550px;
    margin: 0 auto 30px;
}

.progress-section-title {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
    position: relative;
    padding-left: 12px;
}

.progress-section-title:before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 18px;
    background-color: #1890ff;
    border-radius: 2px;
}

/* 基本信息卡片 */
.progress-cards {
    display: flex;
    justify-content: space-between;
    gap: 36px;
    max-width: 1320px;
    margin: 0 auto;
    padding: 0 20px;
}

.progress-card-item {
    flex: 1;
    border-radius: 8px;
    color: #fff;
    position: relative;
    overflow: hidden;
    height: 160px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.card-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    padding: 35px 30px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
}

.progress-card-item.blue {
    background-color: #7887ee;
    background-image: url('@/assets/images/Invitation-count.png');
}

.progress-card-item.green {
    background-color: #3ecfc0;
    background-image: url('@/assets/images/completers-number.png');
}

.progress-card-item.light-blue {
    background-color: #52a4ff;
    background-image: url('@/assets/images/completion-rate.png');
}

.progress-card-value {
    font-size: 42px;
    font-weight: bold;
    margin-bottom: 6px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.progress-card-label {
    font-size: 16px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 时间线样式 */
.timeline-container {
    padding: 20px 0;
    max-width: 1400px;
    margin: 0 auto;
}

.timeline-steps {
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
    position: relative;
}

.timeline-step {
    flex: 1;
    position: relative;
    padding-top: 0;
    max-width: 250px;
    margin: 0 10px;
}

.timeline-card {
    background: #e0e9ff;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 60px;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
    height: 140px;
    /* 增加卡片高度到140px */
    border: none;
    width: 100%;
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 50%, calc(100% - 20px) 100%, 0 100%);
}

.timeline-card.arrow-right:after {
    content: none;
}

.timeline-step.completed .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/first-step.png');
    background-size: 100% 100%;
    border: none;
    color: #333;
}

.timeline-step.active .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/third_step.png');
    background-size: 100% 100%;
    border: none;
    color: #333;
}

.timeline-step:last-child .timeline-card {
    background: transparent;
    background-image: url('@/assets/images/last-step.png');
    background-size: 100% 100%;
    border: none;
    color: #999;
}

.timeline-icon {
    position: absolute;
    top: 55px;
    /* 调整图标位置，使其在更高的卡片中垂直居中 */
    left: 20px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #97b8ff;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
}

.timeline-step.completed .timeline-icon {
    background: #97b8ff;
}

.timeline-step.active .timeline-icon {
    background: #4cd964;
}

.timeline-step:last-child .timeline-icon {
    background: #cccccc;
}

.timeline-content {
    padding-left: 60px;
    /* 增加左边距，避免与图标重叠 */
    display: flex;
    /* 使用flex布局 */
    flex-direction: column;
    justify-content: center;
    /* 内容垂直居中 */
    height: 100%;
}

.timeline-title {
    font-weight: bold;
    margin-bottom: 15px;
    font-size: 16px;
    /* 增大字体大小 */
}

.timeline-date {
    font-size: 14px;
    /* 增大字体大小 */
    color: #666;
}

.timeline-connector {
    position: absolute;
    bottom: -20px;
    left: 0;
    width: 100%;
    height: 50px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.connector-line {
    position: absolute;
    top: 7px;
    left: -50%;
    right: -50%;
    height: 2px;
    background: #97b8ff;
    z-index: 1;
}

.timeline-step.active .connector-line {
    background: #4cd964;
}

.timeline-step:last-child .connector-line {
    background: #cccccc;
    right: 50%;
}

.timeline-step:first-child .connector-line {
    left: 50%;
}

.connector-dot {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    background: #97b8ff;
    position: relative;
    z-index: 2;
}

.connector-dot.completed {
    background: #97b8ff;
}

.connector-dot.active {
    background: #4cd964;
}

.timeline-step:last-child .connector-dot {
    background: #cccccc;
}

.connector-text {
    position: absolute;
    top: 25px;
    font-size: 14px;
    width: 100%;
    text-align: center;
    color: #333;
}

.timeline-step.active .connector-text {
    color: #333;
}

.timeline-step:last-child .connector-text {
    color: #999;
}

@media (max-width: 768px) {
    .progress-cards {
        flex-direction: column;
    }

    .timeline-steps {
        flex-direction: column;
    }

    .timeline-step {
        margin-bottom: 50px;
    }

    .connector-line {
        width: 2px;
        height: 40px;
        left: 50%;
        top: 100%;
        transform: translateX(-50%);
    }

    .timeline-step:last-child .connector-line,
    .timeline-step:first-child .connector-line {
        display: none;
    }
}
</style>
