-- 向量化功能数据库表结构更新
-- 为 ai_knowledge_chunk 表添加向量相关字段

-- 添加向量维度字段
ALTER TABLE ai_knowledge_chunk ADD COLUMN vector_dimension INT DEFAULT 1536 COMMENT '向量维度';

-- 添加向量数据字段（存储Base64编码的JSON数组）
ALTER TABLE ai_knowledge_chunk ADD COLUMN embedding TEXT COMMENT '向量数据（Base64编码的JSON数组）';

-- 更新现有记录的向量状态为未向量化
UPDATE ai_knowledge_chunk SET vector_status = '0' WHERE vector_status IS NULL;

-- 创建向量相关索引（可选，用于提高查询性能）
CREATE INDEX idx_knowledge_chunk_vector_status ON ai_knowledge_chunk(vector_status);
CREATE INDEX idx_knowledge_chunk_knowledge_id ON ai_knowledge_chunk(knowledge_id);
CREATE INDEX idx_knowledge_chunk_document_id ON ai_knowledge_chunk(document_id);

-- 添加注释
COMMENT ON COLUMN ai_knowledge_chunk.vector_dimension IS '向量维度，默认1536（DashScope text-embedding-v2模型）';
COMMENT ON COLUMN ai_knowledge_chunk.embedding IS '向量数据，JSON格式的浮点数数组，用于本地存储和备份';
COMMENT ON COLUMN ai_knowledge_chunk.vector_status IS '向量状态：0-未向量化，1-向量化中，2-已完成，3-失败'; 