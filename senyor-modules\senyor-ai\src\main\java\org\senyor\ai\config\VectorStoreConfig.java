package org.senyor.ai.config;

import lombok.extern.slf4j.Slf4j;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.ai.service.impl.RedisVectorStoreServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 向量存储配置类
 * 使用Redis作为向量存储服务
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class VectorStoreConfig {

    /**
     * Redis向量存储服务
     */
    @Bean
    public IVectorStoreService vectorStoreService(RedisVectorStoreServiceImpl redisVectorStoreService) {
        log.info("初始化Redis向量存储服务");
        return redisVectorStoreService;
    }
} 