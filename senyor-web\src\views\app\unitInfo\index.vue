<template>
    <div class="unit-info">
        <div class="unit-info__container">
            <div class="unit-info__block">
                <div class="unit-info__header">
                    <div class="unit-info__vertical-line"></div>
                    <div class="unit-info__title">基本信息</div>
                </div>
                <el-divider />
                <div class="unit-info__description">
                    单位名称: <b>{{ obj.companyName }}</b>
                </div>
                <br />
                <div class="unit-info__description">
                    所属行业: <b>{{ industryLabel }}</b>
                </div>
                <br />
                <div class="unit-info__description">
                    合同时间: <b>{{ obj.expireTime || '永久' }}</b>
                </div>
                <br />
                <el-divider />
                <div class="unit-info__description">
                    单位地址: <b>{{ formattedAddress }}</b>
                </div>
                <el-button v-if="hasPermission('app:unitInfo:edit')" type="primary" size="small" style="float: right" @click="handleEdit">
                    修改信息
                </el-button>
                <br />
                <div class="unit-info__description">
                    详细地址: <b>{{ obj.address || '未设置' }}</b>
                </div>
                <br />
                <div class="unit-info__description">
                    单位简介: <b>{{ obj.intro || '未设置' }}</b>
                </div>
                <br />

                <div class="unit-info__qrcode-section">
                    <div class="unit-info__section-header">
                        <div class="unit-info__vertical-line"></div>
                        <div class="unit-info__title">二维码</div>
                    </div>
                    <el-divider />
                    <div class="unit-info__qrcode-container">
                        <div class="unit-info__qrcode-row">
                            <div v-for="(item, index) in quick_response_code" :key="index" class="unit-info__qrcode-item">
                                <div class="unit-info__qrcode-card">
                                    <div class="unit-info__qrcode-title">{{ item.label }}</div>
                                    <el-tooltip :content="`扫描二维码跳转到${item.label}页面`">
                                        <div class="unit-info__qrcode-wrapper">
                                            <div :id="`qrcode-${index}`" class="unit-info__qrcode"></div>
                                            <img v-if="logoUrl" :src="logoUrl" class="unit-info__qrcode-logo" alt="单位logo" />
                                        </div>
                                    </el-tooltip>
                                    <div class="unit-info__copy-link-container">
                                        <el-button type="primary" link size="small" @click="copyUrl(item)">
                                            <el-icon><Document /></el-icon>复制链接
                                        </el-button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑对话框 -->
            <el-dialog v-model="dialog.visible" title="修改单位信息" width="800px">
                <el-form ref="formRef" :model="editForm" :rules="rules" label-width="120px">
                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="机构简称" prop="companyShortName">
                                <el-input v-model="editForm.companyShortName" placeholder="请输入机构简称" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系人" prop="contactUserName">
                                <el-input v-model="editForm.contactUserName" placeholder="请输入联系人" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="联系电话" prop="contactPhone">
                                <el-input v-model="editForm.contactPhone" placeholder="请输入联系电话" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="统一社会信用代码" prop="licenseNumber" label-width="35%">
                                <el-input v-model="editForm.licenseNumber" placeholder="请输入统一社会信用代码" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-form-item label="单位地址" prop="areaIds">
                        <el-cascader
                            v-model="editForm.areaIds"
                            :options="areaList"
                            :props="{ value: 'id', label: 'name', expandTrigger: 'hover' }"
                            clearable
                            style="width: 100%"
                        />
                    </el-form-item>

                    <el-form-item label="详细地址" prop="address">
                        <el-input v-model="editForm.address" placeholder="请输入详细地址" />
                    </el-form-item>

                    <el-form-item label="机构简介" prop="intro">
                        <el-input v-model="editForm.intro" type="textarea" :rows="4" placeholder="请输入机构简介" />
                    </el-form-item>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="机构Logo" prop="logo">
                                <imageUpload
                                    v-model="editForm.logo"
                                    :limit="uploadConfigData.limit"
                                    :file-type="uploadConfigData.fileType"
                                    :file-size="uploadConfigData.fileSize"
                                    :compress-support="uploadConfigData.compressSupport"
                                    :compress-target-size="uploadConfigData.compressTargetSize"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="营业执照" prop="businessLicense">
                                <imageUpload
                                    v-model="editForm.businessLicense"
                                    :limit="uploadConfigData.limit"
                                    :file-type="uploadConfigData.fileType"
                                    :file-size="uploadConfigData.fileSize"
                                    :compress-support="uploadConfigData.compressSupport"
                                    :compress-target-size="uploadConfigData.compressTargetSize"
                                />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row>
                        <el-col :span="12">
                            <el-form-item label="logo宽度" prop="logoWidth">
                                <el-input v-model="editForm.logoWidth" placeholder="请输入logo宽度" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="logo高度" prop="logoHeight">
                                <el-input v-model="editForm.logoHeight" placeholder="请输入logo高度" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
                <template #footer>
                    <div class="dialog-footer">
                        <el-button @click="dialog.visible = false">取消</el-button>
                        <el-button type="primary" :loading="loading" @click="handleSubmit"> 确定 </el-button>
                    </div>
                </template>
            </el-dialog>
        </div>
    </div>
</template>

<script setup name="UserSettings" lang="ts">
import { getTenantByTenantId, updateTenant } from '@/api/system/tenant';
import { areaTree } from '@/api/system/area';
import { ref, computed, onMounted, watch } from 'vue';
import useUserStore from '@/store/modules/user';
import { ElMessage } from 'element-plus';
import QRCode from 'qrcodejs2-fix';
import { useRouter } from 'vue-router';
import request from '@/utils/request';
import type { ComponentInternalInstance } from 'vue';
import { toRefs } from 'vue';
import { Document } from '@element-plus/icons-vue';
import {editUnitInfo, getUnitInfoData} from "@/api/app/unitInfo";

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_industry_type, quick_response_code } = toRefs<any>(proxy?.useDict('sys_industry_type', 'quick_response_code'));

const tenantId = useUserStore().tenantId;
const obj = ref({
    id: '',
    tenantId: '',
    parentTenantId: '',
    ancestors: '',
    contactUserName: '',
    contactPhone: '',
    companyShortName: '',
    companyName: '',
    licenseNumber: '',
    coordinate: null,
    tenantLevel: '',
    dataForLevel: '',
    province: 0,
    city: 0,
    area: 0,
    address: '',
    intro: '',
    logo: '',
    logoWidth: '',
    logoHeight: '',
    businessLicense: null,
    systemBg: '',
    systemName: null,
    domain: '',
    visitorUserName: '',
    userName: null,
    remark: '',
    packageId: '',
    expireTime: '',
    usedSourceCount: null,
    sourceCount: 0,
    accountCount: 0,
    industry: '',
    status: '',
    visitorFlag: ''
});

const areaList = ref([]);
const loading = ref(false);
const logoUrl = ref('');

// 图片上传配置
const uploadConfigData = ref({
    limit: 1,
    fileSize: 5,
    fileType: ['jpg', 'png', 'jpeg'],
    compressSupport: false,
    compressTargetSize: 300
});

// 编辑表单
const editForm = ref({
    companyShortName: '',
    contactUserName: '',
    contactPhone: '',
    licenseNumber: '',
    areaIds: [] as (string | number)[],
    address: '',
    intro: '',
    logo: '',
    businessLicense: '',
    logoWidth: '',
    logoHeight: ''
});

// 对话框状态
const dialog = ref({
    visible: false
});

// 表单验证规则
const rules = ref({
    companyShortName: [{ required: true, message: '请输入机构简称', trigger: 'blur' }],
    contactUserName: [{ required: true, message: '请输入联系人', trigger: 'blur' }],
    contactPhone: [{ required: true, message: '请输入联系电话', trigger: 'blur' }],
    areaIds: [{ required: true, message: '请选择单位地址', trigger: 'blur' }],
    address: [{ required: true, message: '请输入详细地址', trigger: 'blur' }],
    intro: [{ required: true, message: '请输入单位简介', trigger: 'blur' }]
});

const formRef = ref();

// 计算属性：获取行业标签
const industryLabel = computed(() => {
    if (!obj.value.industry) return '';
    const item = sys_industry_type.value.find((item) => item.value === obj.value.industry);
    return item ? item.label : obj.value.industry;
});

// 格式化地址显示
const formattedAddress = computed(() => {
    const provinceName = findAreaName(obj.value.province);
    const cityName = findAreaName(obj.value.city);
    const areaName = findAreaName(obj.value.area);

    const addressParts = [provinceName, cityName, areaName].filter(Boolean);
    return addressParts.join('/') || '未设置';
});

// 根据ID查找地区名称
const findAreaName = (id: string | number) => {
    if (!id || !areaList.value.length) return '';

    const queue = [...areaList.value];
    while (queue.length) {
        const node = queue.shift();
        if (node.id === id) return node.name;
        if (node.children?.length) {
            queue.push(...node.children);
        }
    }
    return '';
};

// 获取地区数据
const getAreaList = async () => {
    const res = await areaTree();
    areaList.value = res.data;
};

// 获取单位信息
const getUnitInfo = async () => {
    const res = await getUnitInfoData(tenantId);
    obj.value = res.data;

    // 设置logo URL
    if (obj.value.logo) {
        // 直接使用logo字段作为ossId
        const ossId = obj.value.logo;
        // 使用API获取图片信息
        request({
            url: '/resource/oss/getById/' + ossId,
            method: 'get'
        })
            .then((res) => {
                if (res.code === 200 && res.data && res.data.url) {
                    logoUrl.value = res.data.url;
                } else {
                    logoUrl.value = '';
                    console.log('获取图片信息失败');
                }
            })
            .catch((err) => {
                console.error('获取图片信息出错:', err);
                logoUrl.value = '';
            });
    } else {
        console.log('没有logo ID');
        logoUrl.value = '';
    }
};

// 点击编辑按钮
const handleEdit = async () => {
    editForm.value = {
        ...obj.value,
        areaIds: [obj.value.province, obj.value.city, obj.value.area].filter(Boolean),
    };
    dialog.value.visible = true;
};

// 提交修改
const handleSubmit = async () => {
    try {
        await formRef.value.validate();
        loading.value = true;

        // 从 editForm 中提取数据，仅包含表单字段
        const [province, city, area] = editForm.value.areaIds.map((id) => Number(id));

        // 构建提交数据，仅包含表单中的字段
        const submitData = {
            id: obj.value.id, // 保留ID用于更新
            tenantId: tenantId,
            companyShortName: editForm.value.companyShortName,
            contactUserName: editForm.value.contactUserName,
            contactPhone: editForm.value.contactPhone,
            licenseNumber: editForm.value.licenseNumber,
            province,
            city,
            area,
            address: editForm.value.address,
            intro: editForm.value.intro,
            logo: editForm.value.logo,
            businessLicense: editForm.value.businessLicense,
            logoWidth: editForm.value.logoWidth,
            logoHeight: editForm.value.logoHeight
        };

        // 调用API
        await editUnitInfo(submitData);

        ElMessage.success('修改成功');
        dialog.value.visible = false;
        await getUnitInfo(); // 刷新数据
    } catch (error) {
        console.error(error);
    } finally {
        loading.value = false;
    }
};

// 生成二维码
const generateQRCode = () => {
    // 确保字典数据存在
    if (!quick_response_code.value || quick_response_code.value.length === 0) {
        console.log('未找到二维码URL配置');
        return;
    }

    // 为每个字典数据生成二维码
    quick_response_code.value.forEach((item, index) => {
        // 获取DOM元素
        const qrcodeElement = document.getElementById(`qrcode-${index}`);
        if (!qrcodeElement) return;

        // 清除已有的二维码
        qrcodeElement.innerHTML = '';

        // 构建URL，如果URL中包含{tenantId}占位符，则替换为实际的tenantId
        let url = item.value;
        if (url.includes('{tenantId}')) {
            url = url.replace('{tenantId}', tenantId);
        } else if (!url.includes('?')) {
            // 如果URL不包含查询参数，添加tenantId
            url = `${url}?tenantId=${tenantId}`;
        } else {
            // 如果URL已包含查询参数，追加tenantId
            url = `${url}&tenantId=${tenantId}`;
        }

        // 创建新的二维码
        new QRCode(qrcodeElement, {
            text: url,
            width: 128,
            height: 128,
            colorDark: '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel.H
        });
    });
};

// 构建URL
const buildUrl = (item: any) => {
    if (!item.value) return '';

    let url = item.value;
    if (url.includes('{tenantId}')) {
        url = url.replace('{tenantId}', tenantId);
    } else if (!url.includes('?')) {
        url = `${url}?tenantId=${tenantId}`;
    } else {
        url = `${url}&tenantId=${tenantId}`;
    }

    return url;
};

// 复制链接
const copyUrl = (item: any) => {
    const url = buildUrl(item);
    if (!url) return;

    navigator.clipboard
        .writeText(url)
        .then(() => {
            ElMessage.success(`${item.label}链接已复制`);
        })
        .catch(() => {
            ElMessage.error('复制失败，请手动复制');
        });
};

// 跳转到URL
const goToUrl = (item: any) => {
    const url = buildUrl(item);
    if (!url) return;

    window.open(url, '_blank');
};

// 权限检查方法
const permissions = useUserStore().permissions;
const hasPermission = (permission: string) => {
    // 如果有所有权限的标记，直接返回true
    if (permissions.includes('*:*:*')) {
        return true;
    }
    // 否则检查是否包含特定权限
    return permissions.includes(permission);
};

const router = useRouter();

const goToTalentApplication = () => {
    router.push({
        path: '/talentApplication',
        query: { tenantId: tenantId }
    });
};

const goToApplicationForm = () => {
    router.push({
        path: '/application'
    });
};

// 监听字典数据变化，重新生成二维码
watch(
    () => quick_response_code.value,
    () => {
        // 等待DOM更新后生成二维码
        setTimeout(() => {
            generateQRCode();
        }, 100);
    },
    { deep: true }
);

onMounted(async () => {
    await getAreaList();
    await getUnitInfo();
    // 等待DOM更新后生成二维码
    setTimeout(() => {
        generateQRCode();
    }, 100);
});
</script>

<style scoped lang="scss">
.unit-info {
    &__container {
        padding: 20px;
        min-height: 100vh;
    }

    &__block {
        margin-bottom: 20px;
    }

    &__header,
    &__section-header {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    &__vertical-line {
        width: 4px;
        height: 20px;
        background-color: #409eff;
        margin-right: 10px;
    }

    &__title {
        font-size: 16px;
        font-weight: bold;
        color: #fff;
        background-color: #409eff;
        padding: 5px 15px;
        min-height: 30px;
        line-height: 30px;
        min-width: 100px;
        text-align: center;
        border-radius: 12px 0 12px 0;
    }

    &__description {
        font-size: 15px;
        color: #909399;
        padding-left: 14px;
    }

    &__qrcode-section {
        margin-top: 30px;
    }

    &__qrcode-container {
        margin-top: 20px;
        text-align: left;
        padding-left: 14px;
    }

    &__qrcode-row {
        display: flex;
        flex-wrap: wrap;
        gap: 40px;
    }

    &__qrcode-item {
        width: 128px;
        margin-bottom: 20px;
    }

    &__qrcode-card {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    &__qrcode-title {
        font-size: 14px;
        color: #303133;
        font-weight: 500;
        text-align: center;
        margin-bottom: 10px;
    }

    &__qrcode {
        width: 128px;
        height: 128px;
        margin: 0;
    }

    &__copy-link-container {
        margin-top: 10px;
        text-align: center;
    }

    &__qrcode-label {
        font-size: 14px;
        color: #409eff;
        font-weight: 500;
        text-align: center;
        margin-top: 5px;
    }

    &__qrcode-wrapper {
        position: relative;
        width: 128px;
        height: 128px;
    }

    &__qrcode-logo {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 32px;
        height: 32px;
        border-radius: 4px;
        background-color: white;
        padding: 3px;
        object-fit: contain;
        z-index: 10;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
        border: 1px solid #f0f0f0;
    }
}

.el-divider {
    margin: 20px 0;
}
</style>
