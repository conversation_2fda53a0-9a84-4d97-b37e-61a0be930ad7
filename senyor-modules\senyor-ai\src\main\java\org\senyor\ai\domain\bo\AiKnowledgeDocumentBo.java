package org.senyor.ai.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.senyor.common.mybatis.core.domain.BaseEntity;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI知识库文档业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI知识库文档业务对象")
public class AiKnowledgeDocumentBo extends BaseEntity {

    /**
     * 文档ID
     */
    @Schema(description = "文档ID")
    private Long documentId;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空")
    @Schema(description = "知识库ID", required = true)
    private Long knowledgeId;

    /**
     * 文档名称
     */
    @NotBlank(message = "文档名称不能为空")
    @Size(max = 200, message = "文档名称长度不能超过200字符")
    @Schema(description = "文档名称", required = true)
    private String documentName;

    /**
     * 文档类型（text:文本, pdf:PDF, docx:Word, txt:纯文本）
     */
    @Schema(description = "文档类型")
    private String documentType;

    /**
     * 文档内容
     */
    @Schema(description = "文档内容")
    private String content;

    /**
     * 文档大小（字节）
     */
    @Schema(description = "文档大小")
    private Long fileSize;

    /**
     * 文件路径
     */
    @Schema(description = "文件路径")
    private String filePath;

    /**
     * 文档状态（0:待处理 1:处理中 2:已完成 3:失败）
     */
    @Schema(description = "文档状态")
    private String status;

    /**
     * 处理进度（0-100）
     */
    @Schema(description = "处理进度")
    private Integer progress;

    /**
     * 向量化状态（0:未向量化 1:向量化中 2:已完成 3:失败）
     */
    @Schema(description = "向量化状态")
    private String vectorStatus;

    /**
     * 分块数量
     */
    @Schema(description = "分块数量")
    private Integer chunkCount;

    /**
     * 错误信息
     */
    @Schema(description = "错误信息")
    private String errorMessage;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private Long userId;

    /**
     * 创建用户名
     */
    @Schema(description = "创建用户名")
    private String userName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 上传的文件
     */
    @Schema(description = "上传的文件")
    private MultipartFile file;
}
