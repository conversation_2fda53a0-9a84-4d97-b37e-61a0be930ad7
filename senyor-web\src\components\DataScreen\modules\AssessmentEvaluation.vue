<template>
  <div class="assessment-evaluation">
    <div class="module-title">心理测评看板</div>

    <div class="charts-container">
      <!-- 三级预警展示：水平堆叠条形图 -->
      <div class="main-chart" ref="stackedBarChart"></div>

      <div class="auxiliary-charts">
        <!-- 辅助视图1：对称条形图 - 男女预警比例 -->
        <div class="aux-chart" ref="genderBarChart"></div>

        <!-- 辅助视图2：半圆仪表盘 - 整体处理进度 -->
        <div class="aux-chart" ref="progressGaugeChart"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import { handleChartsInitialization } from '@/utils/chart-helpers';
import {useWarningLevels} from "@/composables/useWarningLevels";
import {GenderWarningData} from "@/api/statistics/riskWarning/types";
import {WarningStatusStatisticsVo} from "@/api/statistics/warning/types";
import {getWarningStatusStatistics} from "@/api/statistics/warning";
import {ElMessage} from "element-plus";
import {getGenderWarningData} from "@/api/statistics/riskWarning";

// 图表元素引用
const stackedBarChart = ref(null);
const genderBarChart = ref(null);
const progressGaugeChart = ref(null);

// 图表实例
let barChartInstance: echarts.ECharts | null = null;
let genderChartInstance: echarts.ECharts | null = null;
let gaugeChartInstance: echarts.ECharts | null = null;

// 使用预警等级组合函数获取缓存的预警等级数据
const { warningLevels, loading: warningLevelsLoading } = useWarningLevels();

// 性别预警数据
const genderWarningData = ref<GenderWarningData | null>(null);

// 预警状态数据
const warningStatusData = ref<WarningStatusStatisticsVo[]>([]);

// 模拟数据 - 整体处理进度
const overallProgress = ref({
    handled: 0,
    total: 0,
    percentage: 0
});

// 获取预警状态统计数据
const fetchWarningStatusData = async () => {
    try {
        const response = await getWarningStatusStatistics();
        if (response.code === 200 && response.data) {
            warningStatusData.value = response.data;

            // 计算整体处理进度 (除了"未开始"状态外的所有状态都视为已处理)
            let handled = 0;
            let total = 0;

            warningStatusData.value.forEach(item => {
                handled += item.tracking + item.referral + item.closed;
                total += item.total;
            });

            overallProgress.value = {
                handled,
                total,
                percentage: total > 0 ? handled / total : 0
            };

            // 数据加载完成后初始化图表
            initStackedBarChart();
            initProgressGaugeChart();
        } else {
            // 如果API调用失败，使用模拟数据
            useDefaultWarningStatusData();
            ElMessage.warning('获取预警状态数据失败，使用模拟数据');
        }
    } catch (error) {
        console.error('获取预警状态数据失败:', error);
        // 使用默认数据初始化
        useDefaultWarningStatusData();
        ElMessage.warning('获取预警状态数据失败，使用模拟数据');
    }
};

// 使用默认预警状态数据
const useDefaultWarningStatusData = () => {
    warningStatusData.value = [
        {
            warningLevelId: 1,
            level: '关注预警',
            notStarted: 35,
            tracking: 28,
            referral: 20,
            closed: 95,
            total: 178,
            color: '#91D5FF'
        },
        {
            warningLevelId: 2,
            level: '轻度预警',
            notStarted: 30,
            tracking: 25,
            referral: 20,
            closed: 80,
            total: 155,
            color: '#FAAD14'
        },
        {
            warningLevelId: 3,
            level: '中度预警',
            notStarted: 20,
            tracking: 20,
            referral: 15,
            closed: 45,
            total: 100,
            color: '#FA8C16'
        },
        {
            warningLevelId: 4,
            level: '重度预警',
            notStarted: 8,
            tracking: 10,
            referral: 7,
            closed: 27,
            total: 52,
            color: '#FF4D4F'
        },
        {
            warningLevelId: 5,
            level: '紧急预警',
            notStarted: 5,
            tracking: 5,
            referral: 3,
            closed: 12,
            total: 25,
            color: '#CF1322'
        }
    ];

    // 计算整体处理进度
    let handled = 0;
    let total = 0;

    warningStatusData.value.forEach(item => {
        handled += item.tracking + item.referral + item.closed;
        total += item.total;
    });

    overallProgress.value = {
        handled,
        total,
        percentage: total > 0 ? handled / total : 0
    };

    // 初始化图表
    initStackedBarChart();
    initProgressGaugeChart();
};

// 获取性别预警数据
const fetchGenderWarningData = async () => {
    try {
        const response = await getGenderWarningData();
        if (response.code === 200 && response.data) {
            genderWarningData.value = response.data.data;
            console.log('性别预警数据:', genderWarningData.value);

            // 数据加载完成后初始化性别预警图表
            if (warningLevels.value.length > 0) {
                setTimeout(() => {
                    initGenderBarChart();
                }, 50);
            }
        }
    } catch (error) {
        console.error('获取性别预警数据失败:', error);
        // 使用默认数据初始化
        initGenderBarChart();
    }
};

// 初始化水平堆叠条形图
const initStackedBarChart = () => {
    if (!stackedBarChart.value || warningStatusData.value.length === 0) return;

    barChartInstance = echarts.init(stackedBarChart.value);

    const levels = warningStatusData.value.map(item => item.level);

    const option = {
        title: {
            text: '五级预警处理状态',
            textStyle: {
                color: '#fff',
                fontSize: 14
            },
            left: 'center',
            top: 5
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params: any) {
                const level = params[0].name;
                const data = warningStatusData.value.find(item => item.level === level);
                if (!data) return '';

                return `${level}<br/>
                未开始: ${data.notStarted} (${(data.notStarted / data.total * 100).toFixed(1)}%)<br/>
                个案追踪: ${data.tracking} (${(data.tracking / data.total * 100).toFixed(1)}%)<br/>
                转介: ${data.referral} (${(data.referral / data.total * 100).toFixed(1)}%)<br/>
                结案: ${data.closed} (${(data.closed / data.total * 100).toFixed(1)}%)<br/>
                总计: ${data.total}`;
            }
        },
        legend: {
            data: ['未开始', '个案追踪', '转介', '结案'],
            textStyle: {
                color: '#666'
            },
            top: 30,
            itemWidth: 12,
            itemHeight: 12
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '20%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLine: {
                lineStyle: {
                    color: '#ddd'
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: '#eee'
                }
            },
            axisLabel: {
                color: '#666'
            }
        },
        yAxis: {
            type: 'category',
            data: levels,
            axisLine: {
                lineStyle: {
                    color: '#ddd'
                }
            },
            axisTick: {
                show: false
            },
            axisLabel: {
                color: '#666',
                formatter: function (value: string) {
                    const data = warningStatusData.value.find(item => item.level === value);
                    return `${value} (${data?.total || 0})`;
                }
            }
        },
        series: [
            {
                name: '未开始',
                type: 'bar',
                stack: '总量',
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return echarts.color.modifyAlpha(data.color, 0.3);
                    }
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return data.notStarted > 10 ? params.value : '';
                    },
                    color: '#fff'
                },
                data: warningStatusData.value.map(item => item.notStarted)
            },
            {
                name: '个案追踪',
                type: 'bar',
                stack: '总量',
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return echarts.color.modifyAlpha(data.color, 0.5);
                    }
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return data.tracking > 10 ? params.value : '';
                    },
                    color: '#fff'
                },
                data: warningStatusData.value.map(item => item.tracking)
            },
            {
                name: '转介',
                type: 'bar',
                stack: '总量',
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return echarts.color.modifyAlpha(data.color, 0.7);
                    }
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return data.referral > 10 ? params.value : '';
                    },
                    color: '#fff'
                },
                data: warningStatusData.value.map(item => item.referral)
            },
            {
                name: '结案',
                type: 'bar',
                stack: '总量',
                emphasis: {
                    focus: 'series'
                },
                itemStyle: {
                    color: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return data.color;
                    }
                },
                label: {
                    show: true,
                    position: 'inside',
                    formatter: function (params: any) {
                        const data = warningStatusData.value[params.dataIndex];
                        return data.closed > 10 ? params.value : '';
                    },
                    color: '#fff'
                },
                data: warningStatusData.value.map(item => item.closed)
            }
        ]
    };

    barChartInstance.setOption(option);
};

// 初始化对称条形图 - 男女预警比例
const initGenderBarChart = () => {
    if (!genderBarChart.value) return;

    genderChartInstance = echarts.init(genderBarChart.value);

    // 如果没有API数据，使用默认数据
    if (!genderWarningData.value || !warningLevels.value.length) {
        const defaultData = {
            male: {
                attention: 98,
                light: 88,
                medium: 58,
                severe: 31,
                emergency: 15
            },
            female: {
                attention: 80,
                light: 67,
                medium: 42,
                severe: 21,
                emergency: 10
            }
        };

        const maleData = [
            defaultData.male.attention,
            defaultData.male.light,
            defaultData.male.medium,
            defaultData.male.severe,
            defaultData.male.emergency
        ];

        const femaleData = [
            -defaultData.female.attention,
            -defaultData.female.light,
            -defaultData.female.medium,
            -defaultData.female.severe,
            -defaultData.female.emergency
        ];

        const option = {
            title: {
                text: '男女预警比例',
                textStyle: {
                    color: '#fff',
                    fontSize: 14
                },
                left: 'center',
                top: 5
            },
            grid: {
                top: '25%',
                bottom: '10%',
                left: '15%',
                right: '15%'
            },
            xAxis: {
                type: 'value',
                axisLabel: {
                    formatter: function (value: number) {
                        return Math.abs(value);
                    },
                    color: '#666'
                },
                axisLine: {
                    lineStyle: {
                        color: '#ddd'
                    }
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    lineStyle: {
                        color: '#eee'
                    }
                }
            },
            yAxis: {
                type: 'category',
                data: ['关注', '轻度', '中度', '重度', '紧急'],
                axisLine: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                axisLabel: {
                    color: '#666'
                }
            },
            series: [
                {
                    name: '男生',
                    type: 'bar',
                    stack: '总量',
                    label: {
                        show: true,
                        position: 'right',
                        color: '#666',
                        fontSize: 12
                    },
                    itemStyle: {
                        color: '#1890FF'
                    },
                    data: maleData
                },
                {
                    name: '女生',
                    type: 'bar',
                    stack: '总量',
                    label: {
                        show: true,
                        position: 'left',
                        formatter: function (params: any) {
                            return -params.value;
                        },
                        color: '#666',
                        fontSize: 12
                    },
                    itemStyle: {
                        color: '#722ED1'
                    },
                    data: femaleData
                }
            ]
        };

        genderChartInstance.setOption(option);
        return;
    }

    // 使用API返回的数据和预警等级信息
    // 按warningLevelId排序，包含所有等级数据
    const filteredLevels = warningLevels.value
        .sort((a, b) => a.warningLevelId - b.warningLevelId);

    // 准备数据
    const maleData: number[] = [];
    const femaleData: number[] = [];
    const levelNames: string[] = [];
    const levelColors: string[] = [];

    filteredLevels.forEach(level => {
        const levelId = level.warningLevelId.toString();
        const maleLevelCount = genderWarningData.value?.male[levelId] || 0;
        const femaleLevelCount = genderWarningData.value?.female[levelId] || 0;

        maleData.push(maleLevelCount);
        femaleData.push(-femaleLevelCount); // 女性数据为负值，用于对称显示
        levelNames.push(level.warningName);
        levelColors.push(level.warningColor);
    });

    const option = {
        title: {
            text: '男女预警比例',
            textStyle: {
                color: '#fff',
                fontSize: 14
            },
            left: 'center',
            top: 5
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            },
            formatter: function (params: any) {
                // 获取索引
                const index = params[0].dataIndex;
                const levelName = levelNames[index];
                const maleValue = Math.abs(params[0].value);
                const femaleValue = Math.abs(params[1].value);

                return `${levelName}<br/>
                男生: ${maleValue} 人<br/>
                女生: ${femaleValue} 人`;
            }
        },
        legend: {
            data: ['男生', '女生'],
            textStyle: {
                color: '#666'
            },
            top: 30,
            itemWidth: 12,
            itemHeight: 12
        },
        grid: {
            top: '25%',
            bottom: '10%',
            left: '15%',
            right: '15%'
        },
        xAxis: {
            type: 'value',
            axisLabel: {
                formatter: function (value: number) {
                    return Math.abs(value);
                },
                color: '#666'
            },
            axisLine: {
                lineStyle: {
                    color: '#ddd'
                }
            },
            axisTick: {
                show: false
            },
            splitLine: {
                lineStyle: {
                    color: '#eee'
                }
            },
            // 设置最小值和最大值，确保即使数据很小也有足够的显示空间
            min: function (value: any) {
                return -Math.max(5, Math.ceil(value.max * 1.2));
            },
            max: function (value: any) {
                return Math.max(5, Math.ceil(value.max * 1.2));
            }
        },
        yAxis: {
            type: 'category',
            data: levelNames,
            axisLine: {
                show: false
            },
            axisTick: {
                show: false
            },
            // 增加Y轴类目之间的间距
            inverse: true, // 反转Y轴，使得数据从上到下排列
            axisLabel: {
                margin: 20,
                color: '#666',
                // 确保标签完全显示
                width: 60,
                overflow: 'truncate'
            }
        },
        series: [
            {
                name: '男生',
                type: 'bar',
                stack: '总量',
                label: {
                    show: true,
                    position: 'right',
                    color: '#666',
                    fontSize: 12,
                    formatter: function (params: any) {
                        // 只有当数值大于0时才显示标签
                        return params.value > 0 ? params.value : '';
                    }
                },
                itemStyle: {
                    color: function (params: any) {
                        // 使用预警等级对应的颜色
                        return levelColors[params.dataIndex] || '#1890FF';
                    },
                    borderRadius: [0, 4, 4, 0] // 右侧圆角
                },
                // 设置柱子宽度，避免太宽导致重叠
                barWidth: '40%',
                barGap: '10%',
                data: maleData
            },
            {
                name: '女生',
                type: 'bar',
                stack: '总量',
                label: {
                    show: true,
                    position: 'left',
                    formatter: function (params: any) {
                        // 只有当数值小于0时才显示标签
                        return params.value < 0 ? -params.value : '';
                    },
                    color: '#666',
                    fontSize: 12
                },
                itemStyle: {
                    color: function (params: any) {
                        // 使用预警等级对应的颜色但透明度降低
                        const color = levelColors[params.dataIndex] || '#722ED1';
                        return echarts.color.modifyAlpha(color, 0.7);
                    },
                    borderRadius: [4, 0, 0, 4] // 左侧圆角
                },
                // 设置柱子宽度，避免太宽导致重叠
                barWidth: '40%',
                barGap: '10%',
                data: femaleData
            }
        ]
    };

    genderChartInstance.setOption(option);
};

// 初始化半圆仪表盘 - 整体处理进度
const initProgressGaugeChart = () => {
    if (!progressGaugeChart.value) return;

    gaugeChartInstance = echarts.init(progressGaugeChart.value);

    const option = {
        title: {
            text: '整体处理进度',
            textStyle: {
                color: '#333',
                fontSize: 14
            },
            left: 'center',
            top: 5
        },
        series: [
            {
                type: 'gauge',
                startAngle: 180,
                endAngle: 0,
                min: 0,
                max: 100,
                radius: '85%',
                center: ['50%', '70%'],
                progress: {
                    show: true,
                    width: 15,
                    itemStyle: {
                        color: {
                            type: 'linear',
                            x: 0,
                            y: 0,
                            x2: 1,
                            y2: 0,
                            colorStops: [
                                { offset: 0, color: '#13C2C2' },
                                { offset: 1, color: '#1890FF' }
                            ]
                        }
                    }
                },
                pointer: {
                    show: false
                },
                axisLine: {
                    lineStyle: {
                        width: 15,
                        color: [
                            [1, 'rgba(0, 0, 0, 0.05)']
                        ]
                    }
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                anchor: {
                    show: false
                },
                title: {
                    show: false
                },
                detail: {
                    valueAnimation: true,
                    fontSize: 20,
                    offsetCenter: [0, '0%'],
                    color: '#1890FF',
                    formatter: function (value: number) {
                        return value.toFixed(1) + '%';
                    }
                },
                data: [
                    {
                        value: (overallProgress.value.percentage * 100),
                        name: '处理进度'
                    }
                ]
            },
            {
                type: 'gauge',
                startAngle: 180,
                endAngle: 0,
                min: 0,
                max: 1,
                radius: '60%',
                center: ['50%', '70%'],
                pointer: {
                    show: false
                },
                axisTick: {
                    show: false
                },
                splitLine: {
                    show: false
                },
                axisLabel: {
                    show: false
                },
                detail: {
                    show: true,
                    fontSize: 12,
                    offsetCenter: [0, '30%'],
                    color: '#666',
                    formatter: `${overallProgress.value.handled}/${overallProgress.value.total}`
                },
                data: [{ value: 0 }],
                axisLine: {
                    show: false
                }
            }
        ]
    };

    gaugeChartInstance.setOption(option);
};

// 窗口大小变化时重置图表
const handleResize = () => {
    barChartInstance?.resize();
    genderChartInstance?.resize();
    gaugeChartInstance?.resize();
};

onMounted(() => {
    // 获取预警状态数据
    fetchWarningStatusData();

    // 获取性别预警数据
    fetchGenderWarningData();

    // 添加窗口大小变化监听
    window.addEventListener('resize', handleResize);

    // 添加额外的延时重绘，确保在容器完全渲染后正确显示
    setTimeout(() => {
        handleResize();
    }, 300);
});

onBeforeUnmount(() => {
    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);

    // 销毁图表实例
    barChartInstance?.dispose();
    genderChartInstance?.dispose();
    gaugeChartInstance?.dispose();
})
</script>

<style scoped>
.assessment-evaluation {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  overflow: hidden;
}

.module-title {
  font-size: 16px;
  color: #4ECDC4;
  margin-bottom: 10px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
  flex-shrink: 0;
}

.module-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #4ECDC4, rgba(78, 205, 196, 0.3));
  border-radius: 2px;
}

.charts-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  min-height: 0;
}

.main-chart {
  height: 50%;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(16, 35, 75, 0.2);
  flex-shrink: 0;
}

.auxiliary-charts {
  display: flex;
  gap: 10px;
  height: 50%;
  flex-shrink: 0;
}

.aux-chart {
  flex: 1;
  height: 100%;
  border-radius: 6px;
  overflow: hidden;
  background: rgba(16, 35, 75, 0.2);
  padding: 5px;
  box-sizing: border-box;
}
</style>
