package org.senyor.ai.service.impl;

import com.alibaba.dashscope.aigc.generation.Generation;
import com.alibaba.dashscope.aigc.generation.GenerationParam;
import com.alibaba.dashscope.aigc.generation.GenerationResult;
import com.alibaba.dashscope.aigc.generation.SearchOptions;
import com.alibaba.dashscope.common.Message;
import com.alibaba.dashscope.common.Role;
import com.alibaba.dashscope.exception.ApiException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import com.alibaba.dashscope.exception.InputRequiredException;

import lombok.RequiredArgsConstructor;
import org.senyor.ai.config.ApiKeyConfig;
import org.senyor.ai.service.IKnowledgeRetrievalService;
import org.senyor.common.satoken.utils.LoginHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.senyor.ai.config.DashScopeConfig;
import org.senyor.ai.domain.AiConversation;
import org.senyor.ai.domain.AiMessage;
import org.senyor.ai.domain.dto.ChatRequest;
import org.senyor.ai.domain.dto.ChatResponse;
import org.senyor.ai.mapper.AiMessageMapper;
import org.senyor.ai.service.IAiChatService;
import org.senyor.ai.service.IAiConversationService;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.ai.domain.AiConfig;

import io.reactivex.Flowable;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * AI聊天服务实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class AiChatServiceImpl extends ServiceImpl<AiMessageMapper, AiMessage> implements IAiChatService {

    private static final Logger log = LoggerFactory.getLogger(AiChatServiceImpl.class);

    private final DashScopeConfig dashScopeConfig;
    private final Generation dashScopeClient;
    private final IAiConversationService aiConversationService;
    private final ApiKeyConfig apiKeyConfig;
    private final IKnowledgeRetrievalService knowledgeRetrievalService;

    /**
     * 流式发送消息并获取回复
     *
     * @param chatRequest 聊天请求
     * @return 聊天响应流
     */
    @Override
    public Flowable<ChatResponse> streamChat(ChatRequest chatRequest) {
        String conversationId = chatRequest.getConversationId();
        boolean isNewConversation = false;

        // 预先获取用户ID，避免在异步流式回调中无法获取
        final Long currentUserId = LoginHelper.getUserId();
        final String currentUserName = LoginHelper.getUsername();
        final String tenantId = LoginHelper.getTenantId();

        try {
            // 如果没有会话ID，创建新会话
            if (conversationId == null || conversationId.isEmpty()) {
                AiConversation conversation = new AiConversation();
                // 获取配置（优先使用指定配置，其次使用默认配置）
                AiConfig config = dashScopeConfig.getConfig();

                // 设置默认模型或使用请求中的模型
                String model = chatRequest.getModel();
                if (model == null || model.isEmpty()) {
                    model = config.getModel();
                }
                conversation.setModel(model);

                // 设置用户ID和用户名
                conversation.setUserId(currentUserId);
                conversation.setUserName(currentUserName);

                String title = chatRequest.getMessage();
                if (title != null && title.length() > 20) {
                    title = title.substring(0, 20) + "...";
                }
                conversation.setTitle(title);
                // 创建新会话
                conversationId = aiConversationService.createAiConversation(conversation);
                isNewConversation = true;
            }

            // 取第一次消息前20个字符作为标题
            AiConversation conversation =aiConversationService.getAiConversationById(conversationId);
            if(conversation.getMessageCount()<=0){
                String title = chatRequest.getMessage();
                if (title != null && title.length() > 20) {
                    title = title.substring(0, 20) + "...";
                }
                conversation.setTitle(title);
                aiConversationService.updateAiConversation(conversation);
            }

            // 创建用户消息
            AiMessage userMessage = new AiMessage();
            userMessage.setConversationId(conversationId);
            userMessage.setUserId(currentUserId);
            userMessage.setTenantId(tenantId);
            userMessage.setRole("user");
            userMessage.setContent(chatRequest.getMessage());
            save(userMessage);

            // 获取历史消息
            List<AiMessage> historyMessages = getHistoryMessages(conversationId);

            // 准备消息列表
            List<Message> messages = new ArrayList<>();

            // 添加历史消息
            for (AiMessage message : historyMessages) {
                String role = message.getRole();
                if ("user".equals(role)) {
                    messages.add(Message.builder()
                        .role(Role.USER.getValue())
                        .content(message.getContent())
                        .build());
                } else if ("assistant".equals(role)) {
                    messages.add(Message.builder()
                        .role(Role.ASSISTANT.getValue())
                        .content(message.getContent())
                        .build());
                } else if ("system".equals(role)) {
                    messages.add(Message.builder()
                        .role(Role.SYSTEM.getValue())
                        .content(message.getContent())
                        .build());
                }
            }

            // 获取配置（优先使用请求中的参数，其次使用数据库配置）
            AiConfig config = dashScopeConfig.getConfig();

            // 设置模型和参数
            final String model = chatRequest.getModel() != null && !chatRequest.getModel().isEmpty() ?
                chatRequest.getModel() : config.getModel();

            Float temperature = chatRequest.getTemperature();
            if (temperature == null) {
                temperature = config.getTemperature();
            }

            Integer maxTokens = chatRequest.getMaxTokens();
            if (maxTokens == null) {
                maxTokens = config.getMaxTokens();
            }

            // 获取深度思考参数，默认false
            Boolean thinkingMode = chatRequest.getThinkingMode() != null ? chatRequest.getThinkingMode() : Boolean.FALSE;
            log.info("聊天请求 - 会话ID: {}, 深度思考模式: {}", conversationId, thinkingMode);

            // 获取联网搜索参数，默认false
            Boolean enableSearch = chatRequest.getEnableSearch() != null ? chatRequest.getEnableSearch() : Boolean.FALSE;
            log.info("聊天请求 - 会话ID: {}, 联网搜索模式: {}", conversationId, enableSearch);

            // 构建系统提示词（唯一且顺序正确）
            String systemPrompt = config.getSystemPrompt();
            
            // 优先使用请求中的知识库ID，其次使用配置中的知识库ID
            Long knowledgeId = chatRequest.getKnowledgeId();
            if (knowledgeId == null) {
                knowledgeId = config.getKnowledgeId();
            }
            
            // 如果启用了知识库检索且有知识库ID，则构建增强提示词
            Boolean enableKnowledge = chatRequest.getEnableKnowledge();
            if (enableKnowledge != null && enableKnowledge && knowledgeId != null) {
                systemPrompt = knowledgeRetrievalService.buildEnhancedPrompt(
                    chatRequest.getMessage(), knowledgeId, systemPrompt);
                log.info("启用知识库检索，知识库ID: {}", knowledgeId);
            } else if (knowledgeId != null) {
                // 兼容旧版本，如果配置中有知识库ID但没有明确启用，也进行检索
                systemPrompt = knowledgeRetrievalService.buildEnhancedPrompt(
                    chatRequest.getMessage(), knowledgeId, systemPrompt);
                log.info("使用配置的知识库检索，知识库ID: {}", knowledgeId);
            }

            // 创建请求参数构建器
            GenerationParam.GenerationParamBuilder paramsBuilder = GenerationParam.builder()
                .apiKey(apiKeyConfig.getDashscopeApiKey())
                .model(model)
                .messages(messages)
                .temperature(temperature)
                .maxTokens(maxTokens)
                .prompt(buildEnhancedSystemPrompt(systemPrompt))
                .resultFormat(GenerationParam.ResultFormat.MESSAGE)
                .incrementalOutput(true)
                .enableThinking(thinkingMode); // 深度思考参数，开启后会返回模型的思考过程

            // 根据enableSearch参数决定是否启用搜索
            if (enableSearch) {
                //searchOptions
                SearchOptions searchOptions = SearchOptions.builder()
                    .forcedSearch(true)
                    .searchStrategy("pro")
                    .build();

                paramsBuilder.enableSearch(true)
                    .searchOptions(searchOptions);
            }

            // 创建请求参数
            GenerationParam params = paramsBuilder.build();

            // 使用原子引用保存完整内容、思考过程和会话ID
            final String finalConversationId = conversationId;
            final boolean finalIsNewConversation = isNewConversation;
            AtomicReference<StringBuilder> fullContent = new AtomicReference<>(new StringBuilder());
            AtomicReference<StringBuilder> reasoningContentRef = new AtomicReference<>(new StringBuilder());
            AtomicInteger totalTokens = new AtomicInteger(0);
            AtomicReference<String> finishReasonRef = new AtomicReference<>("");
            AtomicReference<Long> messageIdRef = new AtomicReference<>(null);

            // 流式调用API并处理响应
            return dashScopeClient.streamCall(params)
                .map(result -> {
                    String content = "";
                    String reasoningContent = null;
                    if (result.getOutput() != null && result.getOutput().getChoices() != null &&
                        !result.getOutput().getChoices().isEmpty() &&
                        result.getOutput().getChoices().getFirst().getMessage() != null) {
                        content = result.getOutput().getChoices().getFirst().getMessage().getContent();
                        fullContent.get().append(content);
                        // 获取思考过程内容并累加
                        String reasoning = result.getOutput().getChoices().getFirst().getMessage().getReasoningContent();
                        if (reasoning != null && !reasoning.isEmpty()) {
                            reasoningContentRef.get().append(reasoning);
                            if (log.isDebugEnabled()) {
                                log.debug("收到思考过程内容，长度: {}", reasoning.length());
                            }
                        }
                        // 更新结束原因
                        String finishReason = result.getOutput().getChoices().getFirst().getFinishReason();
                        if (finishReason != null) {
                            finishReasonRef.set(finishReason);
                        }
                    }

                    // 如果有token信息，更新总token数
                    if (result.getUsage() != null && result.getUsage().getTotalTokens() != null) {
                        totalTokens.set(result.getUsage().getTotalTokens());
                    }

                    // 检查是否为最后一个消息
                    boolean isLast = "stop".equals(finishReasonRef.get()) || "length".equals(finishReasonRef.get());

                    // 如果是最后一个消息，保存完整回复到数据库
                    if (isLast && messageIdRef.get() == null) {
                        String finalContent = fullContent.get().toString();
                        String finalReasoningContent = reasoningContentRef.get().toString();
                        AiMessage assistantMessage = new AiMessage();
                        assistantMessage.setConversationId(finalConversationId);
                        assistantMessage.setUserId(currentUserId);
                        assistantMessage.setTenantId(tenantId);
                        assistantMessage.setRole("assistant");
                        assistantMessage.setContent(finalContent);
                        assistantMessage.setModel(model);
                        assistantMessage.setTokens(totalTokens.get());
                        assistantMessage.setFinishReason(finishReasonRef.get());
                        // 保存思考过程内容
                        if (!finalReasoningContent.isEmpty()) {
                            assistantMessage.setReasoningContent(finalReasoningContent);
                            log.info("保存思考过程到数据库，长度: {}", finalReasoningContent.length());
                        }
                        save(assistantMessage);
                        messageIdRef.set(assistantMessage.getMessageId());
                        aiConversationService.updateMessageCountAndTokens(finalConversationId, 2, totalTokens.get());
                    }

                    // 构建响应
                    ChatResponse response = new ChatResponse();
                    response.setConversationId(finalConversationId);
                    response.setContent(content);
                    response.setModel(model);
                    response.setIsNewConversation(finalIsNewConversation);
                    // 实时返回当前片段的reasoningContent，最后一次返回完整思考过程
                    String currentReasoningContent = reasoningContentRef.get().toString();
                    if (isLast) {
                        if (currentReasoningContent.length() > 0) {
                            log.info("返回完整思考过程，长度: {}", currentReasoningContent.length());
                        }
                        response.setReasoningContent(currentReasoningContent);
                        response.setMessageId(messageIdRef.get());
                        response.setTokens(totalTokens.get());
                        response.setFinishReason(finishReasonRef.get());
                    } else {
                        response.setReasoningContent(currentReasoningContent);
                    }
                    return response;
                })
                .doOnError(e -> {
                    if (e instanceof NoApiKeyException) {
                        log.error("API密钥未配置: {}", e.getMessage());
                    } else if (e instanceof ApiException) {
                        log.error("AI接口调用异常: {}", e.getMessage());
                    } else {
                        log.error("AI聊天异常: {}", e.getMessage());
                    }
                });

        } catch (Exception e) {
            log.error("流式聊天初始化异常: {}", e.getMessage());
            return Flowable.error(new ServiceException("AI服务异常：" + e.getMessage()));
        }
    }

    /**
     * 根据会话ID获取历史消息
     *
     * @param conversationId 会话ID
     * @return 消息列表
     */
    @Override
    public List<AiMessage> getHistoryMessages(String conversationId) {
        return baseMapper.selectAiMessagesByConversationId(conversationId);
    }

    /**
     * 清空会话消息
     *
     * @param conversationId 会话ID
     * @return 清除的消息数
     */
    @Override
    @Transactional
    public int clearMessages(String conversationId) {
        // 获取会话详情
        AiConversation conversation = aiConversationService.getAiConversationById(conversationId);
        if (conversation == null) {
            throw new ServiceException("会话不存在");
        }

        // 删除会话下的所有消息
        int result = baseMapper.deleteAiMessagesByConversationId(conversationId);

        // 重置会话的消息数和令牌数
        if (result > 0) {
            AiConversation updateConversation = new AiConversation();
            updateConversation.setConversationId(conversationId);
            updateConversation.setMessageCount(0);
            updateConversation.setTotalTokens(0);
            aiConversationService.updateAiConversation(updateConversation);
        }

        return result;
    }

    /**
     * 创建系统消息
     *
     * @param conversationId 会话ID
     * @param systemPrompt 系统提示词
     * @return 消息ID
     */
    @Override
    public Long createSystemMessage(String conversationId, String systemPrompt) {
        AiMessage message = new AiMessage();
        message.setConversationId(conversationId);
        message.setUserId(LoginHelper.getUserId());
        message.setRole("system");
        message.setContent(systemPrompt);

        save(message);
        return message.getMessageId();
    }

    /**
     * 创建用户消息
     *
     * @param conversationId 会话ID
     * @param content 消息内容
     * @return 消息ID
     */
    @Override
    public Long createUserMessage(String conversationId, String content) {
        AiMessage message = new AiMessage();
        message.setConversationId(conversationId);
        message.setUserId(LoginHelper.getUserId());
        message.setRole("user");
        message.setContent(content);

        save(message);
        return message.getMessageId();
    }

    /**
     * 创建助手消息
     *
     * @param conversationId 会话ID
     * @param content 消息内容
     * @param model 模型名称
     * @param tokens 令牌数
     * @param finishReason 完成原因
     * @return 消息ID
     */
    @Override
    public Long createAssistantMessage(String conversationId, String content, String model, Integer tokens, String finishReason) {
        AiMessage message = new AiMessage();
        message.setConversationId(conversationId);
        message.setUserId(LoginHelper.getUserId());
        message.setRole("assistant");
        message.setContent(content);
        message.setModel(model);
        message.setTokens(tokens);
        message.setFinishReason(finishReason);
        // 可选添加reasoningContent字段，如果需要手动创建带思考过程的消息

        save(message);
        return message.getMessageId();
    }

    @Override
    public void feedback(String conversationId, Long messageId, Integer likeStatus) {
        AiMessage msg = getById(messageId);
        if (msg == null || !msg.getConversationId().equals(conversationId)) {
            throw new ServiceException("消息不存在");
        }
        msg.setLikeStatus(likeStatus);
        updateById(msg);
    }

    /**
     * 构建增强的系统提示词，包含当前日期时间信息
     */
    private String buildEnhancedSystemPrompt(String originalPrompt) {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        String currentDateTime = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss"));
        String currentDate = now.format(java.time.format.DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        String dayOfWeek = now.getDayOfWeek().getDisplayName(java.time.format.TextStyle.FULL, java.util.Locale.CHINESE);

        StringBuilder enhancedPrompt = new StringBuilder();

        // 添加当前日期时间信息
        enhancedPrompt.append("当前真实日期时间：").append(currentDateTime)
            .append("，今天是").append(currentDate).append("，").append(dayOfWeek);

        // 添加原始系统提示词
        if (originalPrompt != null && !originalPrompt.trim().isEmpty()) {
            enhancedPrompt.append(originalPrompt);
        } else {
            enhancedPrompt.append("你是一个有用的AI助手，可以帮助用户解答问题和完成任务。");
        }

        return enhancedPrompt.toString();
    }



}
