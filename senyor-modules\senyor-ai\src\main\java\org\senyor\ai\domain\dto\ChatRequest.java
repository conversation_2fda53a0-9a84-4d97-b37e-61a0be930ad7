package org.senyor.ai.domain.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 聊天请求DTO
 *
 * <AUTHOR>
 */
@Data
@Schema(description = "聊天请求参数")
public class ChatRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会话ID（新会话不传）
     */
    @Schema(description = "会话ID（新会话不传）")
    private String conversationId;

    /**
     * 用户消息内容
     */
    @Schema(description = "用户消息内容", required = true)
    private String message;

    /**
     * 系统提示词
     */
    @Schema(description = "系统提示词")
    private String systemPrompt;

    /**
     * 模型名称
     */
    @Schema(description = "模型名称")
    private String model;

    /**
     * 最大令牌数
     */
    @Schema(description = "最大令牌数")
    private Integer maxTokens;

    /**
     * 温度参数
     */
    @Schema(description = "温度参数")
    private Float temperature;

    /**
     * 是否深度思考模式
     */
    @Schema(description = "是否深度思考模式")
    private Boolean thinkingMode = false;

    /**
     * 是否启用联网搜索
     */
    @Schema(description = "是否启用联网搜索")
    private Boolean enableSearch = false;

    /**
     * 知识库ID（用于RAG检索）
     */
    @Schema(description = "知识库ID（用于RAG检索）")
    private Long knowledgeId;

    /**
     * 是否启用知识库检索
     */
    @Schema(description = "是否启用知识库检索")
    private Boolean enableKnowledge = false;
}
