package org.senyor.ai.controller;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

import cn.dev33.satoken.annotation.SaCheckPermission;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.domain.vo.AiKnowledgeDocumentVo;
import org.senyor.ai.service.IAiKnowledgeDocumentService;
import org.senyor.ai.service.IKnowledgeRetrievalService;
import org.senyor.ai.service.IKnowledgeRetrievalService.KnowledgeChunk;
import org.senyor.common.core.domain.R;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * AI知识库文档控制器
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/documents")
@Tag(name = "AI知识库文档管理", description = "AI知识库文档相关接口")
public class AiKnowledgeDocumentController extends BaseController {

    private final IAiKnowledgeDocumentService aiKnowledgeDocumentService;
    private final IKnowledgeRetrievalService knowledgeRetrievalService;

    /**
     * 获取文档列表
     */
    @GetMapping("/list")
    @Operation(summary = "文档列表", description = "获取AI知识库文档列表")
    @SaCheckPermission("ai:knowledge:list")
    public R<List<AiKnowledgeDocumentVo>> list(AiKnowledgeDocument document) {
        return R.ok(aiKnowledgeDocumentService.selectAiKnowledgeDocumentList(document));
    }

    /**
     * 根据知识库ID获取文档列表
     */
    @GetMapping("/knowledge/{knowledgeId}")
    @Operation(summary = "知识库文档", description = "根据知识库ID获取文档列表")
    @SaCheckPermission("ai:knowledge:query")
    public R<List<AiKnowledgeDocumentVo>> getDocumentsByKnowledgeId(@PathVariable("knowledgeId") Long knowledgeId) {
        return R.ok(aiKnowledgeDocumentService.getAiKnowledgeDocumentsByKnowledgeId(knowledgeId));
    }

    /**
     * 获取文档详情
     */
    @GetMapping("/{documentId}")
    @Operation(summary = "文档详情", description = "根据文档ID获取AI知识库文档详情")
    @SaCheckPermission("ai:knowledge:query")
    public R<AiKnowledgeDocumentVo> getInfo(@PathVariable("documentId") Long documentId) {
        return R.ok(aiKnowledgeDocumentService.getAiKnowledgeDocumentById(documentId));
    }

    /**
     * 创建文档
     */
    @PostMapping
    @Operation(summary = "创建文档", description = "创建新的AI知识库文档")
    @SaCheckPermission("ai:knowledge:add")
    public R<Long> add(@Validated @RequestBody AiKnowledgeDocument document) {
        return R.ok(aiKnowledgeDocumentService.createAiKnowledgeDocument(document));
    }

    /**
     * 更新文档
     */
    @PutMapping
    @Operation(summary = "更新文档", description = "更新AI知识库文档")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> edit(@Validated @RequestBody AiKnowledgeDocument document) {
        aiKnowledgeDocumentService.updateAiKnowledgeDocument(document);
        return R.ok();
    }

    /**
     * 删除文档
     */
    @DeleteMapping("/{documentIds}")
    @Operation(summary = "删除文档", description = "删除AI知识库文档")
    @SaCheckPermission("ai:knowledge:remove")
    public R<Void> remove(@PathVariable Long[] documentIds) {
        aiKnowledgeDocumentService.deleteAiKnowledgeDocumentByIds(documentIds);
        return R.ok();
    }

    /**
     * 上传文档
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文档", description = "上传文档到知识库")
    @SaCheckPermission("ai:knowledge:add")
    public R<Long> upload(@RequestParam("knowledgeId") Long knowledgeId,
                         @RequestParam("documentName") String documentName,
                         @RequestParam("documentType") String documentType,
                         @RequestParam(value = "content", required = false) String content,
                         @RequestParam(value = "file", required = false) MultipartFile file,
                         @RequestParam(value = "remark", required = false) String remark) {

        if ("text".equals(documentType)) {
            // 文本类型，直接创建文档
            AiKnowledgeDocument document = new AiKnowledgeDocument();
            document.setKnowledgeId(knowledgeId);
            document.setDocumentName(documentName);
            document.setDocumentType(documentType);
            document.setContent(content);
            document.setRemark(remark);
            document.setFileSize((long) (content != null ? content.length() : 0));

            Long documentId = aiKnowledgeDocumentService.createAiKnowledgeDocument(document);

            aiKnowledgeDocumentService.processDocument(documentId);

            return R.ok(documentId);
        } else {
            // 文件类型，上传文件
            if (file == null || file.isEmpty()) {
                return R.fail("请选择要上传的文件");
            }

            // 创建文档对象，使用前端传递的文档名称
            AiKnowledgeDocument document = new AiKnowledgeDocument();
            document.setKnowledgeId(knowledgeId);
            document.setDocumentName(documentName); // 使用前端传递的文档名称
            document.setDocumentType(documentType);
            document.setFileSize(file.getSize());
            document.setContent(aiKnowledgeDocumentService.extractContent(file));
            document.setRemark(remark);
            document.setStatus("0"); // 待处理
            document.setProgress(0);
            document.setVectorStatus("0"); // 未向量化
            document.setChunkCount(0);

            Long documentId = aiKnowledgeDocumentService.createAiKnowledgeDocument(document);
            
            aiKnowledgeDocumentService.processDocument(documentId);

            return R.ok(documentId);
        }
    }

    /**
     * 重新处理文档
     */
    @PutMapping("/reprocess/{documentId}")
    @Operation(summary = "重新处理", description = "重新处理AI知识库文档")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> reprocess(@PathVariable("documentId") Long documentId) {
        boolean success = aiKnowledgeDocumentService.reprocessDocument(documentId);
        if (success) {
            return R.ok();
        } else {
            return R.fail("重新处理失败");
        }
    }

    /**
     * 更新文档状态
     */
    @PutMapping("/status/{documentId}/{status}")
    @Operation(summary = "更新状态", description = "更新AI知识库文档状态")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> updateStatus(@PathVariable("documentId") Long documentId, @PathVariable("status") String status) {
        aiKnowledgeDocumentService.updateDocumentStatus(documentId, status);
        return R.ok();
    }

    /**
     * 更新文档进度
     */
    @PutMapping("/progress/{documentId}/{progress}")
    @Operation(summary = "更新进度", description = "更新AI知识库文档处理进度")
    @SaCheckPermission("ai:knowledge:edit")
    public R<Void> updateProgress(@PathVariable("documentId") Long documentId, @PathVariable("progress") Integer progress) {
        aiKnowledgeDocumentService.updateDocumentProgress(documentId, progress);
        return R.ok();
    }

    /**
     * 批量重新向量化文档（应用新的文档名称向量化策略）
     */
    @PostMapping("/batch-revectorize")
    @Operation(summary = "批量重新向量化", description = "批量重新向量化文档，应用新的文档名称向量化策略")
    @SaCheckPermission("ai:knowledge:edit")
    public R<String> batchRevectorize(@RequestBody List<Long> documentIds) {
        try {
            if (documentIds == null || documentIds.isEmpty()) {
                return R.fail("请选择要重新向量化的文档");
            }
            
            int successCount = 0;
            for (Long documentId : documentIds) {
                try {
                    boolean success = aiKnowledgeDocumentService.reprocessDocument(documentId);
                    if (success) {
                        successCount++;
                    }
                } catch (Exception e) {
                    // 记录单个文档处理失败，但继续处理其他文档
                    System.err.println("文档重新向量化失败，文档ID: " + documentId + ", 错误: " + e.getMessage());
                }
            }
            
            return R.ok(String.format("批量重新向量化完成，成功: %d/%d", successCount, documentIds.size()));
        } catch (Exception e) {
            return R.fail("批量重新向量化异常：" + e.getMessage());
        }
    }

    /**
     * 测试增强检索功能
     */
    @PostMapping("/test-retrieval")
    @Operation(summary = "测试检索", description = "测试增强检索功能，返回详细的检索结果")
    @SaCheckPermission("ai:knowledge:query")
    public R<Map<String, Object>> testRetrieval(@RequestParam("query") String query,
                                               @RequestParam(value = "knowledgeId", required = false) Long knowledgeId,
                                               @RequestParam(value = "topK", defaultValue = "10") int topK) {
        try {
            if (query == null || query.trim().isEmpty()) {
                return R.fail("查询内容不能为空");
            }
            
            // 调用检索服务
            List<KnowledgeChunk> results = knowledgeRetrievalService.retrieveKnowledge(query, knowledgeId, topK);
            
            // 构建返回结果
            Map<String, Object> response = new HashMap<>();
            response.put("query", query);
            response.put("knowledgeId", knowledgeId);
            response.put("topK", topK);
            response.put("totalResults", results.size());
            response.put("results", results);
            
            // 统计信息
            Map<String, Object> stats = new HashMap<>();
            stats.put("avgScore", results.stream().mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0).average().orElse(0.0));
            stats.put("maxScore", results.stream().mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0).max().orElse(0.0));
            stats.put("minScore", results.stream().mapToDouble(r -> r.getScore() != null ? r.getScore() : 0.0).min().orElse(0.0));
            response.put("statistics", stats);
            
            return R.ok(response);
        } catch (Exception e) {
            return R.fail("检索测试失败：" + e.getMessage());
        }
    }
}
