package org.senyor.api.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.app.domain.vo.AppTemplateVo;
import org.senyor.app.service.IAppTemplateService;
import org.senyor.common.core.domain.R;
import org.senyor.common.core.domain.model.LoginUser;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.senyor.system.domain.bo.SysUserBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 模板字段api配置
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/templateField")
public class TemplateFieldController extends BaseController {
    /**
     * 模板字段服务接口
     * */
    private final IAppTemplateService templateService;

    /**
     * 查询用户自己的模板字段
     * */
    @GetMapping("/getAllFieldData")
    public R<AppTemplateVo> getAllFieldData() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        if (loginUser == null || loginUser.getUserId() == null) {
            throw new ServiceException("登录状态异常 请重新登录!");
        }
        Long uid = loginUser.getUserId();
        return R.ok(templateService.getAllFieldData(uid));
    }

    /**
     * 修改用户自己的模板字段
     * */
    @PutMapping("/editFieldData")
    public R<Void> editFieldData(@RequestBody SysUserBo bo) {
        return toAjax(templateService.editFieldData(bo));
    }



}
