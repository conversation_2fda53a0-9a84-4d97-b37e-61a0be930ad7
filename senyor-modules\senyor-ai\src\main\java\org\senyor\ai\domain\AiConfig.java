package org.senyor.ai.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;


import java.util.Date;

/**
 * AI配置表 ai_config
 *
 * <AUTHOR>
 */
@Data
@TableName("ai_config")
public class AiConfig {

    /**
     * 配置ID
     */
    @TableId(value = "config_id", type = IdType.AUTO)
    private Long configId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 配置描述
     */
    private String configDesc;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 温度参数(0-1之间)
     */
    private Float temperature;

    /**
     * 最大令牌数
     */
    private Integer maxTokens;

    /**
     * 系统提示词
     */
    private String systemPrompt;

    /**
     * 绑定知识库ID
     */
    private Long knowledgeId;

    /**
     * 创建用户ID
     */
    private Long userId;

    /**
     * 创建用户名
     */
    private String userName;

    /**
     * 是否默认配置(Y是 N否)
     */
    private String isDefault;

    /**
     * 是否系统级配置(Y是 N否)
     */
    private String isSystem;

    /**
     * 是否启用(0启用 1禁用)
     */
    private String status;

    /**
     * 备注
     */
    private String remark;
    /**
     * 创建部门
     */
    @TableField(exist = false)
    private Long createDept;

    /**
     * 创建者
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
