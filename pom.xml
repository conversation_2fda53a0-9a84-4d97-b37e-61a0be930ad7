<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.senyor</groupId>
    <artifactId>xinyue-admin</artifactId>
    <version>${revision}</version>

    <name>senyor-system</name>
    <url>https://gitee.com/senyor/senyor-Vue-Plus</url>
    <description>senyor-云平台管理系统</description>

    <properties>
        <revision>1.0.0-BETA</revision>
        <spring-boot.version>3.2.5</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>21</java.version>
        <mybatis.version>3.5.16</mybatis.version>
        <springdoc.version>2.5.0</springdoc.version>
        <therapi-javadoc.version>0.15.0</therapi-javadoc.version>
        <poi.version>5.2.3</poi.version>
        <easyexcel.version>3.3.4</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.38.0</satoken.version>
        <mybatis-plus.version>3.5.6</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.27</hutool.version>
        <okhttp.version>4.10.0</okhttp.version>
        <spring-boot-admin.version>3.2.3</spring-boot-admin.version>
        <redisson.version>3.29.0</redisson.version>
        <lock4j.version>2.2.7</lock4j.version>
        <dynamic-ds.version>4.3.0</dynamic-ds.version>
        <alibaba-ttl.version>2.14.4</alibaba-ttl.version>
        <snailjob.version>1.0.0-beta1</snailjob.version>
        <mapstruct-plus.version>1.3.6</mapstruct-plus.version>
        <mapstruct-plus.lombok.version>0.2.0</mapstruct-plus.lombok.version>
        <lombok.version>1.18.32</lombok.version>
        <bouncycastle.version>1.76</bouncycastle.version>
        <justauth.version>1.16.6</justauth.version>
        <!-- 离线IP地址定位库 -->
        <ip2region.version>2.7.0</ip2region.version>

        <!-- OSS 配置 -->
        <aws.sdk.version>2.28.22</aws.sdk.version>
        <!-- SMS 配置 -->
        <sms4j.version>3.2.1</sms4j.version>
        <!-- 限制框架中的fastjson版本 -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- 阿里云模型调用sdk -->
        <dashscope.sdk.version>2.20.8</dashscope.sdk.version>
        <!-- 阿里云人脸表情识别 sdk -->
        <aliyun.facebody.version>5.1.2</aliyun.facebody.version>
        <aliyun.credentials.version>0.3.1</aliyun.credentials.version>

        <!-- 字符串公式解析 -->
        <eval.ezylang.version>3.4.0</eval.ezylang.version>

        <pdf.box.version>2.0.27</pdf.box.version>

        <!-- 插件版本 -->
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <maven-war-plugin.version>3.2.2</maven-war-plugin.version>
        <maven-compiler-plugin.verison>3.11.0</maven-compiler-plugin.verison>
        <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
        <flatten-maven-plugin.version>1.3.0</flatten-maven-plugin.version>

        <!--工作流配置-->
        <!--<flowable.version>7.0.0</flowable.version>-->
        <deepoove.version>1.12.1</deepoove.version>
<!--        <docx4j.version>6.1.2</docx4j.version>-->
        <openpdf.version>1.3.30</openpdf.version>
        <docx4j-export-fo.version>11.4.9</docx4j-export-fo.version>
        <docx4j-core.version>11.4.9</docx4j-core.version>
        <jaxb-impl.verion>3.0.2</jaxb-impl.verion>
        <bind-api.version>4.0.2</bind-api.version>
        <jaxb-referenceImpl.version>11.4.9</jaxb-referenceImpl.version>
        <fop.version>2.9</fop.version>
        <httpclient.version>4.5.13</httpclient.version>
        <jfree.version>1.5.3</jfree.version>

    </properties>

    <profiles>
        <profile>
            <id>local</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>local</profiles.active>
                <logging.level>info</logging.level>
            </properties>
        </profile>
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>info</logging.level>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>info</logging.level>
            </properties>
        </profile>
    </profiles>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- springboot 开发包 -->
             <!--<dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-devtools</artifactId>
                <version>${spring-boot.version}</version>
                <optional>true</optional>
            </dependency>-->

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--<dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>-->

            <!--<dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-bom</artifactId>
                <version>${flowable.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>-->

            <!-- JustAuth 的依赖配置-->
            <dependency>
                <groupId>me.zhyd.oauth</groupId>
                <artifactId>JustAuth</artifactId>
                <version>${justauth.version}</version>
            </dependency>

            <!-- common 的依赖配置-->
            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-common-bom</artifactId>
                <version>${revision}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-api</artifactId>
                <version>${springdoc.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.therapi</groupId>
                <artifactId>therapi-runtime-javadoc</artifactId>
                <version>${therapi-javadoc.version}</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot3-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>cn.hutool</groupId>
                        <artifactId>hutool-all</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-core</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-annotation</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!--  AWS SDK for Java 2.x  -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${aws.sdk.version}</version>
                <exclusions>
                    <!-- 将基于 CRT 的 HTTP 客户端从类路径中移除 -->
                    <exclusion>
                        <groupId>software.amazon.awssdk</groupId>
                        <artifactId>aws-crt-client</artifactId>
                    </exclusion>
                    <!-- 将基于 Apache 的 HTTP 客户端从类路径中移除 -->
                    <exclusion>
                        <groupId>software.amazon.awssdk</groupId>
                        <artifactId>apache-client</artifactId>
                    </exclusion>
                    <!-- 将配置基于 URL 连接的 HTTP 客户端从类路径中移除 -->
                    <exclusion>
                        <groupId>software.amazon.awssdk</groupId>
                        <artifactId>url-connection-client</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <!-- 基于 AWS CRT 的 S3 客户端的性能增强的 S3 传输管理器 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!-- 将基于 Netty 的 HTTP 客户端从类路径中移除 -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>netty-nio-client</artifactId>
                <version>${aws.sdk.version}</version>
            </dependency>
            <!--短信sms4j-->
            <dependency>
                <groupId>org.dromara.sms4j</groupId>
                <artifactId>sms4j-spring-boot-starter</artifactId>
                <version>${sms4j.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
            </dependency>

            <!-- SnailJob Client -->
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-starter</artifactId>
                <version>${snailjob.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aizuda</groupId>
                <artifactId>snail-job-client-job-core</artifactId>
                <version>${snailjob.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId>
                <version>${alibaba-ttl.version}</version>
            </dependency>

            <!-- 加密包引入 -->
            <dependency>
                <groupId>org.bouncycastle</groupId>
                <artifactId>bcprov-jdk15to18</artifactId>
                <version>${bouncycastle.version}</version>
            </dependency>

            <dependency>
                <groupId>io.github.linpeilie</groupId>
                <artifactId>mapstruct-plus-spring-boot-starter</artifactId>
                <version>${mapstruct-plus.version}</version>
            </dependency>

            <!-- 离线IP地址定位库 ip2region -->
            <dependency>
                <groupId>org.lionsoul</groupId>
                <artifactId>ip2region</artifactId>
                <version>${ip2region.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/com.alibaba/dashscope-sdk-java -->
            <!-- 阿里云模型 调用sdk-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>${dashscope.sdk.version}</version>
            </dependency>

            <!--
            引入依赖包
            最低SDK版本要求：facebody20191230的SDK版本需大于等于3.0.7。 5.1.2
            可以在此仓库地址中引用最新版本SDK：https://mvnrepository.com/artifact/com.aliyun/facebody20191230
            人脸属性识别能力可以识别检测人脸的性别、年龄、表情、眼镜、帽子五种属性，支持人脸遮挡、光照、模糊度、姿态、噪声综合质量评分，支持检测含有多张人脸的照片属性判断。
            -->
            <!-- https://mvnrepository.com/artifact/com.aliyun/facebody20191230 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>facebody20191230</artifactId>
                <version>${aliyun.facebody.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>credentials-java</artifactId>
                <version>${aliyun.credentials.version}</version>
                <!--<version>1.0.1</version>-->
            </dependency>

            <!-- 公式解析 -->
            <dependency>
                <groupId>com.ezylang</groupId>
                <artifactId>EvalEx</artifactId>
                <version>${eval.ezylang.version}</version>
            </dependency>

            <!-- -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdf.box.version}</version>
            </dependency>


            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-system</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-app</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-api</artifactId>
                <version>${revision}</version>
            </dependency>
            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-scale</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-job</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-generator</artifactId>
                <version>${revision}</version>
            </dependency>

            <dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-demo</artifactId>
                <version>${revision}</version>
            </dependency>

            <!--  工作流模块  -->
            <!--<dependency>
                <groupId>org.senyor</groupId>
                <artifactId>senyor-workflow</artifactId>
                <version>${revision}</version>
            </dependency>-->

            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>${deepoove.version}</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>org.docx4j</groupId>-->
<!--                <artifactId>docx4j</artifactId>-->
<!--                <version>${docx4j.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>org.slf4j</groupId>-->
<!--                        <artifactId>slf4j-reload4j</artifactId>-->
<!--                    </exclusion>-->
<!--                    <exclusion>-->
<!--                        <groupId>javax.xml.bind</groupId>-->
<!--                        <artifactId>jaxb-api</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.github.librepdf</groupId>
                <artifactId>openpdf</artifactId>
                <version>${openpdf.version}</version>
            </dependency>

            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-export-fo</artifactId>
                <version>${docx4j-export-fo.version}</version>
            </dependency>

            <dependency>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
                <version>${jaxb-impl.verion}</version>
                <scope>runtime</scope>
            </dependency>

            <dependency>
                <groupId>jakarta.xml.bind</groupId>
                <artifactId>jakarta.xml.bind-api</artifactId>
                <version>${bind-api.version}</version>
            </dependency>

            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
                <version>${jaxb-referenceImpl.version}</version>
            </dependency>

            <dependency>
                <groupId>org.docx4j</groupId>
                <artifactId>docx4j-core</artifactId>
                <version>${docx4j-core.version}</version>
            </dependency>

            <dependency>
                <groupId>xalan</groupId>
                <artifactId>xalan</artifactId>
                <version>2.7.2</version>
            </dependency>
            <dependency>
                <groupId>net.sf.saxon</groupId>
                <artifactId>saxon</artifactId>
                <version>8.7</version>
            </dependency>


            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>fop</artifactId>
                <version>${fop.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.httpcomponents</groupId>
                <artifactId>httpclient</artifactId>
                <version>${httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jfree</groupId>
                <artifactId>jfreechart</artifactId>
                <version>${jfree.version}</version>
            </dependency>

        </dependencies>

    </dependencyManagement>


    <modules>
        <module>senyor-admin</module>
        <module>senyor-common</module>
        <module>senyor-extend</module>
        <module>senyor-modules</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.verison}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>com.github.therapi</groupId>
                            <artifactId>therapi-runtime-javadoc-scribe</artifactId>
                            <version>${therapi-javadoc.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.springframework.boot</groupId>
                            <artifactId>spring-boot-configuration-processor</artifactId>
                            <version>${spring-boot.version}</version>
                        </path>
                        <path>
                            <groupId>io.github.linpeilie</groupId>
                            <artifactId>mapstruct-plus-processor</artifactId>
                            <version>${mapstruct-plus.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${mapstruct-plus.lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <compilerArgs>
                        <arg>-parameters</arg>
                    </compilerArgs>
                </configuration>
            </plugin>
            <!-- 单元测试使用 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <argLine>-Dfile.encoding=UTF-8</argLine>
                    <!-- 根据打包环境执行对应的@Tag测试方法 -->
                    <groups>${profiles.active}</groups>
                    <!-- 排除标签 -->
                    <excludedGroups>exclude</excludedGroups>
                </configuration>
            </plugin>
            <!-- 统一版本号管理 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <version>${flatten-maven-plugin.version}</version>
                <configuration>
                    <updatePomFile>true</updatePomFile>
                    <flattenMode>resolveCiFriendliesOnly</flattenMode>
                </configuration>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>flatten.clean</id>
                        <phase>clean</phase>
                        <goals>
                            <goal>clean</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>


    <pluginRepositories>

        <pluginRepository>
            <id>central</id>
            <url>https://maven.aliyun.com/repository/central</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>

        <!--<pluginRepository>
            <id>public</id>
            <name>huawei nexus</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>-->
    </pluginRepositories>

</project>


