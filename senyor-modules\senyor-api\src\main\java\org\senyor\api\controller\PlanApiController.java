package org.senyor.api.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.senyor.api.service.IPlanApiService;
import org.senyor.app.domain.vo.AppTemplateVo;
import org.senyor.app.service.IAppTemplateService;
import org.senyor.scale.domain.vo.PlanVo;
import org.senyor.api.service.IScaleApiService;
import org.senyor.common.core.domain.R;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.web.core.BaseController;
import org.senyor.scale.domain.bo.ScaleAnswerBo;
import org.senyor.scale.domain.bo.ScaleAssessPlanBo;
import org.senyor.scale.domain.vo.ScaleAnswerVo;
import org.senyor.scale.domain.vo.ScaleAssessPlanVo;
import org.senyor.scale.service.IScaleAnswerService;
import org.senyor.scale.service.IScaleAssessPlanService;
import org.senyor.system.domain.bo.SysUserBo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * api配置
 *
 * <AUTHOR>
 * @date 2024-12-25
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/api/plan")
public class PlanApiController extends BaseController {

    /**
     * 量表服务接口
     */
    private final IScaleApiService scaleApiService;
    //用户回答service
    private final IScaleAnswerService scaleAnswerService;
    // 普查活动信息
    private final IScaleAssessPlanService scaleAssessPlanService;

    private final IPlanApiService  planApiService;

    private final IAppTemplateService appTemplateService;


    /**
     * 查询当前进行中的测评活动列表
     */
    @GetMapping("/planningList")
    public TableDataInfo<PlanVo> queryPlanning(ScaleAssessPlanBo bo, PageQuery pageQuery) {
        return scaleAssessPlanService.queryPlanning(bo, pageQuery);
    }

    /**
     * 活动详情
     *
     * @param planId 任务id
     */
    @GetMapping("/planning/{planId}")
    public R<PlanVo> queryPlanningById(@NotNull(message = "活动id不能为空")
                                                  @PathVariable Long planId) {
        PlanVo planVo = scaleAssessPlanService.queryPlanningByPlanId(planId);
        if (planVo == null) {
            return R.ok("活动不存在，或者您不在普查范围");
        }
        // 查询活动记录
        ScaleAnswerBo answerBo = new ScaleAnswerBo();
        answerBo.setUserId(LoginHelper.getUserId());
        answerBo.setPlanId(planId);
        List<ScaleAnswerVo> scaleAnswerVos = scaleAnswerService.queryEffectiveList(answerBo);
        if (CollectionUtils.isNotEmpty(scaleAnswerVos)) {
            List<Long> doneScaleIds = scaleAnswerVos.stream().map(ScaleAnswerVo::getScaleId).toList();
            planVo.setDoneScaleIds(doneScaleIds);
        }
        return R.ok(planVo);
    }

    @PutMapping("/updateUserInfoByField")
    public R<Void> updateUserInfoByField(@RequestBody SysUserBo bo) {
        return toAjax(planApiService.updateUserByField(bo));
    }

    /**
     * 根据模板ID获取对应模板字段的值
     * */
    @GetMapping("/getTemplateFieldData/{userId}/{templateId}")
    public R<AppTemplateVo> getTemplateFieldData(@PathVariable Long userId, @PathVariable Long templateId) {
        return R.ok(appTemplateService.getTemplateFieldData(userId, templateId));
    }
}
