import type { DataPointProperties, Feature, GeoJSONSource, MapFeatureProperties, WorkerMessage } from './dataScreenTypes';

const processInitData = (
    geoData: GeoJSONSource,
    featureProps: MapFeatureProperties,
    userData: Record<string, number>,
    tenantPoints: DataPointProperties[] = []
): echarts.EChartsOption => {
    console.log('processInitData--------------' + JSON.stringify(featureProps));
    if ((featureProps.level === 'district' || featureProps.level === 'town') && tenantPoints.length > 0) {
        return generateDistrictMapWithPoints(geoData, featureProps, userData, tenantPoints);
    }
    if (featureProps.level === 'town') {
        console.log('==town')
        // return generateScatterSeries(featureProps);
    }
    return processMapConfig(geoData, featureProps, userData);
};

const generateDistrictMapWithPoints = (
    geoData: GeoJSONSource,
    featureProps: MapFeatureProperties,
    userData: Record<string, number>,
    points: DataPointProperties[]
): echarts.EChartsOption => {
    const mapOption: echarts.EChartsOption = {
        tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'mousemove',
        },
        roam: true,
        series: [],
        geo: []
    };

    // 添加地图系列
    // const mapSeries: echarts.MapSeriesOption = {
    //     type: 'map',
    //     map: featureProps.name,
    //     geoId: 'mainGeo',
    //     zoom: 1.2,
    //     label: {
    //         show: true,
    //         fontSize: getLabelSize(featureProps.level),
    //         color: '#fff'
    //     },
    //     data: geoData.features.map((f) => {
    //         const adcode = f.properties.adcode.toString();
    //         return {
    //             name: f.properties.name,
    //             value: userData[adcode] || 0
    //         };
    //     }),
    //     itemStyle: {
    //         borderColor: '#c8feff',
    //         borderWidth: 0.5,
    //         shadowBlur: 3,
    //         shadowColor: '#66edff',
    //         areaColor: 'rgba(69,110,150,0.1)'
    //     },
    //     emphasis: {
    //         label: {
    //             color: '#fff'
    //         },
    //         itemStyle: {
    //             shadowBlur: 10,
    //             borderWidth: 1,
    //             areaColor: 'rgba(69,110,150,0.5)'
    //         }
    //     }
    // };

    // (mapOption.series as any[]).push(mapSeries);

    // 添加散点
    const scatterSeries: echarts.ScatterSeriesOption = {
        type: 'effectScatter',
        name: '机构分布',
        coordinateSystem: 'geo',
        geoId: 'mainGeo',
        data: points.map(point => ({
            name: point.name,
            value: point.coordinates,
            tenantId: point.tenantId
        })),
        symbolSize: 12,
        itemStyle: {
            color: {
                type: 'radial',
                x: 0.5,
                y: 0.5,
                r: 0.5,
                colorStops: [
                    { offset: 0, color: 'rgba(255,215,0,0.6)' },
                    { offset: 1, color: 'rgba(255,140,0,0.8)' }
                ],
                global: false
            }
        }
    };

    (mapOption.series as any[]).push(scatterSeries);

    // 添加geo配置
    mapOption.geo = [{
        id: 'mainGeo',
        map: featureProps.name,
        roam: true,
        zoom: 1.2,
        label: {
            show: true,
            fontSize: getLabelSize(featureProps.level),
            color: '#fff'
        },
        itemStyle: {
            borderColor: '#c8feff',
            borderWidth: 0.5,
            shadowBlur: 3,
            shadowColor: '#66edff',
            areaColor: 'rgba(69,110,150,0.1)'
        },
        emphasis: {
            label: {
                color: '#fff'
            },
            itemStyle: {
                shadowBlur: 10,
                borderWidth: 1,
                areaColor: 'rgba(69,110,150,0.5)'
            }
        }
    }];

    return mapOption;
};
// 地图配置处理（纯地理逻辑）
const processMapConfig = (
    geoData: GeoJSONSource,
    featureProps: MapFeatureProperties,
    userData: Record<string, number>
): echarts.EChartsOption => {
    const mapName = featureProps.name;
    const series = [
        {
            type: 'map',
            map: mapName,
            zoom: 1.2,
            label: {
                show: true,
                fontSize: getLabelSize(featureProps.level),
                color: '#fff'
            },
            data: geoData.features.map((f) => {
                const adcode = f.properties.adcode.toString();
                return {
                    name: f.properties.name,
                    level: f.properties.level,
                    adcode: f.properties.adcode,
                    center: f.properties.center,
                    value: userData[adcode] || 0
                };
            }),
            itemStyle: {
                borderColor: '#c8feff',
                borderWidth: 0.5,
                shadowBlur: 3,
                shadowColor: '#66edff',
                areaColor: 'rgba(69,110,150,0.1)'
            },
            emphasis: {
                label: {
                    color: '#fff'
                },
                itemStyle: {
                    shadowBlur: 10,
                    borderWidth: 1,
                    areaColor: 'rgba(69,110,150,0.5)'
                }
            }
        }
    ];
    if ('100000' == mapName) {
        series[0].zoom = 1.6; // 初始放大1.5倍
        series[0].center = [104.114, 37.55]; // 地图中心点（经纬度）
    }
    return {
        tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: '名称:{b}<br/>注册人数:{c}'
        },
        roam: true,
        series
    };
};

// 数据点配置处理（纯业务逻辑）
const generateScatterSeries = (featureProps: MapFeatureProperties): echarts.ScatterSeriesOption[] => {
    const points = generateDataPoints(featureProps);
    // console.log(`points: ${JSON.stringify(points)}`);

    const series = [
        {
            type: 'effectScatter',
            z: 100,
            coordinateSystem: 'geo', // 使用地理坐标系
            data: points,
            // symbol: function (params, key) {
            //   return "image://" + require("@/assets/logo.png");
            // },
            symbolSize: 12, // 标记的大小
            itemStyle: {
                color: {
                    type: 'radial',
                    x: 0.5,
                    y: 0.5,
                    r: 0.5,
                    colorStops: [
                        {
                            offset: 0,
                            color: 'rgba(27,214,76,0.6)'
                        },
                        {
                            offset: 0.8,
                            color: 'rgba(88,249,130,0.8)'
                        },
                        {
                            offset: 1,
                            color: 'rgba(145,249,88,0.7)'
                        }
                    ],
                    global: false
                }
            }
            // label:{show:true}
        }
    ];

    return {
        tooltip: {
            show: true,
            trigger: 'item',
            triggerOn: 'mousemove',
            formatter: '名称:{b}<br/>坐标:{c}<br/>'
        },
        series,
        geo: [
            {
                map: featureProps.name,
                layoutCenter: ['50%', '50%'],
                layoutSize: '45%',
                top: '10%',
                roam: true,
                animationDurationUpdate: 0,
                zoom: 2,
                z: 3,
                label: {
                    show: true,
                    color: '#fff',
                    // fontSize: 10,
                    fontFamily: 'Arial'
                },
                itemStyle: {
                    borderColor: '#c8feff',
                    borderWidth: 0.5,
                    shadowBlur: 3,
                    shadowColor: '#66edff',
                    areaColor: 'rgba(69,110,150,0.1)'
                },
                emphasis: {
                    label: {
                        color: '#fff'
                    },
                    itemStyle: {
                        shadowBlur: 10,
                        borderWidth: 1,
                        areaColor: 'rgba(69,110,150,0.5)'
                    }
                }
            }
        ]
    };
};

// 类型安全的标签尺寸计算
const getLabelSize = (level: string): number => {
    const sizeMap: Record<string, number> = {
        nation: 12,
        province: 11,
        city: 10,
        district: 9
    };
    return sizeMap[level] || 10;
};

// 数据点生成（带类型校验）
const generateDataPoints = (
    featureProps: MapFeatureProperties,
    tenantPoints: DataPointProperties[] = []
): DataPointProperties[] => {
    if (featureProps.level === 'district' && tenantPoints.length > 0) {
        return tenantPoints;
    }
    if (featureProps.level !== 'town') return [];
    return Array.from({ length: 15 }, (_, i) => {
        const pointType = ['hospital', 'school', 'business'][i % 3];
        return {
            id: `${featureProps.adcode}-${i}`,
            adcode: featureProps.adcode,
            name: `${featureProps.name} ${getPointName(pointType)}${i + 1}`,
            type: pointType as DataPointProperties['type'],
            count: Math.ceil(Math.random() * 10000),
            coordinates: generateRandomCoordinate(featureProps.center),
            address: `${featureProps.name} ${getStreet(i)}`
        };
    });
};

// 辅助方法
const getPointName = (type: string): string => {
    return (
        {
            hospital: '医院',
            school: '学校',
            business: '商业中心'
        }[type] || '设施'
    );
};

const generateRandomCoordinate = (center: [number, number]): [string, string] => {
    return [(center[0] + (Math.random() - 0.5) * 0.2).toFixed(6), (center[1] + (Math.random() - 0.5) * 0.2).toFixed(6)];
};

const getStreet = (index: number): string => {
    const directions = ['东', '南', '西', '北'];
    return `${directions[index % 4]}路${Math.floor(Math.random() * 100)}号`;
};

self.onmessage = (e: MessageEvent<WorkerMessage>) => {
    try {
        switch (e.data.type) {
            case 'init':
                const initOptions = processInitData(
                    e.data.payload.geoData,
                    e.data.payload.featureProps,
                    e.data.payload.userData,
                    e.data.payload.tenantPoints
                );
                self.postMessage(initOptions);
                break;

            case 'update':
                const updateOptions = processInitData(
                    e.data.payload.geoData,
                    e.data.payload.featureProps,
                    e.data.payload.userData,
                    e.data.payload.tenantPoints
                );
                self.postMessage(updateOptions);
                break;

            case 'drill':
                const nextAdcode = calculateNextAdcode(e.data.payload.feature);
                self.postMessage(nextAdcode);
                break;
        }
    } catch (err) {
        self.postMessage({ error: err instanceof Error ? err.message : 'Worker Error' });
    }
};

const calculateNextAdcode = (feature: Feature) => {
    const props = feature.properties as MapFeatureProperties;
    return props.adcode;

    /*if (adcode === '100000') return props.adcode; // 全国下钻到省
    if (adcode.endsWith('0000')) return parseInt(adcode); // 省下钻到市
    if (adcode.endsWith('00')) return parseInt(adcode); // 市下钻到区县
    return props.parent?.adcode || 100000; // 区县返回上级*/
};

