<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.ai.mapper.AiKnowledgeChunkMapper">

    <resultMap type="org.senyor.ai.domain.AiKnowledgeChunk" id="AiKnowledgeChunkResult">
        <result property="chunkId" column="chunk_id"/>
        <result property="documentId" column="document_id"/>
        <result property="knowledgeId" column="knowledge_id"/>
        <result property="content" column="content"/>
        <result property="chunkIndex" column="chunk_index"/>
        <result property="chunkSize" column="chunk_size"/>
        <result property="vectorId" column="vector_id"/>
        <result property="vectorStatus" column="vector_status"/>
        <result property="similarityScore" column="similarity_score"/>
        <result property="metadata" column="metadata"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAiKnowledgeChunkVo">
        select chunk_id, document_id, knowledge_id, content, chunk_index, chunk_size,
               vector_id, vector_status, similarity_score, metadata, user_id, user_name,
                create_by, create_time, update_by, update_time, remark
        from ai_knowledge_chunk
    </sql>

    <!-- 从指定知识库中检索相关分块 -->
    <select id="selectRelevantChunks" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where knowledge_id = #{knowledgeId}
          and vector_status = '2'  <!-- 已完成向量化 -->
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            content like concat('%', #{query}, '%')
            or content like concat('%', #{query}, '%')
          )
        </if>
        order by
        <if test="query != null and query != ''">
          case when content like concat('%', #{query}, '%') then 1 else 2 end,
        </if>
        similarity_score desc, chunk_index asc
        limit #{topK}
    </select>

    <!-- 从指定知识库中检索相关分块（支持关键词数组） -->
    <select id="selectRelevantChunksWithKeywords" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where knowledge_id = #{knowledgeId}
          and vector_status = '2'
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            content like concat('%', #{query}, '%')
            <if test="keywords != null and keywords.size() > 0">
              <foreach collection="keywords" item="kw">
                or content like concat('%', #{kw}, '%')
              </foreach>
            </if>
          )
        </if>
        order by similarity_score desc, chunk_index asc
        limit #{topK}
    </select>

    <!-- 从所有知识库中检索相关分块 -->
    <select id="selectRelevantChunksFromAll" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where vector_status = '2'  <!-- 已完成向量化 -->
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            content like concat('%', #{query}, '%')
            or content like concat('%', #{query}, '%')
          )
        </if>
        order by
        <if test="query != null and query != ''">
          case when content like concat('%', #{query}, '%') then 1 else 2 end,
        </if>
        similarity_score desc, chunk_index asc
        limit #{topK}
    </select>

    <!-- 从所有知识库中检索相关分块（支持关键词数组） -->
    <select id="selectRelevantChunksFromAllWithKeywords" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where vector_status = '2'
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            content like concat('%', #{query}, '%')
            <if test="keywords != null and keywords.size() > 0">
              <foreach collection="keywords" item="kw">
                or content like concat('%', #{kw}, '%')
              </foreach>
            </if>
          )
        </if>
        order by similarity_score desc, chunk_index asc
        limit #{topK}
    </select>

    <!-- 根据文档ID查询分块列表 -->
    <select id="selectChunksByDocumentId" parameterType="Long" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where document_id = #{documentId}
        order by chunk_index asc
    </select>

    <!-- 根据知识库ID查询分块列表 -->
    <select id="selectChunksByKnowledgeId" parameterType="Long" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where knowledge_id = #{knowledgeId}
        order by chunk_index asc
    </select>

    <!-- 删除文档的所有分块 -->
    <delete id="deleteChunksByDocumentId" parameterType="Long">
        delete from ai_knowledge_chunk where document_id = #{documentId}
    </delete>

    <!-- 删除知识库的所有分块 -->
    <delete id="deleteChunksByKnowledgeId" parameterType="Long">
        delete from ai_knowledge_chunk where knowledge_id = #{knowledgeId}
    </delete>

    <!-- 模糊匹配检索（指定知识库） -->
    <select id="selectChunksByFuzzyMatch" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where knowledge_id = #{knowledgeId}
          and vector_status = '2'
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            <!-- 完整查询匹配 -->
            content like concat('%', #{query}, '%')
            <!-- 使用MySQL的LOCATE函数进行模糊匹配 -->
            or locate(#{query}, content) > 0
            <!-- 关键词匹配 -->
            <if test="keywords != null and keywords.size() > 0">
              <foreach collection="keywords" item="keyword">
                or content like concat('%', #{keyword}, '%')
              </foreach>
            </if>
          )
        </if>
        order by 
        <if test="query != null and query != ''">
          case when content like concat('%', #{query}, '%') then 1 else 2 end,
        </if>
        chunk_index asc
        limit #{topK}
    </select>

    <!-- 模糊匹配检索（所有知识库） -->
    <select id="selectAllChunksByFuzzyMatch" resultMap="AiKnowledgeChunkResult">
        <include refid="selectAiKnowledgeChunkVo"/>
        where vector_status = '2'
          and content is not null
          and content != ''
        <if test="query != null and query != ''">
          and (
            <!-- 完整查询匹配 -->
            content like concat('%', #{query}, '%')
            <!-- 使用MySQL的LOCATE函数进行模糊匹配 -->
            or locate(#{query}, content) > 0
            <!-- 关键词匹配 -->
            <if test="keywords != null and keywords.size() > 0">
              <foreach collection="keywords" item="keyword">
                or content like concat('%', #{keyword}, '%')
              </foreach>
            </if>
          )
        </if>
        order by 
        <if test="query != null and query != ''">
          case when content like concat('%', #{query}, '%') then 1 else 2 end,
        </if>
        chunk_index asc
        limit #{topK}
    </select>

</mapper>
