package org.senyor.statistics.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.senyor.statistics.domain.vo.ScaleWarningResultVo;
import org.senyor.statistics.domain.vo.TenantBaseCodeVo;
import org.senyor.statistics.domain.vo.TenantWarningStatisticsVo;
import org.senyor.statistics.domain.vo.TenantsResultObjVo;

import java.util.List;
import java.util.Map;

@Mapper
public interface UserDataStatisticsMapper {
    // 获取所有机构ID
    @InterceptorIgnore(tenantLine = "true")
    List<String> getTenantIdsByLevel(@Param("level") String level, @Param("levelId") Integer levelId);

    // 获取用户总数及上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getUserCount(@Param("tenantIds") List<String> tenantIds);

    Map<String, Long> getHomeUserCount();

    // 获取人员总数上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getStudentUserCount(@Param("tenantIds") List<String> tenantIds);

    // 获取访问量上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getLoginCount(@Param("tenantIds") List<String> tenantIds);

    // 获取咨询师总数及上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getConsultantCount(@Param("tenantIds") List<String> tenantIds);


    // 获取咨询师总数及上个月数据
    Map<String, Long> getHomeConsultantCount();

    // 获取机构总数及上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getTenantCount(@Param("tenantIds") List<String> tenantIds);

    // 获取完善档案数及上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getProfileCount(@Param("tenantIds") List<String> tenantIds);

    Map<String, Long> getHomeProfileCount();

    // 获取测评数及上个月数据
    @InterceptorIgnore(tenantLine = "true")
    Map<String, Long> getScaleCount(@Param("tenantIds") List<String> tenantIds);

    // 获取性别分布
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getGenderDistribution(@Param("tenantIds") List<String> tenantIds);

    // 档案统计完成情况
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getProfileStatus(@Param("tenantIds") List<String> tenantIds);

    // 科普减压统计
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getRelaxationStatus(@Param("tenantIds") List<String> tenantIds, @Param("flagStr") String flagStr);

    // 科普减压 统计资源类型发布
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getRelaxationTypes(@Param("tenantIds") List<String> tenantIds);

    // 获取每个机构内的用户总数和机构名
    @InterceptorIgnore(tenantLine = "true")
    List<TenantWarningStatisticsVo> getUserNumOnTenant(@Param("tenantIds") List<String> tenantIds);

    // 心理资讯 每月咨询量统计
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getConsultationRecordsNumOnMonth(@Param("tenantIds") List<String> tenantIds);

    // 心理资讯 预约日期分布统计
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getConsultationTimeOnMonth(@Param("tenantIds") List<String> tenantIds);

    // 心理资讯 问题类型统计
    @InterceptorIgnore(tenantLine = "true")
    List<String> getConsultationRecordsNumOnQuestionType(@Param("tenantIds") List<String> tenantIds);

    // 测评活动 测评趋势统计
    @InterceptorIgnore(tenantLine = "true")
    List<ScaleWarningResultVo> getAssesPlanByType(@Param("tenantIds") List<String> tenantIds);

    // 测评活动 预警用户统计
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getWarningUserCountByLevel(@Param("tenantIds") List<String> tenantIds);

    // 测评活动 干预统计
    @InterceptorIgnore(tenantLine = "true")
    List<Map<String, Object>> getWarningUserByStatus(@Param("tenantIds") List<String> tenantIds);

    // 根据tenantId查机构信息
    @InterceptorIgnore(tenantLine = "true")
    TenantBaseCodeVo getTenantBaseCode(@Param("tenantId") String tenantId);

    // 获得地区内用户数量
    @InterceptorIgnore(tenantLine = "true")
    List<TenantBaseCodeVo> getUserNumOnLevelCode(@Param("tenantLevel") int tenantLevel,@Param("adCode") Integer adCode);

    // 获得地区内机构具体信息集合
    @InterceptorIgnore(tenantLine = "true")
    List<TenantsResultObjVo> getTenantsInArea(@Param("adCode") Integer adCode);
}
