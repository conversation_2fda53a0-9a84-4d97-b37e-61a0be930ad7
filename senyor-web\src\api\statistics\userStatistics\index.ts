import request from '@/utils/request';
import { userStatisticsVo } from './types';

/**
 * 获取区域用户统计
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getUserBaseStats(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getUserBaseStats',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 人员状况分析 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getUserConditionStats(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getUserConditionStats',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 科普解压 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @param flagStr 使用次数 标识符
 * @returns 区域用户数据
 */
export function getRelaxationStatistics(level: string, levelId: string | number, flagStr: string) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getRelaxationStatistics',
        method: 'get',
        params: {
            level,
            levelId,
            flagStr
        }
    });
}

/**
 * 附属单位预警分析 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getTenantWarningStats(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getTenantWarningStats',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 心理咨询统计 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getConsultationRecordsStats(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getConsultationRecordsStats',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 测评活动分析 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getScaleWarningRecords(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getScaleWarningRecords',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 查询机构地理位置信息
 *
 * @param tenantId 机构编号
 * @returns 当前机构位置信息
 */
export function getTenantBaseCode(tenantId: string) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getTenantBaseCode/' + tenantId,
        method: 'get',
    });
}

/**
 * 各区域人数 统计数据
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getUserNumOnLevelCode(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getUserNumOnLevelCode',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}

/**
 * 区域内的机构具体信息 统计
 *
 * @param level 区域级别
 * @param levelId 区域ID
 * @returns 区域用户数据
 */
export function getTenantsInArea(level: string, levelId: string | number) {
    return request<userStatisticsVo[]>({
        url: '/statistics/userData/getTenantsInArea',
        method: 'get',
        params: {
            level,
            levelId
        }
    });
}
