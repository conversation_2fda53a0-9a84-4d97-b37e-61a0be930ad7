package org.senyor.app.service;

import org.senyor.app.domain.vo.AppFieldVo;
import org.senyor.app.domain.vo.AppTemplateVo;
import org.senyor.app.domain.bo.AppTemplateBo;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.system.domain.bo.SysUserBo;

import java.util.Collection;
import java.util.List;

/**
 * 模板Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IAppTemplateService {

    /**
     * 查询模板
     *
     * @param id 主键
     * @return 模板
     */
    AppTemplateVo queryById(Long id);

    /**
     * 分页查询模板列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 模板分页列表
     */
    TableDataInfo<AppTemplateVo> queryPageList(AppTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的模板列表
     *
     * @param bo 查询条件
     * @return 模板列表
     */
    List<AppTemplateVo> queryList(AppTemplateBo bo);

    /**
     * 新增模板
     *
     * @param bo 模板
     * @return 是否新增成功
     */
    Long insertByBo(AppTemplateBo bo);

    /**
     * 修改模板
     *
     * @param bo 模板
     * @return 是否修改成功
     */
    Boolean updateByBo(AppTemplateBo bo);

    /**
     * 校验并批量删除模板信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据角色ID查询模板
     * @param roleId 角色id
     * */
    AppTemplateVo getTemplateByRoleId(Long roleId);
    /**
     * 获取对应模板字段的值
     * */
    AppTemplateVo getTemplateFieldData(Long userId, Long templateId);

    /**
     * 一键完善
     * @param templateId 模板id
     * */
    Boolean improve(Long templateId);

    /**
     * 根据用户ID获取对应模板字段的值
     * */
    AppTemplateVo getTemplateFieldDataByUserId(Long userId);

    /**
     * 获取全部字段的值
     * */
    AppTemplateVo getAllFieldData(Long userId);

    /**
     * 一键档案完善 单对单 仅单个用户需要完善
     * @param userId 用户ID
     * */
    Boolean improveByUserId(Long userId);

    /**
     * API - 修改用户自己的模板字段
     * */
    Boolean editFieldData(SysUserBo bo);
}
