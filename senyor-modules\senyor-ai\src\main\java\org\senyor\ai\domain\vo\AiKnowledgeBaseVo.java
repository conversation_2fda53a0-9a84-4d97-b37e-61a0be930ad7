package org.senyor.ai.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.senyor.common.mybatis.core.domain.BaseEntity;

import java.util.Date;

/**
 * AI知识库视图对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI知识库视图对象")
public class AiKnowledgeBaseVo extends BaseEntity {

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    @Schema(description = "知识库名称")
    private String knowledgeName;

    /**
     * 知识库描述
     */
    @Schema(description = "知识库描述")
    private String knowledgeDesc;

    /**
     * 知识库类型（document:文档库, qa:问答库, custom:自定义）
     */
    @Schema(description = "知识库类型")
    private String knowledgeType;

    /**
     * 知识库状态（0:启用 1:禁用）
     */
    @Schema(description = "知识库状态")
    private String status;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量")
    private Integer documentCount;

    /**
     * 向量维度
     */
    @Schema(description = "向量维度")
    private Integer vectorDimension;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private Long userId;

    /**
     * 创建用户名
     */
    @Schema(description = "创建用户名")
    private String userName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private Date updateTime;
}
