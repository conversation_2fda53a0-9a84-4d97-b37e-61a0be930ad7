-- 测试知识库数据
-- 用于验证RAG功能是否正常工作

-- 插入测试知识库
INSERT INTO ai_knowledge_base (knowledge_id, knowledge_name, knowledge_desc, knowledge_type, status, vector_dimension, user_id, user_name, tenant_id, create_by, create_time, update_by, update_time, remark) 
VALUES 
(1, '产品手册', '公司产品使用手册和功能介绍', 'document', '0', 1536, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试知识库'),
(2, '技术文档', '技术架构和开发文档', 'document', '0', 1536, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试知识库');

-- 插入测试文档
INSERT INTO ai_knowledge_document (document_id, knowledge_id, document_name, document_type, content, file_path, file_size, chunk_count, status, progress, vector_status, error_message, user_id, user_name, tenant_id, create_by, create_time, update_by, update_time, remark) 
VALUES 
(1, 1, '产品功能介绍', 'text', '我们的产品是一个智能AI助手系统，具有以下主要功能：1. 智能对话：支持自然语言交互，能够理解用户意图并提供准确回答。2. 知识库检索：基于RAG技术，能够从知识库中检索相关信息。3. 多模态支持：支持文本、图片等多种输入格式。4. 实时学习：能够从对话中学习并改进回答质量。5. 个性化定制：支持根据用户需求进行个性化配置。', NULL, 0, 0, '2', 100, '2', NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试文档'),
(2, 1, '安装指南', 'text', '产品安装步骤：1. 下载安装包：从官网下载最新版本的安装包。2. 解压文件：将下载的压缩包解压到指定目录。3. 配置环境：确保系统满足最低配置要求，包括Java 8+、MySQL 5.7+等。4. 初始化数据库：运行数据库初始化脚本，创建必要的表结构。5. 启动服务：运行启动脚本，启动AI助手服务。6. 验证安装：访问管理界面，确认服务正常运行。', NULL, 0, 0, '2', 100, '2', NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试文档'),
(3, 2, '技术架构', 'text', '系统技术架构：1. 前端：基于Vue.js 3和Element Plus构建的现代化Web界面。2. 后端：采用Spring Boot框架，提供RESTful API服务。3. 数据库：使用MySQL存储结构化数据，Redis缓存热点数据。4. AI模型：集成阿里云DashScope大语言模型，支持多种AI能力。5. 向量数据库：使用向量数据库存储文档向量，支持语义检索。6. 文件存储：支持本地存储和云存储，灵活配置存储方案。', NULL, 0, 0, '2', 100, '2', NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试文档');

-- 插入测试分块数据
INSERT INTO ai_knowledge_chunk (chunk_id, document_id, knowledge_id, content, chunk_index, chunk_size, vector_id, vector_status, similarity_score, metadata, user_id, user_name, tenant_id, create_by, create_time, update_by, update_time, remark) 
VALUES 
(1, 1, 1, '我们的产品是一个智能AI助手系统，具有以下主要功能：1. 智能对话：支持自然语言交互，能够理解用户意图并提供准确回答。2. 知识库检索：基于RAG技术，能够从知识库中检索相关信息。', 0, 200, NULL, '2', 0.95, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'),
(2, 1, 1, '3. 多模态支持：支持文本、图片等多种输入格式。4. 实时学习：能够从对话中学习并改进回答质量。5. 个性化定制：支持根据用户需求进行个性化配置。', 1, 150, NULL, '2', 0.90, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'),
(3, 2, 1, '产品安装步骤：1. 下载安装包：从官网下载最新版本的安装包。2. 解压文件：将下载的压缩包解压到指定目录。3. 配置环境：确保系统满足最低配置要求，包括Java 8+、MySQL 5.7+等。', 0, 180, NULL, '2', 0.88, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'),
(4, 2, 1, '4. 初始化数据库：运行数据库初始化脚本，创建必要的表结构。5. 启动服务：运行启动脚本，启动AI助手服务。6. 验证安装：访问管理界面，确认服务正常运行。', 1, 160, NULL, '2', 0.85, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'),
(5, 3, 2, '系统技术架构：1. 前端：基于Vue.js 3和Element Plus构建的现代化Web界面。2. 后端：采用Spring Boot框架，提供RESTful API服务。3. 数据库：使用MySQL存储结构化数据，Redis缓存热点数据。', 0, 190, NULL, '2', 0.92, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'),
(6, 3, 2, '4. AI模型：集成阿里云DashScope大语言模型，支持多种AI能力。5. 向量数据库：使用向量数据库存储文档向量，支持语义检索。6. 文件存储：支持本地存储和云存储，灵活配置存储方案。', 1, 170, NULL, '2', 0.87, NULL, 1, 'admin', '000000', 'admin', NOW(), 'admin', NOW(), '测试分块'); 