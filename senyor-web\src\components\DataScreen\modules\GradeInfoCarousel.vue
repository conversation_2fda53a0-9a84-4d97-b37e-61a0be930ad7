<template>
    <div class="grade-carousel">
        <div class="module-title">机构信息轮播</div>

        <div class="carousel-container">
            <div class="carousel-wrapper" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
                <div class="carousel-card" v-for="(grade, index) in gradesData" :key="index">
                    <div class="card-left">
                        <div class="grade-name">{{ grade.name }}</div>
                        <div class="grade-info">
                            <div class="info-item">
                                <div class="label">成员数量</div>
                                <div class="value">{{ grade.studentCount }}人</div>
                            </div>
                            <!-- <div class="info-item">
                              <div class="label">班级数量</div>
                              <div class="value">{{ grade.classCount }}班</div>
                            </div> -->
                            <div class="info-item">
                                <div class="label">预警人数</div>
                                <div class="value warning">{{ grade.warningCount }}人</div>
                            </div>
                            <div class="info-item">
                                <div class="label">咨询师</div>
                                <div class="value">{{ grade.teacherCount }}人</div>
                            </div>
                        </div>
                    </div>

                    <div class="card-right">
                        <!-- 学生数量/年级总容量进度条 -->
                        <div class="capacity-progress">
                            <div class="progress-info">
                                <span class="label">成员容量</span>
                                <span class="value">{{ grade.studentCount }}/{{ grade.capacity }}</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress" :style="{ width: `${(grade.studentCount / grade.capacity) * 100}%` }"></div>
                            </div>
                        </div>

                        <!-- 预警人数火焰图 -->
                        <div class="warning-flames">
                            <div class="flames-title">预警情况分布</div>
                            <div class="flames-container">
                                <div v-for="(level, idx) in grade.warningLevels" :key="idx" class="flame-item" :style="{
                  height: `${level.count * 5}px`,
                  backgroundColor: level.color,
                  opacity: 0.7 + (idx * 0.1)
                }">
                                    <div class="flame-tooltip">
                                        <div class="tooltip-title">{{ level.name }}</div>
                                        <div class="tooltip-count">{{ level.count }}人</div>
                                    </div>
                                </div>
                            </div>
                            <div class="flames-labels">
                                <span v-for="(level, idx) in grade.warningLevels" :key="idx">{{ level.name }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 轮播控制器 -->
            <div class="carousel-controls">
                <div class="dots">
          <span v-for="(_, index) in gradesData" :key="index" :class="['dot', { active: index === currentIndex }]"
                @click="goToSlide(index)"></span>
                </div>
                <div class="control-buttons">
                    <button class="control-btn" @click="toggleAutoplay">
                        <span v-if="!isPaused">暂停</span>
                        <span v-else>播放</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue';
import {getDeptCarouselData} from "@/api/statistics/homePage";
import {useWarningLevels} from "@/composables/useWarningLevels";
// 获取预警等级数据
const { warningLevels, fetchWarningLevels } = useWarningLevels();

// 年级轮播数据
const gradesData = ref([]);

// 当前显示的索引
const currentIndex = ref(0);

// 自动播放状态
const isPaused = ref(false);

// 自动播放定时器
let autoplayTimer: number | null = null;

// 获取部门轮播数据
const fetchDeptCarouselData = async () => {
    try {
        // 先获取所有预警等级
        await fetchWarningLevels();

        const res = await getDeptCarouselData();
        if (res.code === 200 && res.data) {
            // 处理数据，补充缺失的预警等级
            gradesData.value = res.data.map(dept => {
                // 为每个部门补充完整的预警等级数据
                const completeWarningLevels = warningLevels.value.map(level => {
                    // 查找部门中是否有该预警等级的数据
                    const existingLevel = dept.warningLevels.find(
                        deptLevel => deptLevel.warningLevelId === level.warningLevelId
                    );

                    // 如果存在则返回现有数据，否则创建一个计数为0的数据
                    return existingLevel || {
                        name: level.warningName,
                        count: 0,
                        color: level.warningColor,
                        warningLevelId: level.warningLevelId
                    };
                });

                return {
                    ...dept,
                    warningLevels: completeWarningLevels
                };
            });
        } else {
            // 如果API调用失败，使用模拟数据
            useMockData();
            ElMessage.warning('获取部门数据失败，使用模拟数据');
        }
    } catch (error) {
        console.error('获取部门轮播数据失败:', error);
        // 如果API调用失败，使用模拟数据
        useMockData();
        ElMessage.warning('获取部门数据失败，使用模拟数据');
    }
};

// 使用模拟数据
const useMockData = () => {
    gradesData.value = [
        {
            name: '一年级',
            studentCount: 3245,
            capacity: 3500,
            classCount: 18,
            warningCount: 86,
            teacherCount: 4,
            warningLevels: [
                { name: '关注', count: 25, color: '#91D5FF' },
                { name: '轻度', count: 20, color: '#FAAD14' },
                { name: '中度', count: 15, color: '#FA8C16' },
                { name: '重度', count: 8, color: '#FF4D4F' },
                { name: '紧急', count: 3, color: '#CF1322' }
            ]
        },
        {
            name: '二年级',
            studentCount: 3056,
            capacity: 3200,
            classCount: 17,
            warningCount: 93,
            teacherCount: 3,
            warningLevels: [
                { name: '关注', count: 28, color: '#91D5FF' },
                { name: '轻度', count: 22, color: '#FAAD14' },
                { name: '中度', count: 18, color: '#FA8C16' },
                { name: '重度', count: 10, color: '#FF4D4F' },
                { name: '紧急', count: 5, color: '#CF1322' }
            ]
        },
        {
            name: '三年级',
            studentCount: 2987,
            capacity: 3100,
            classCount: 16,
            warningCount: 78,
            teacherCount: 3,
            warningLevels: [
                { name: '关注', count: 24, color: '#91D5FF' },
                { name: '轻度', count: 19, color: '#FAAD14' },
                { name: '中度', count: 14, color: '#FA8C16' },
                { name: '重度', count: 8, color: '#FF4D4F' },
                { name: '紧急', count: 3, color: '#CF1322' }
            ]
        },
        {
            name: '四年级',
            studentCount: 2845,
            capacity: 3000,
            classCount: 15,
            warningCount: 65,
            teacherCount: 3,
            warningLevels: [
                { name: '关注', count: 20, color: '#91D5FF' },
                { name: '轻度', count: 18, color: '#FAAD14' },
                { name: '中度', count: 12, color: '#FA8C16' },
                { name: '重度', count: 5, color: '#FF4D4F' },
                { name: '紧急', count: 2, color: '#CF1322' }
            ]
        },
        {
            name: '五年级',
            studentCount: 2678,
            capacity: 2800,
            classCount: 14,
            warningCount: 72,
            teacherCount: 2,
            warningLevels: [
                { name: '关注', count: 22, color: '#91D5FF' },
                { name: '轻度', count: 20, color: '#FAAD14' },
                { name: '中度', count: 12, color: '#FA8C16' },
                { name: '重度', count: 6, color: '#FF4D4F' },
                { name: '紧急', count: 4, color: '#CF1322' }
            ]
        }
    ];
};

// 切换到指定幻灯片
const goToSlide = (index: number) => {
    currentIndex.value = index;
};

// 下一张幻灯片
const nextSlide = () => {
    if (gradesData.value.length === 0) return;
    currentIndex.value = (currentIndex.value + 1) % gradesData.value.length;
};

// 切换自动播放状态
const toggleAutoplay = () => {
    isPaused.value = !isPaused.value;

    if (isPaused.value) {
        if (autoplayTimer) {
            clearInterval(autoplayTimer);
            autoplayTimer = null;
        }
    } else {
        startAutoplay();
    }
};

// 开始自动播放
const startAutoplay = () => {
    if (autoplayTimer) {
        clearInterval(autoplayTimer);
    }

    autoplayTimer = window.setInterval(() => {
        nextSlide();
    }, 5000);
};

onMounted(async () => {
    await fetchDeptCarouselData();
    startAutoplay();
});

onBeforeUnmount(() => {
    if (autoplayTimer) {
        clearInterval(autoplayTimer);
    }
});
</script>

<style scoped>
.grade-carousel {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 10px;
  box-sizing: border-box;
  position: relative;
}

.module-title {
  font-size: 16px;
  color: #4ECDC4;
  margin-bottom: 10px;
  font-weight: bold;
  position: relative;
  padding-left: 10px;
}

.module-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 16px;
  background: linear-gradient(to bottom, #4ECDC4, rgba(78, 205, 196, 0.3));
  border-radius: 2px;
}

.carousel-container {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.carousel-wrapper {
  display: flex;
  height: 100%;
  transition: transform 0.5s ease;
}

.carousel-card {
  min-width: 100%;
  height: 100%;
  display: flex;
  background: rgba(16, 35, 75, 0.3);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(3px);
}

.card-left {
  width: 30%;
  padding: 15px;
  background: rgba(16, 35, 75, 0.5);
  display: flex;
  flex-direction: column;
  border-right: 1px solid rgba(78, 205, 196, 0.2);
}

.grade-name {
  font-size: 18px;
  font-weight: bold;
  color: #4ECDC4;
  margin-bottom: 15px;
  text-align: center;
  padding-bottom: 8px;
  border-bottom: 1px dotted rgba(78, 205, 196, 0.3);
}

.grade-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-item .label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.info-item .value {
  font-size: 14px;
  color: #fff;
  font-weight: bold;
}

.info-item .value.warning {
  color: #FF4D4F;
}

.card-right {
  width: 70%;
  padding: 15px;
  display: flex;
  flex-direction: column;
}

.capacity-progress {
  margin-bottom: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.progress-info .label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.progress-info .value {
  font-size: 12px;
  color: #fff;
}

.progress-bar {
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: linear-gradient(to right, #1890FF, #69c0ff);
  border-radius: 4px;
  transition: width 0.5s;
}

.warning-flames {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.flames-title {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.85);
  margin-bottom: 15px;
}

.flames-container {
  flex: 1;
  display: flex;
  align-items: flex-end;
  justify-content: space-around;
  padding: 0 10px;
}

.flame-item {
  width: 30px;
  border-radius: 4px 4px 0 0;
  position: relative;
  transition: all 0.3s;
  cursor: pointer;
}

.flame-item:hover {
  transform: translateY(-5px);
}

.flame-tooltip {
  position: absolute;
  top: -65px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: #fff;
  padding: 5px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
  pointer-events: none;
  white-space: nowrap;
}

.flame-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px;
  border-style: solid;
  border-color: rgba(0, 0, 0, 0.7) transparent transparent transparent;
}

.flame-item:hover .flame-tooltip {
  opacity: 1;
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 2px;
}

.flames-labels {
  display: flex;
  justify-content: space-around;
  margin-top: 8px;
}

.flames-labels span {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
}

.carousel-controls {
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
}

.dots {
  display: flex;
  justify-content: center;
  margin-right: 20px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  margin: 0 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.dot.active {
  background: #4ECDC4;
  box-shadow: 0 0 5px rgba(78, 205, 196, 0.7);
}

.control-buttons {
  display: flex;
}

.control-btn {
  background: rgba(24, 144, 255, 0.2);
  border: 1px solid rgba(24, 144, 255, 0.5);
  color: #4ECDC4;
  font-size: 12px;
  padding: 3px 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  outline: none;
}

.control-btn:hover {
  background: rgba(24, 144, 255, 0.4);
}
</style>
