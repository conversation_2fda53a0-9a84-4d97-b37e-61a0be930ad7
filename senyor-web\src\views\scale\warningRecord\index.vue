<template>
    <div class="p-2">
        <transition :enter-active-class="proxy?.animate.searchAnimate.enter" :leave-active-class="proxy?.animate.searchAnimate.leave">
            <div v-show="showSearch" class="mb-[10px]">
                <el-card shadow="hover">
                    <el-form ref="queryFormRef" :model="queryParams" :inline="true" label-width="auto">
                        <!-- <el-form-item label="用户ID" prop="userId">
                            <el-input v-model="queryParams.userId" placeholder="请输入用户ID" clearable
                                @change="handleQuery" />
                        </el-form-item> -->
                        <el-form-item label="量表" prop="scaleId">
                            <el-select v-model="queryParams.scaleId" clearable filterable placeholder="请选择" @change="handleQuery">
                                <el-option
                                    v-for="scaleInfo in optionsScaleInfoList"
                                    :key="scaleInfo.scaleId"
                                    :label="scaleInfo.scaleName"
                                    :value="scaleInfo.scaleId"
                                >
                                    <span style="float: left">{{ scaleInfo.scaleName }}</span>
                                    <span style="float: right; color: #8492a6; font-size: 13px">{{ scaleInfo.scaleId }}</span>
                                </el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="活动名称" prop="planName">
                            <el-input v-model="queryParams.planName" placeholder="请输入活动名称" clearable @change="handleQuery" />
                        </el-form-item>
                        <el-form-item label="部门名称" prop="deptName">
                            <el-input v-model="queryParams.deptName" placeholder="请输入部门名称" clearable @change="handleQuery" />
                        </el-form-item>
                        <el-form-item label="推荐关注" prop="recommendAttention">
                            <el-select v-model="queryParams.recommendAttention" placeholder="请选择推荐关注" clearable @change="handleQuery">
                                <el-option v-for="item in ap_attention_level" :key="item.label" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="访谈状态" prop="status">
                            <el-select v-model="queryParams.status" placeholder="请选择访谈状态" clearable @change="handleQuery">
                                <el-option v-for="item in ap_closing_status" :key="item.label" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="预警级别" prop="warningLevelId">
                            <el-select v-model="queryParams.warningLevelId" placeholder="请选择预警级别" clearable @change="handleQuery">
                                <el-option
                                    v-for="item in warningConfigList"
                                    :key="item.warningName"
                                    :label="item.warningName"
                                    :value="item.warningLevelId"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="性别" prop="sex">
                            <el-select v-model="queryParams.sex" placeholder="请选择性别" clearable @change="handleQuery">
                                <el-option v-for="item in sys_user_sex" :key="item.label" :label="item.label" :value="item.value" />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="预警时间" prop="params">
                            <el-date-picker
                                v-model="params"
                                type="datetimerange"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间"
                                format="YYYY-MM-DD HH:mm:ss"
                                date-format="YYYY/MM/DD ddd"
                                time-format="A hh:mm:ss"
                                value-format="YYYY-MM-DD HH:mm:ss"
                                @change="handleQuery"
                            />
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                        </el-form-item>
                    </el-form>
                </el-card>
            </div>
        </transition>

        <el-card shadow="never">
            <template #header>
                <el-row :gutter="10" class="mb8">
                    <!-- <el-col :span="1.5">
                        <el-button v-hasPermi="['scale:warningRecord:add']" type="primary" plain icon="Plus"
                            @click="handleAdd">新增</el-button>
                    </el-col> -->
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['scale:warningRecord:edit']"
                            type="success"
                            plain
                            icon="Edit"
                            :disabled="single"
                            @click="handleUpdate()"
                            >修改</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['scale:warningRecord:remove']"
                            type="danger"
                            plain
                            icon="Delete"
                            :disabled="multiple"
                            @click="handleDelete()"
                            >删除</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button v-hasPermi="['scale:warningRecord:export']" type="warning" plain icon="Download" @click="handleExport"
                            >导出</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['scale:warningRecord:exportWordMulti']"
                            type="success"
                            plain
                            icon="Download"
                            :disabled="multiple"
                            @click="handleExportMulti"
                            >批量导出Word</el-button
                        >
                    </el-col>
                    <el-col :span="1.5">
                        <el-button
                            v-hasPermi="['scale:warningRecord:exportPDFMulti']"
                            type="primary"
                            plain
                            icon="Download"
                            :disabled="multiple"
                            @click="handleExportPDFMulti"
                            >批量导出PDF</el-button
                        >
                    </el-col>
                    <right-toolbar v-model:showSearch="showSearch" @query-table="getList"></right-toolbar>
                </el-row>
            </template>

            <el-table v-loading="loading" :data="warningRecordList" @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column v-if="false" label="主键" align="center" prop="id" />
                <el-table-column v-if="false" label="用户ID" align="center" prop="userId" />
                <el-table-column label="姓名" align="center" prop="nickName" />
                <el-table-column label="角色" align="center" prop="roleName" />
                <el-table-column label="所属部门" align="center" prop="deptName" />
                <el-table-column label="活动名称" align="center" prop="planName" />
                <el-table-column label="性别" align="center" prop="sex">
                    <template #default="scope">
                        {{ getDictLabel(sys_user_sex, scope.row.sex) }}
                    </template>
                </el-table-column>
                <el-table-column label="量表" align="center" prop="scaleId">
                    <template #default="scope">
                        <template v-for="item in optionsScaleInfoList">
                            <template v-if="scope.row.scaleId == item.scaleId">
                                {{ item.scaleName }}
                            </template>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column label="测评回答标识" align="center" prop="answerId" />
                <el-table-column label="访谈状态" align="center" prop="status">
                    <template #default="scope">
                        {{ getDictLabel(ap_closing_status, scope.row.status) }}
                    </template>
                </el-table-column>
                <el-table-column label="推荐关注" align="center" prop="recommendAttention">
                    <template #default="scope">
                        {{ getDictLabel(ap_attention_level, scope.row.recommendAttention) }}
                    </template>
                </el-table-column>
                <el-table-column v-if="false" label="预警级别" align="center" prop="warningLevelId" />
                <el-table-column label="预警等级" align="center" prop="warningLevelId">
                    <template #default="{ row }">
                        <el-tag
                            :color="formatWarningColor(row.warningLevelId)"
                            :style="{ color: getTextColor(formatWarningColor(row.warningLevelId)) }"
                        >
                            {{ formatWarningName(row.warningLevelId) }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="预警时间" align="center" prop="createTime" />
                <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-tooltip content="修改" placement="top">
                            <el-button
                                v-hasPermi="['scale:warningRecord:edit']"
                                link
                                type="primary"
                                icon="Edit"
                                @click="handleUpdate(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="上报" placement="top">
                            <el-button
                                v-hasPermi="['scale:warningRecord:edit']"
                                link
                                type="primary"
                                icon="Pointer"
                                @click="handleReport(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="查看报告" placement="top">
                            <el-button
                                v-hasPermi="['scale:warningRecord:query']"
                                link
                                type="primary"
                                icon="Tickets"
                                @click="checkReport(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="删除" placement="top">
                            <el-button
                                v-hasPermi="['scale:warningRecord:remove']"
                                link
                                type="primary"
                                icon="Delete"
                                @click="handleDelete(scope.row)"
                            ></el-button>
                        </el-tooltip>
                        <el-tooltip content="一键咨询">
                            <el-button
                                v-if="!scope.row.recommendAttention"
                                v-hasPermi="['scale:warningRecord:remove']"
                                link
                                type="primary"
                                icon="DocumentAdd"
                                @click="handleConsult(scope.row)"
                            ></el-button>
                        </el-tooltip>
                    </template>
                </el-table-column>
            </el-table>

            <pagination
                v-show="total > 0"
                v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize"
                :total="total"
                @pagination="getList"
            />
        </el-card>
        <!-- 添加或修改预警记录对话框 -->
        <el-dialog v-model="dialog.visible" :title="dialog.title" width="600px" append-to-body :before-close="handleClose">
            <el-form ref="warningRecordFormRef" :model="form" :rules="rules" label-width="auto">
                <el-form-item label="用户ID" prop="userId">
                    <el-input v-model="form.userId" placeholder="请输入用户ID" />
                </el-form-item>
                <el-form-item label="姓名" prop="nickName">
                    <el-input v-model="form.nickName" placeholder="请输入姓名" />
                </el-form-item>
                <el-form-item label="量表ID" prop="scaleId">
                    <el-input v-model="form.scaleId" placeholder="请输入量表ID" />
                </el-form-item>
                <el-form-item label="量表回答ID" prop="answerId">
                    <el-input v-model="form.answerId" placeholder="请输入量表回答ID" />
                </el-form-item>
                <el-form-item label="预警级别ID" prop="warningLevelId">
                    <el-input v-model="form.warningLevelId" placeholder="请输入预警级别ID" />
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 上报的弹框 -->
        <el-dialog v-model="reportDialogVisible" title="上报" width="600" align-center>
            <el-form ref="reportRecordFormRef" :model="reportRecordForm" label-width="auto">
                <el-form-item label="机构id：" prop="tenantId">
                    <el-input v-model="reportRecordForm.tenantId" disabled></el-input>
                </el-form-item>
                <el-form-item label="上报机构id：" prop="reportingTenantId">
                    <el-input v-model="reportRecordForm.reportingTenantId" disabled></el-input>
                </el-form-item>
                <el-form-item label="用户id：" prop="userId">
                    <el-input v-model="reportRecordForm.userId" disabled></el-input>
                </el-form-item>
                <el-form-item label="部门名称：" prop="dept">
                    <el-input v-model="reportRecordForm.dept" disabled></el-input>
                </el-form-item>
                <el-form-item label="昵称：" prop="nikeName">
                    <el-input v-model="reportRecordForm.nikeName" disabled></el-input>
                </el-form-item>
                <el-form-item label="生日：" prop="birthDate">
                    <el-input v-model="reportRecordForm.birthDate" disabled></el-input>
                </el-form-item>
                <el-form-item label="年龄：" prop="age">
                    <el-input v-model="reportRecordForm.age" disabled></el-input>
                </el-form-item>
                <el-form-item label="性别：" prop="sex">
                    <el-input :value="getDictLabel(sys_user_sex, reportRecordForm.sex)" disabled></el-input>
                </el-form-item>
                <el-form-item label="风险级别：" prop="riskLevel">
                    <el-input v-model="reportRecordForm.riskLevel" disabled></el-input>
                </el-form-item>
                <el-form-item label="上报原因：" prop="reportingReason">
                    <el-input v-model="reportRecordForm.reportingReason" type="textarea" placeholder="请输入上报原因"></el-input>
                </el-form-item>
                <el-form-item label="处理意见：" prop="disposeSuggestion">
                    <el-input v-model="reportRecordForm.disposeSuggestion" type="textarea" placeholder="请输入处理意见"></el-input>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button :loading="buttonLoading" type="primary" @click="submitReportForm">确 定</el-button>
                    <el-button @click="cancelReport">取 消</el-button>
                </div>
            </template>
        </el-dialog>
        <!-- 报告弹窗 -->
        <el-dialog v-model="showReportVisible">
            <person-report :answer-id="reportId" :user-id="currentUserId" :scale-id="scaleId" :show-warning="shouldShowWarning"></person-report>
        </el-dialog>

        <ConsultantManagement
            ref="consultantManagementRef"
            :con-cialog-data="conCialogData"
            @confirm-call-back-con="submitCallbackCon"
        ></ConsultantManagement>
    </div>
</template>

<script setup name="WarningRecord" lang="ts">
import { listWarningRecord, getWarningRecord, delWarningRecord, addWarningRecord, updateWarningRecord } from '@/api/scale/warningRecord';
import { WarningRecordVO, WarningRecordQuery, WarningRecordForm } from '@/api/scale/warningRecord/types';
import ConsultantManagement from '@/components/ConsultantManagement/index.vue';
import { getUser } from '@/api/system/user';
import { UserInfo, UserVO, UserForm, UserQuery } from '@/api/system/user/types';
import { ReportingRecordVO, ReportingRecordForm, ReportingRecordQuery } from '@/api/scale/reportingRecord/types';
import { TenantVO, TenantForm, TenantQuery } from '@/api/system/tenant/types';
import { getTenantByTenantId } from '@/api/system/tenant';
import { addReportingRecord } from '@/api/scale/reportingRecord';
import { EarlyWarningLevelConfigVO } from '@/api/scale/earlyWarningLevelConfig/types';
import { listEarlyWarningLevelConfig } from '@/api/scale/earlyWarningLevelConfig';
import { ScaleInfoQuery, ScaleInfoVO } from '@/api/scale/scaleInfo/types';
import { listScaleInfo } from '@/api/scale/scaleInfo';
import { batchExportPDF, batchExportWord, exportWord, getReportInfo } from '@/api/scale/scaleRecord';

const { proxy } = getCurrentInstance() as ComponentInternalInstance;
const { sys_user_sex, warning_status, consultant_type, ap_closing_status, ap_attention_level } = toRefs<any>(
    proxy?.useDict('sys_user_sex', 'warning_status', 'consultant_type', 'ap_closing_status', 'ap_attention_level')
);
const warningRecordList = ref<WarningRecordVO[]>([]);
const buttonLoading = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref<Array<string | number>>([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const reportDialogVisible = ref(false);
const queryFormRef = ref<ElFormInstance>();
const warningRecordFormRef = ref<ElFormInstance>();
const reportRecordFormRef = ref<ElFormInstance>();
const userInfo = ref<UserVO>();
const optionsScaleInfoList = ref<ScaleInfoVO[]>([]);
const dialog = reactive<DialogOption>({
    visible: false,
    title: ''
});
const params = ref([]);
const warningConfigList = ref<EarlyWarningLevelConfigVO[]>([]);
const showReportVisible = ref(false);
const initFormData: WarningRecordForm = {
    userId: undefined,
    nickName: undefined,
    scaleId: undefined,
    answerId: undefined,
    warningLevelId: undefined
};
let conCialogData = reactive({
    ifEdit: undefined,
    diaLogTitle: undefined,
    conId: undefined,
    resId: undefined,
    apUserId: undefined,
    earlyWarningID: undefined // 添加预警ID字段
});

const data = reactive<PageData<WarningRecordForm, WarningRecordQuery>>({
    form: { ...initFormData },
    queryParams: {
        pageNum: 1,
        pageSize: 10,
        userId: undefined,
        nickName: undefined,
        planName: undefined,
        scaleId: undefined,
        recommendAttention: undefined,
        deptName: undefined,
        answerId: undefined,
        warningLevelId: undefined,
        orderByColumn: 'createTime',
        isAsc: 'desc',
        params: {}
    },
    rules: {
        userId: [{ required: true, message: '用户ID不能为空', trigger: 'blur' }],
        scaleId: [{ required: true, message: '量表ID不能为空', trigger: 'blur' }],
        answerId: [{ required: true, message: '量表回答ID不能为空', trigger: 'blur' }],
        warningLevelId: [{ required: true, message: '预警级别ID不能为空', trigger: 'blur' }]
    }
});

const getDictLabel = (dictOptions: any[], value: any) => {
    const item = dictOptions.find((item) => item.value === value);
    return item ? item.label : value;
};

const reportRecordForm = ref<ReportingRecordForm>({
    id: '',
    tenantId: '',
    reportingTenantId: '',
    userId: '',
    nikeName: '',
    birthDate: '',
    age: '',
    sex: '',
    dept: '',
    riskLevel: '',
    reportingReason: '',
    userName: '',
    // status:'',
    disposeSuggestion: ''
});
const { queryParams, form, rules } = toRefs(data);

/** 查询预警记录列表 */
const getList = async () => {
    console.log();

    if (params.value[0] != null && params.value[0] != '') {
        queryParams.value.params['beginTime'] = params.value[0];
    }
    if (params.value[1] != null && params.value[1] != '') {
        queryParams.value.params['endTime'] = params.value[1];
    }
    loading.value = true;
    const res = await listWarningRecord(queryParams.value);
    warningRecordList.value = res.rows;
    total.value = res.total;
    loading.value = false;
    console.log(ap_closing_status.value, '字典数据');
};
/** 查询预警配置列表 */
const getWarningConfigList = async () => {
    loading.value = true;
    const res = await listEarlyWarningLevelConfig();
    warningConfigList.value = res.rows;
    console.log('获取的预警配置信息为：', warningConfigList.value);
};
//咨询记录组件
const consultantManagementRef = ref<InstanceType<typeof ConsultantManagement>>();

const initQueryScaleInfo: ScaleInfoQuery = {
    pageNum: 1,
    pageSize: 1000,
    scaleId: undefined,
    scaleName: undefined,
    scaleType: undefined,
    category: undefined,
    params: {}
};
/** 查询量表信息列表 */
const getOptionsScaleInfoList = async () => {
    const res = await listScaleInfo(initQueryScaleInfo);
    //console.log(`initQueryScaleInfo:${JSON.stringify(initQueryScaleInfo)},listScaleInfo:${JSON.stringify(res)}`);
    optionsScaleInfoList.value = res.rows;
};
/** 取消按钮 */
const cancel = () => {
    reset();
    dialog.visible = false;
};
const cancelReport = () => {
    reportDialogVisible.value = false;
};
/** 关闭dialog前置事件 */
const handleClose = (done) => {
    cancel();
    done();
};

//修改办理人
const submitCallbackCon = async (data) => {
    getList();
};

/** 表单重置 */
const reset = () => {
    form.value = { ...initFormData };
    warningRecordFormRef.value?.resetFields();
};
/**上报表单重置 */
const resetReport = () => {
    reportRecordFormRef.value?.resetFields();
    reportRecordForm.value = {
        tenantId: userInfo.value.tenantId || '',
        userId: userInfo.value.userId || '',
        dept: userInfo.value.deptName || '',
        nikeName: userInfo.value.nickName || '',
        birthDate: userInfo.value.birth || '',
        age: userInfo.value.age || '',
        sex: userInfo.value.sex || '',
        reportingReason: '',
        disposeSuggestion: ''
    };
};
/** 搜索按钮操作 */
const handleQuery = () => {
    queryParams.value.pageNum = 1;
    getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
    params.value = [];
    queryFormRef.value?.resetFields();
    handleQuery();
};

/** 多选框选中数据 */
const handleSelectionChange = (selection: WarningRecordVO[]) => {
    ids.value = selection.map((item) => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
};

/** 新增按钮操作 */
const handleAdd = () => {
    reset();
    dialog.visible = true;
    dialog.title = '添加预警记录';
};

/** 修改按钮操作 */
const handleUpdate = async (row?: WarningRecordVO) => {
    reset();
    const _id = row?.id || ids.value[0];
    const res = await getWarningRecord(_id);
    Object.assign(form.value, res.data);
    dialog.visible = true;
    dialog.title = '修改预警记录';
};

/** 提交按钮 */
const submitForm = () => {
    warningRecordFormRef.value?.validate(async (valid: boolean) => {
        if (valid) {
            buttonLoading.value = true;
            if (form.value.id) {
                await updateWarningRecord(form.value).finally(() => (buttonLoading.value = false));
            } else {
                await addWarningRecord(form.value).finally(() => (buttonLoading.value = false));
            }
            proxy?.$modal.msgSuccess('操作成功');
            dialog.visible = false;
            await getList();
        }
    });
};

/** 删除按钮操作 */
const handleDelete = async (row?: WarningRecordVO) => {
    const _ids = row?.id || ids.value;
    await proxy?.$modal.confirm('是否确认删除预警记录编号为"' + _ids + '"的数据项？').finally(() => (loading.value = false));
    await delWarningRecord(_ids);
    proxy?.$modal.msgSuccess('删除成功');
    await getList();
};

/**咨询按钮操作 */
const handleConsult = async (row?: WarningRecordVO) => {
    // 设置弹窗标题和模式
    conCialogData.diaLogTitle = '添加咨询记录';
    conCialogData.ifEdit = 0;

    // 传递用户ID
    conCialogData.apUserId = row.userId;

    // 传递预警ID
    conCialogData.earlyWarningID = row.id;

    // 打开咨询管理组件
    consultantManagementRef.value.open();
};

/** 导出按钮操作 */
const handleExport = () => {
    proxy?.download(
        'scale/warningRecord/export',
        {
            ...queryParams.value
        },
        `warningRecord_${new Date().getTime()}.xlsx`
    );
};
/**上报按钮操作 */
const handleReport = async (row?: WarningRecordVO) => {
    reportDialogVisible.value = true;
    const userId = row.userId;
    //查询用户信息
    const res = await getUser(userId);
    userInfo.value = res.data.user;
    //获取机构编号
    const tenantIdRow = userInfo.value.tenantId;
    //查询上级机构编号
    // const resTenant = await getTenantByTenantId(tenantIdRow);
    // tenantInfo.value = resTenant.data;

    //将查询到的用户信息绑定到表单
    reportRecordForm.value = {
        tenantId: userInfo.value.tenantId || '', //用户tenentId
        reportingTenantId: tenantInfo.value.parentTenantId || '', //上级tenantId
        userId: userInfo.value.userId || '',
        dept: userInfo.value.deptName || '',
        nikeName: userInfo.value.nickName || '',
        birthDate: userInfo.value.birth || '',
        age: userInfo.value.age || '',
        sex: userInfo.value.sex || '',
        reportingReason: '',
        disposeSuggestion: '',
        riskLevel: `${row.warningLevelId}`
    };
};
/** 提交上报表单 */
const submitReportForm = async () => {
    console.log('提交数据：', reportRecordForm.value);
    buttonLoading.value = true;
    try {
        await addReportingRecord(reportRecordForm.value);
        proxy?.$modal.msgSuccess('操作成功');
        reportDialogVisible.value = false;
    } finally {
        buttonLoading.value = false;
    }
};
/** 格式化预警配置名称*/
const formatWarningName = (warningLevelId) => {
    const config = warningConfigList.value.find((item) => item.warningLevelId === warningLevelId);
    return config ? config.warningName : warningLevelId;
};
/** 格式化预警配置颜色*/
const formatWarningColor = (warningLevelId) => {
    const config = warningConfigList.value.find((item) => item.warningLevelId === warningLevelId);
    return config ? config.warningColor : '#FFFFFF';
};
/** 根据warningLevelId获取预警颜色 */
const getWarningColor = (warningLevelId) => {
    const config = warningConfigList.value.find((item) => item.warningLevelId === warningLevelId);
    return config ? config.warningColor : '#FFFFFF';
};

/** 根据背景色计算文字颜色（确保可读性） */
const getTextColor = (bgColor) => {
    if (!bgColor) return '#000000';

    // 处理颜色格式，确保是6位hex
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.length === 3 ? hex[0] + hex[0] : hex.substr(0, 2), 16);
    const g = parseInt(hex.length === 3 ? hex[1] + hex[1] : hex.substr(2, 2), 16);
    const b = parseInt(hex.length === 3 ? hex[2] + hex[2] : hex.substr(4, 2), 16);

    // 计算亮度（YIQ公式）
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 128 ? '#000000' : '#FFFFFF';
};
/** 查看报告*/
const reportId = ref<string | number>(0);
const currentUserId = ref<string | number>(0);
const scaleId = ref<string | number>(0);
const checkReport = async (row?: WarningRecordVO) => {
    reportId.value = row.answerId;
    currentUserId.value = row.userId;
    scaleId.value = row.scaleId;
    console.log(`answerId:${reportId.value}, userId = ${row.userId},scaleId = ${row.scaleId}`);

    showReportVisible.value = true;
    const userId = row.userId;
    const res = await getUser(userId);
    userInfo.value = res.data.user;
    console.log('查询的用户信息为：', userInfo.value);
};
const reportType = ref<'normal' | 'abnormal'>('abnormal');
const shouldShowWarning = computed(() => {
    return reportType.value === 'abnormal';
});

/**
 * 批量导出数据
 * @param exportType
 */
const handleBatchExport = async (exportType: 'word' | 'pdf') => {
    try {
        loading.value = true;
        const selectedIds = ids.value;
        if (selectedIds.length === 0) {
            proxy?.$modal.msgWarning('请至少选择一条记录进行导出');
            return;
        }
        //获取选中的记录
        const selectedRecords = warningRecordList.value.filter((record) => selectedIds.includes(record.id));
        //准备批量导出的数据
        const batchExportData = await prepareExportData(selectedRecords);
        if (batchExportData.exportDataVoList.length === 0) {
            proxy?.$modal.msgError('没有可导出的有效数据');
            return;
        }
        //调用对应导出接口
        const exportFn = exportType === 'word' ? batchExportWord : batchExportPDF;
        const res = await exportFn(batchExportData);
        if (res.code === 200) {
            proxy?.$modal.msgSuccess(`已成功提交${batchExportData.exportDataVoList.length}个报告批量导出任务，请到任务下载查看`);
        } else {
            throw new Error(res.msg || '导出任务提交失败');
        }
    } catch (error) {
        console.error('批量导出失败：', error);
        proxy?.$modal.msgError(error.message || '批量导出失败，请稍后重试');
    } finally {
        loading.value = false;
    }
};
/**
 * 准备导出数据
 */
const prepareExportData = async (records: any[]) => {
    const exportDataVoList = await Promise.all(
        records.map(async (record) => {
            try {
                const userRes = await getUser(record.userId);
                const userInfo = userRes.data.user;
                const reportRes = await getReportInfo(record.answerId, record.scaleId);
                const reportInfo = reportRes.data;
                //判断是否有预警信息
                const hasWarning = reportInfo.scaleEarlyWarningRecordVoList?.length > 0;
                return {
                    userInfo: {
                        userName: userInfo.userName || '暂无',
                        deptName: userInfo.deptName || '暂无',
                        age: userInfo.age || '暂无',
                        phonenumber: userInfo.phonenumber || '暂无'
                    },
                    scaleInfo: {
                        scaleName: reportInfo.scaleInfoVo?.scaleName || '暂无',
                        introduction: reportInfo.scaleInfoVo?.introduction || '暂无',
                        description: reportInfo.scaleInfoVo?.description || '暂无'
                    },
                    evaluations:
                        reportInfo.scaleRecordVoList?.map((item) => ({
                            factor: item.factor || '暂无',
                            score: Number(item.score) || 0,
                            appraiseTitle: item.appraiseTitle || '暂无',
                            appraiseContent: item.appraiseContent || '暂无',
                            appraiseIntro: item.appraiseIntro || '暂无',
                            createTime: item.createTime || '无'
                        })) || [],
                    scaleEarlyWarningRecordVoList: hasWarning
                        ? reportInfo.scaleEarlyWarningRecordVoList?.map((item) => ({
                              warningGroupName: item.warningGroupName || '暂无',
                              warningLevelName: item.warningLevelName || '暂无',
                              warningIntro: item.warningIntro || '暂无',
                              warningSuggestions: item.warningSuggestions || '暂无',
                              remark: item.remark || '暂无'
                          }))
                        : undefined,
                    answers: {
                        answer: reportInfo.scaleAnswerVo?.answer
                    },
                    _hasWarning: hasWarning
                };
            } catch (error) {
                console.error(`准备用户${record.userId}的导出数据失败`, error);
                return null;
            }
        })
    );
    const validDataList = exportDataVoList.filter(Boolean);
    //判断是否有预警信息
    const hasAnyWarning = validDataList.some((item) => item._hasWarning);
    return {
        exportDataVoList: validDataList,
        dictType: 'sys_file_template',
        dictLabel: hasAnyWarning ? '个人报告' : '个人报告（不含预警）'
    };
};
/**
 * 批量导出word
 */
const handleExportMulti = async () => {
    await handleBatchExport('word');
};
/**
 * 批量导出pdf
 */
const handleExportPDFMulti = async () => {
    await handleBatchExport('pdf');
};
onMounted(() => {
    getList();
    getWarningConfigList();
    getOptionsScaleInfoList();
});
</script>
