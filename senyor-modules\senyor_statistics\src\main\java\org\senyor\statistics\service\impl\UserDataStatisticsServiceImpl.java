package org.senyor.statistics.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.core.utils.StringUtils;
import org.senyor.statistics.domain.vo.*;
import org.senyor.statistics.mapper.UserDataStatisticsMapper;
import org.senyor.statistics.service.IUserDataStatisticsService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class UserDataStatisticsServiceImpl implements IUserDataStatisticsService {

    private final UserDataStatisticsMapper userDataStatisticsMapper;

    /**
     * 根据层级和区域ID查询该地区的统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public UserBaseDataStatisticsVo getUserBaseStats(String level, Integer levelId) {
        UserBaseDataStatisticsVo result = UserBaseDataStatisticsVo.initObj();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return result;
        }

        // 用户总数及增长率
        Map<String, Long> userCountMap = userDataStatisticsMapper.getUserCount(tenantIds);
        if (objIsNull(userCountMap)) {
            return result;
        }
        result.setSystemUserNum(userCountMap.get("currentTotal"));
        result.setSystemUserNumGrowth(calculateGrowth(userCountMap));

        // 咨询师总数及增长率
        Map<String, Long> consultantCountMap = userDataStatisticsMapper.getConsultantCount(tenantIds);
        if (objIsNull(consultantCountMap)) {
            return result;
        }
        result.setConsultantNum(consultantCountMap.get("currentTotal"));
        result.setConsultantNumGrowth(calculateGrowth(consultantCountMap));

        // 附属机构数量及增长率
        Map<String, Long> tenantCountMap = userDataStatisticsMapper.getTenantCount(tenantIds);
        if (objIsNull(tenantCountMap)) {
            return result;
        }
        result.setChildTenantNum(tenantCountMap.get("currentTotal"));
        result.setChildTenantNumGrowth(calculateGrowth(tenantCountMap));

        // 计算咨询师占比及增长率
        calculateConsultantRatio(result, userCountMap, consultantCountMap);

        // 已完善的档案数量及增长率
        Map<String, Long> profileCountMap = userDataStatisticsMapper.getProfileCount(tenantIds);
        if (objIsNull(profileCountMap)) {
            return result;
        }
        result.setUserProfileNum(profileCountMap.get("currentTotal"));
        result.setUserProfileNumGrowth(calculateGrowth(profileCountMap));

        //  访问量上个月数据
        Map<String, Long> loginCountMap = userDataStatisticsMapper.getLoginCount(tenantIds);
        if (objIsNull(loginCountMap)) {
            return result;
        }
        result.setVisitsNum(loginCountMap.get("currentTotal"));
        result.setVisitsNumGrowth(calculateGrowth(loginCountMap));

        // 测评记录数量及增长率
        Map<String, Long> scaleCountMap = userDataStatisticsMapper.getScaleCount(tenantIds);
        if (objIsNull(scaleCountMap)) {
            return result;
        }
        result.setScaleNum(scaleCountMap.get("currentTotal"));
        result.setScaleNumGrowth(calculateGrowth(scaleCountMap));

        // 人员总数及增长率
        Map<String, Long> studentUserCount = userDataStatisticsMapper.getStudentUserCount(tenantIds);
        if (objIsNull(studentUserCount)) {
            return result;
        }
        result.setUserNum(studentUserCount.get("currentTotal")); // 系统用户数
        result.setUserNumGrowth(calculateGrowth(studentUserCount)); // 系统用户数增长率

        return result;
    }

    /**
     * 人员状况分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public UserConditionDataStatisticsVo getUserConditionStats(String level, Integer levelId) {
        UserConditionDataStatisticsVo result = UserConditionDataStatisticsVo.initObj();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return result;
        }
        List<Map<String, Object>> profileStatus = userDataStatisticsMapper.getProfileStatus(tenantIds);
        if (profileStatus == null || profileStatus.isEmpty()) {
            return result;
        }
        for (Map<String, Object> item : profileStatus) {
            Object flagObj = item.get("flag");
            Object countObj = item.get("count");
            if (flagObj != null && countObj != null) {
                int flag = Integer.parseInt(flagObj.toString());
                long count = Long.parseLong(countObj.toString());
                switch (flag) {
                    case 0:
                        result.setProfileEmptyNum(count);
                        break;
                    case 1:
                        result.setProfileNoNum(count);
                        break;
                    case 2:
                        result.setProfileYesNum(count);
                        break;
                    default:
                        result.setProfileYesNum(0L);
                        result.setProfileNoNum(0L);
                        result.setProfileEmptyNum(0L);
                        break;
                }
            }
        }

        List<Map<String, Object>> genderDistribution = userDataStatisticsMapper.getGenderDistribution(tenantIds);
        if (genderDistribution == null || genderDistribution.isEmpty()) {
            return result;
        }
        for (Map<String, Object> item : genderDistribution) {
            Object genderObj = item.get("gender");
            Object countObj = item.get("count");
            if (genderObj != null && countObj != null) {
                int flag = Integer.parseInt(genderObj.toString());
                long count = Long.parseLong(countObj.toString());
                switch (flag) {
                    case 0:
                        result.setManNum(count);
                        break;
                    case 1:
                        result.setWomanNum(count);
                        break;
                    default:
                        result.setManNum(0L);
                        result.setWomanNum(0L);
                        break;
                }
            }
        }
        result.setUserNum(result.getManNum() + result.getWomanNum());
        return result;
    }

    /**
     * 科普解压 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public RelaxationStatisticsVo getRelaxationStatistics(String level, Integer levelId, String flagStr) {
        RelaxationStatisticsVo result = RelaxationStatisticsVo.initObj();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return result;
        }
        if (flagStr == null || StringUtils.isBlank(flagStr)) {
            flagStr = "app_relaxation_raining";
        }
        List<Long> useNumList = result.getUseNumList();
        List<String> nameList = result.getNameList();
        List<Map<String, Object>> relaxationStatus = userDataStatisticsMapper.getRelaxationStatus(tenantIds, flagStr);
        if (relaxationStatus == null || relaxationStatus.isEmpty()) {
            return result;
        }
        for (Map<String, Object> item : relaxationStatus) {
            Object nameObj = item.get("name");
            Object countObj = item.get("count");
            if (nameObj != null && countObj != null) {
                String name = nameObj.toString();
                long count = Long.parseLong(countObj.toString());
                nameList.add(name);
                useNumList.add(count);
            }
        }
        result.setNameList(nameList);
        result.setUseNumList(useNumList);

        List<Map<String, Object>> relaxationTypes = userDataStatisticsMapper.getRelaxationTypes(tenantIds);
        if (relaxationTypes == null || relaxationTypes.isEmpty()) {
            return result;
        }
        for (Map<String, Object> item : relaxationTypes) {
            Object typeObj = item.get("type");
            Object countObj = item.get("count");
            if (typeObj != null && countObj != null) {
                int flag = Integer.parseInt(typeObj.toString());
                long count = Long.parseLong(countObj.toString());
                switch (flag) {
                    case 0:
                        result.setAudioNum(count);
                        break;
                    case 1:
                        result.setVideoNum(count);
                        break;
                    default:
                        result.setAudioNum(0L);
                        result.setVideoNum(0L);
                        break;
                }
            }
        }
        return result;
    }

    /**
     * 附属单位预警分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public List<TenantWarningStatisticsVo> getTenantWarningStats(String level, Integer levelId) {
        ArrayList<TenantWarningStatisticsVo> resultList = new ArrayList<>();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return resultList;
        }
        List<TenantWarningStatisticsVo> userNumOnTenant = userDataStatisticsMapper.getUserNumOnTenant(tenantIds);
        if (objIsNull(userNumOnTenant)) {
            return resultList;
        }
        return userNumOnTenant;
    }

    /**
     * 心理咨询统计 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public ConsultationRecordsStatisticsVo getConsultationRecordsStats(String level, Integer levelId) {
        ConsultationRecordsStatisticsVo result = ConsultationRecordsStatisticsVo.initObj();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return result;
        }

        // 生成最近6个月的月份列表（1-12）
        List<String> lastSixMonths = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        for (int i = 5; i >= 0; i--) {
            cal.add(Calendar.MONTH, -i);
            lastSixMonths.add(String.valueOf(cal.get(Calendar.MONTH) + 1));
            cal.add(Calendar.MONTH, i);
        }

        // 处理咨询记录数量统计
        Map<String, Long> recordsMap = new HashMap<>();
        List<Map<String, Object>> recordsData = userDataStatisticsMapper.getConsultationRecordsNumOnMonth(tenantIds);
        if (recordsData != null) {
            recordsData.forEach(item -> {
                if (item.get("month") != null && item.get("count") != null) {
                    recordsMap.put(item.get("month").toString(), Long.parseLong(item.get("count").toString()));
                }
            });
        }
        result.setMonthList(new ArrayList<>(lastSixMonths));
        result.setUseCountList(lastSixMonths.stream()
            .map(month -> recordsMap.getOrDefault(month, 0L))
            .collect(Collectors.toList()));

        // 处理咨询时间统计
        Map<String, Long> timeMap = new HashMap<>();
        List<Map<String, Object>> timeData = userDataStatisticsMapper.getConsultationTimeOnMonth(tenantIds);
        if (timeData != null) {
            timeData.forEach(item -> {
                if (item.get("month") != null && item.get("count") != null) {
                    timeMap.put(item.get("month").toString(), Long.parseLong(item.get("count").toString()));
                }
            });
        }
        result.setConsultationMonthList(new ArrayList<>(lastSixMonths));
        result.setConsultationUseCountList(lastSixMonths.stream()
            .map(month -> timeMap.getOrDefault(month, 0L))
            .collect(Collectors.toList()));

        // 处理问题类型统计
        List<String> questionTypes = userDataStatisticsMapper.getConsultationRecordsNumOnQuestionType(tenantIds);
        if (questionTypes != null) {
            Map<String, Long> typeCountMap = new HashMap<>();
            questionTypes.stream()
                .filter(StringUtils::isNotEmpty)
                .flatMap(type -> Arrays.stream(type.split("，")))
                .filter(StringUtils::isNotEmpty)
                .forEach(type -> typeCountMap.put(type, typeCountMap.getOrDefault(type, 0L) + 1));

            result.setQuestionTypeList(new ArrayList<>(typeCountMap.keySet()));
            result.setQuestionCountList(new ArrayList<>(typeCountMap.values()));
        }

        return result;
    }

    /**
     * 测评活动分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public ScaleWarningRecordsStatisticsVo getScaleWarningRecords(String level, Integer levelId) {
        ScaleWarningRecordsStatisticsVo result = ScaleWarningRecordsStatisticsVo.initObj();
        List<String> tenantIds = userDataStatisticsMapper.getTenantIdsByLevel(level, levelId);
        if (tenantIds == null || tenantIds.isEmpty()) {
            return result;
        }
        List<ScaleWarningResultVo> assesPlanByType = userDataStatisticsMapper.getAssesPlanByType(tenantIds);
        if (assesPlanByType == null || assesPlanByType.isEmpty()) {
            return result;
        }
        Calendar calendar = Calendar.getInstance();
        int currentMonth = calendar.get(Calendar.MONTH) + 1;
        int startMonth = currentMonth - 5;
        int currentYear = calendar.get(Calendar.YEAR);
        if (startMonth <= 0) {
            startMonth += 12;
        }
        List<Integer> monthList = new ArrayList<>();
        List<Long> scaleGDataList = new ArrayList<>();
        List<Long> scaleTDataList = new ArrayList<>();
        for (int i = 0; i < 6; i++) {
            int month = startMonth + i;
            if (month > 12) {
                month -= 12;
            }
            monthList.add(month);
            scaleGDataList.add(0L);
            scaleTDataList.add(0L);
        }
        for (ScaleWarningResultVo vo : assesPlanByType) {
            int month = vo.getMonth();
            int index = monthList.indexOf(month);
            if (index >= 0) {
                if ("G".equals(vo.getAssessType())) {
                    scaleGDataList.set(index, scaleGDataList.get(index) + vo.getCount());
                } else if ("T".equals(vo.getAssessType())) {
                    scaleTDataList.set(index, scaleTDataList.get(index) + vo.getCount());
                }
            }
        }
        result.setScaleTypeList(Arrays.asList("G", "T"));
        result.setScaleGDataList(scaleGDataList);
        result.setScaleTDataList(scaleTDataList);
        result.setMonthList(monthList);

        List<Map<String, Object>> warningUserCountByLevel = userDataStatisticsMapper.getWarningUserCountByLevel(tenantIds);
        if (objIsNull(warningUserCountByLevel)) {
            return result;
        }
        ArrayList<String> warningLevelList = new ArrayList<>();
        ArrayList<Long> warningUserCountList = new ArrayList<>();
        Long warningUserCount = 0L;
        for (Map<String, Object> item : warningUserCountByLevel) {
            Object typeObj = item.get("type");
            Object countObj = item.get("count");
            if (typeObj != null && countObj != null) {
                String type = typeObj.toString();
                long count = Long.parseLong(countObj.toString());
                warningLevelList.add(type);
                warningUserCountList.add(count);
                warningUserCount += count;
            }
        }
        result.setWarningLevelList(warningLevelList);
        result.setWarningUserCountList(warningUserCountList);
        result.setWarningUserCount(warningUserCount);

        List<Map<String, Object>> warningUserByStatus = userDataStatisticsMapper.getWarningUserByStatus(tenantIds);
        if (objIsNull(warningUserByStatus)){
            return result;
        }
        ArrayList<String> riskStatusNameList = new ArrayList<>();
        ArrayList<Long> riskStatusCountList = new ArrayList<>();
        long riskStatusRateNum = 0L;
        long riskStatusRateOnOk = 0L;
        for (Map<String, Object> item : warningUserByStatus) {
            Object typeObj = item.get("type");
            Object countObj = item.get("count");
            if (typeObj != null && countObj != null) {
                String type = typeObj.toString();
                long count = Long.parseLong(countObj.toString());
                riskStatusNameList.add(type);
                riskStatusCountList.add(count);
                riskStatusRateNum += count;
                if ("6003".equals(type)) {
                    riskStatusRateOnOk += count;
                }
            }
        }
        Double riskStatusRate = calculateRatio(riskStatusRateNum , riskStatusRateOnOk);
        result.setRiskStatusNameList(riskStatusNameList);
        result.setWarningNumTotal(riskStatusRateNum);
        result.setRiskStatusCountList(riskStatusCountList);
        result.setRiskStatusRate(riskStatusRate);
        return result;
    }

    /**
     * 获取用户当前机构的信息 地区级别,adCode等
     * @param tenantId 机构编号
     * */
    @Override
    public TenantBaseCodeVo getTenantBaseCode(String tenantId) {
        TenantBaseCodeVo vo = userDataStatisticsMapper.getTenantBaseCode(tenantId);
        if (vo != null) {
            String tenantLevel = vo.getTenantLevel();
            switch (tenantLevel) {
                case "0": // 国级
                    vo.setLevel("nation");
                    vo.setAdCode("100000");
                    break;
                case "1": // 省级
                    vo.setLevel("province");
                    vo.setAdCode(vo.getProvince());
                    break;
                case "2": // 市级
                    vo.setLevel("city");
                    vo.setAdCode(vo.getCity());
                    break;
                case "3": // 区级
                    vo.setLevel("district");
                    vo.setAdCode(vo.getArea());
                    break;
                case "4": // 机构级
                    vo.setLevel("town");
                    vo.setAdCode(vo.getArea());
                    break;
            }
        }
        return vo;
    }


    /**
     * 各区域人数 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public Map<String, Long> getUserNumOnLevelCode(String level, Integer levelId) {
        HashMap<String, Long> resultMap = new HashMap<>();
        if (level == null || StringUtils.isBlank(level)) {
            return resultMap;
        }
        int tenantLevel = 0;
        Integer adCode = 100000;
        List<TenantBaseCodeVo> userNumOnLevelCode = new ArrayList<>();
        Long userCount = 0L;
        switch (level) {
            case "nation": //国级
                userNumOnLevelCode.addAll(userDataStatisticsMapper.getUserNumOnLevelCode(tenantLevel, adCode));
                if (!userNumOnLevelCode.isEmpty()) {
                    userNumOnLevelCode.forEach(e-> {
                        resultMap.put(e.getProvince(), e.getUserNum());
                    });
                }

                break;
            case "province": //省级
                tenantLevel = 1;
                adCode = levelId;
                userNumOnLevelCode.addAll(userDataStatisticsMapper.getUserNumOnLevelCode(tenantLevel, adCode));
                if (!userNumOnLevelCode.isEmpty()) {
                    userNumOnLevelCode.forEach(e-> {
                        resultMap.put(e.getCity(), e.getUserNum());
                    });
                }
                break;
            case "city": //市级
                tenantLevel = 2;
                adCode = levelId;
                userNumOnLevelCode.addAll(userDataStatisticsMapper.getUserNumOnLevelCode(tenantLevel, adCode));
                if (!userNumOnLevelCode.isEmpty()) {
                    userNumOnLevelCode.forEach(e-> {
                        resultMap.put(e.getArea(), e.getUserNum());
                    });
                }
                break;
            case "area": //区级
                tenantLevel = 3;
                adCode = levelId;
                userNumOnLevelCode.addAll(userDataStatisticsMapper.getUserNumOnLevelCode(tenantLevel, adCode));
                if (!userNumOnLevelCode.isEmpty()) {
                    userNumOnLevelCode.forEach(e-> {
                        resultMap.put(e.getArea(), e.getUserNum());
                    });
                }
                break;
        }
        return resultMap;
    }

    /**
     * 区域内的机构具体信息 统计
     * @param level 层级
     * @param levelId 区域ID
     * */
    @Override
    public List<TenantsResultObjVo> getTenantsInArea(String level, Integer levelId) {
        if (level == null || levelId == null || StringUtils.isBlank(level)){
            throw new ServiceException("地区参数传递错误！");
        }
        List<TenantsResultObjVo> tenantsInArea = userDataStatisticsMapper.getTenantsInArea(levelId);
        System.out.println(tenantsInArea);
        return tenantsInArea;
    }

    /**
     * 工具函数 判断查询结果是否为NULL 单个对象
     * */
    public boolean objIsNull(Object obj) {
        return obj == null;
    }

    /**
     * 计算咨询师占比及增长率
     */
    private void calculateConsultantRatio(UserBaseDataStatisticsVo result,
                                          Map<String, Long> userCountMap,
                                          Map<String, Long> consultantCountMap) {
        Long currentUserNum = userCountMap.get("currentTotal");
        Long currentConsultantNum = consultantCountMap.get("currentTotal");
        Double currentRatio = calculateRatio(currentUserNum, currentConsultantNum);
        result.setConsultantOnPercentage(currentRatio);
        Long lastMonthUserNum = userCountMap.get("lastMonthEndTotal");
        Long lastMonthConsultantNum = consultantCountMap.get("lastMonthEndTotal");
        Double lastMonthRatio = calculateRatio(lastMonthUserNum, lastMonthConsultantNum);
        if (Objects.equals(currentConsultantNum, lastMonthConsultantNum)) {
            result.setConsultantOnPercentageGrowth(0.00);
        }else {
            result.setConsultantOnPercentageGrowth(calculateRatioGrowth(currentRatio, lastMonthRatio));
        }
    }

    /**
     * 计算比例
     */
    private Double calculateRatio(Long totalNum, Long consultantNum) {
        if (consultantNum == null || consultantNum == 0L) {
            return 0.00;
        }
        return formatDecimal(Math.max(-100.00, Math.min((double)consultantNum / totalNum * 100, 100.00)));
    }

    /**
     * 计算比例增长率
     */
    private Double calculateRatioGrowth(Double currentRatio, Double lastMonthRatio) {
        if (lastMonthRatio == null || lastMonthRatio == 0L) {
            return currentRatio > 0 ? 100.0 : 0.0;
        }
        double growth = ((currentRatio - lastMonthRatio) * 100.0) / lastMonthRatio;
        return formatDecimal(Math.max(-100, Math.min(growth, 100)));
    }

    /**
     * 计算增长率
     */
    private Double calculateGrowth(Map<String, Long> dataMap) {
        Long current = dataMap.get("currentTotal");
        Long lastMonthEnd = dataMap.get("lastMonthEndTotal");

        if (lastMonthEnd == null || lastMonthEnd == 0) {
            return current > 0 ? 100.0 : 0.0;
        }

        double growth = ((current - lastMonthEnd) * 100.0) / lastMonthEnd;
        return formatDecimal(Math.max(-100, Math.min(growth, 100)));
    }

    /**
     * 格式化小数 保留两位
     */
    private Double formatDecimal(double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
}
