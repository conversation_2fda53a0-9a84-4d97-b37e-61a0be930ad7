package org.senyor.ai.service.impl;

import java.util.List;

import lombok.RequiredArgsConstructor;
import org.senyor.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.senyor.ai.domain.AiKnowledgeBase;
import org.senyor.ai.domain.vo.AiKnowledgeBaseVo;
import org.senyor.ai.mapper.AiKnowledgeBaseMapper;
import org.senyor.ai.service.IAiKnowledgeBaseService;
import org.senyor.common.core.utils.DateUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.senyor.ai.service.IAiKnowledgeDocumentService;
import org.senyor.ai.service.IVectorStoreService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * AI知识库服务实现类
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class AiKnowledgeBaseServiceImpl extends ServiceImpl<AiKnowledgeBaseMapper, AiKnowledgeBase> implements IAiKnowledgeBaseService {

    private static final Logger log = LoggerFactory.getLogger(AiKnowledgeBaseServiceImpl.class);

    private final IAiKnowledgeDocumentService documentService;
    
    private final IVectorStoreService vectorStoreService;

    /**
     * 查询单个知识库
     *
     * @param knowledgeId 知识库ID
     * @return AI知识库对象
     */
    @Override
    public AiKnowledgeBaseVo getAiKnowledgeBaseById(Long knowledgeId) {
        return baseMapper.selectAiKnowledgeBaseById(knowledgeId);
    }

    /**
     * 根据用户ID查询知识库列表
     *
     * @param userId 用户ID
     * @return 知识库列表
     */
    @Override
    public List<AiKnowledgeBaseVo> getAiKnowledgeBasesByUserId(Long userId) {
        return baseMapper.selectAiKnowledgeBasesByUserId(userId);
    }

    /**
     * 查询所有知识库
     *
     * @param aiKnowledgeBase 查询参数
     * @return 知识库列表
     */
    @Override
    public List<AiKnowledgeBaseVo> selectAiKnowledgeBaseList(AiKnowledgeBase aiKnowledgeBase) {
        return baseMapper.selectAiKnowledgeBaseList(aiKnowledgeBase);
    }

    /**
     * 查询启用的知识库列表
     *
     * @return 启用的知识库列表
     */
    @Override
    public List<AiKnowledgeBaseVo> getEnabledKnowledgeBases() {
        return baseMapper.selectEnabledKnowledgeBases();
    }

    /**
     * 根据ID数组查询知识库列表
     *
     * @param knowledgeIds 知识库ID数组
     * @return 知识库列表
     */
    @Override
    public List<AiKnowledgeBaseVo> getAiKnowledgeBasesByIds(Long[] knowledgeIds) {
        return baseMapper.selectAiKnowledgeBasesByIds(knowledgeIds);
    }

    /**
     * 创建知识库
     *
     * @param aiKnowledgeBase 知识库对象
     * @return 知识库ID
     */
    @Override
    @Transactional
    public Long createAiKnowledgeBase(AiKnowledgeBase aiKnowledgeBase) {
        // 设置当前用户信息
        Long userId = LoginHelper.getUserId();
        String userName = LoginHelper.getUsername();
        String tenantId = LoginHelper.getTenantId();

        aiKnowledgeBase.setUserId(userId);
        aiKnowledgeBase.setUserName(userName);

        // 设置默认值
        if (aiKnowledgeBase.getStatus() == null) {
            aiKnowledgeBase.setStatus("0"); // 默认启用
        }
        if (aiKnowledgeBase.getDocumentCount() == null) {
            aiKnowledgeBase.setDocumentCount(0);
        }
        if (aiKnowledgeBase.getVectorDimension() == null) {
            aiKnowledgeBase.setVectorDimension(1536); // 默认向量维度
        }

        // 设置创建者等信息
        aiKnowledgeBase.setCreateBy(userId);
        aiKnowledgeBase.setCreateTime(DateUtils.getNowDate());

        baseMapper.insert(aiKnowledgeBase);
        return aiKnowledgeBase.getKnowledgeId();
    }

    /**
     * 更新知识库
     *
     * @param aiKnowledgeBase 知识库对象
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAiKnowledgeBase(AiKnowledgeBase aiKnowledgeBase) {
        aiKnowledgeBase.setUpdateBy(LoginHelper.getUserId());
        aiKnowledgeBase.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(aiKnowledgeBase);
    }

    /**
     * 删除知识库
     *
     * @param knowledgeId 知识库ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAiKnowledgeBaseById(Long knowledgeId) {
        try {
            // 清理Redis中的向量数据
            String indexName = "ai_knowledge_vectors";
            int deletedVectors = vectorStoreService.deleteVectorsByKnowledgeId(indexName, knowledgeId);
            log.info("已清理知识库的Redis向量数据，知识库ID: {}, 删除向量数量: {}", knowledgeId, deletedVectors);
            
            return baseMapper.deleteById(knowledgeId);
        } catch (Exception e) {
            log.error("删除知识库失败，知识库ID: {}", knowledgeId, e);
            throw new RuntimeException("删除知识库失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除知识库
     *
     * @param knowledgeIds 知识库ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAiKnowledgeBaseByIds(Long[] knowledgeIds) {
        try {
            // 清理Redis中的向量数据
            String indexName = "ai_knowledge_vectors";
            int totalDeletedVectors = 0;
            for (Long knowledgeId : knowledgeIds) {
                int deletedVectors = vectorStoreService.deleteVectorsByKnowledgeId(indexName, knowledgeId);
                totalDeletedVectors += deletedVectors;
            }
            log.info("已清理批量知识库的Redis向量数据，知识库数量: {}, 总删除向量数量: {}", knowledgeIds.length, totalDeletedVectors);
            
            return baseMapper.deleteBatchIds(java.util.Arrays.asList(knowledgeIds));
        } catch (Exception e) {
            log.error("批量删除知识库失败", e);
            throw new RuntimeException("批量删除知识库失败: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用知识库
     *
     * @param knowledgeId 知识库ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateKnowledgeBaseStatus(Long knowledgeId, String status) {
        AiKnowledgeBase knowledgeBase = new AiKnowledgeBase();
        knowledgeBase.setKnowledgeId(knowledgeId);
        knowledgeBase.setStatus(status);
        knowledgeBase.setUpdateBy(LoginHelper.getUserId());
        knowledgeBase.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(knowledgeBase);
    }

    /**
     * 更新知识库文档数量
     *
     * @param knowledgeId 知识库ID
     * @param documentCount 文档数量
     * @return 结果
     */
    @Override
    public int updateDocumentCount(Long knowledgeId, Integer documentCount) {
        AiKnowledgeBase knowledgeBase = new AiKnowledgeBase();
        knowledgeBase.setKnowledgeId(knowledgeId);
        knowledgeBase.setDocumentCount(documentCount);
        knowledgeBase.setUpdateBy(LoginHelper.getUserId());
        knowledgeBase.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(knowledgeBase);
    }
}
