package org.senyor.statistics.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 机构基础信息
 */
@Data
@NoArgsConstructor
public class TenantBaseCodeVo {

    /**
     * 机构级别
     */
    private String tenantLevel;

    /**
     * 省级 adCode
     */
    private String province;

    /**
     * 市级 adCode
     */
    private String city;

    /**
     * 区级 adCode
     */
    private String area;

    /**
     * adCode
     * */
    private String adCode;

    /**
     * 机构地区level 转译
     * */
    private String level;

    /**
     * 机构用户数量
     * */
    private Long userNum;
}
