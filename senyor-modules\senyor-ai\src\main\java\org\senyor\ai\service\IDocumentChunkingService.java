package org.senyor.ai.service;

import java.util.List;

/**
 * 文档分块服务接口
 * 用于将文档内容分割成适合向量化的小块
 *
 * <AUTHOR>
 */
public interface IDocumentChunkingService {

    /**
     * 将文档内容分割成小块
     *
     * @param content 文档内容
     * @param chunkSize 每块的大小（字符数）
     * @param overlap 块之间的重叠字符数
     * @return 分块列表
     */
    List<String> chunkContent(String content, int chunkSize, int overlap);

    /**
     * 智能分块（根据段落、句子等自然边界）
     *
     * @param content 文档内容
     * @param maxChunkSize 最大块大小
     * @return 分块列表
     */
    List<String> smartChunkContent(String content, int maxChunkSize);

    /**
     * 处理文档并创建分块
     *
     * @param documentId 文档ID
     * @param content 文档内容
     * @param knowledgeId 知识库ID
     * @return 创建的分块数量
     */
    int processDocument(Long documentId, String content, Long knowledgeId);
} 