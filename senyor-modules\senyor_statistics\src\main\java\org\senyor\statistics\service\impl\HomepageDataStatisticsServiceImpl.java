package org.senyor.statistics.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.utils.ObjectUtils;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.tenant.helper.TenantHelper;
import org.senyor.statistics.domain.vo.GradeInfoCarouselVo;
import org.senyor.statistics.domain.vo.HomepageUserDataStatisticsVo;
import org.senyor.statistics.domain.vo.UserBaseDataStatisticsVo;
import org.senyor.statistics.mapper.HomepageDataStatisticsMapper;
import org.senyor.statistics.mapper.UserDataStatisticsMapper;
import org.senyor.statistics.service.IHomepageDataStatisticsService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Service
@RequiredArgsConstructor
@Slf4j
public class HomepageDataStatisticsServiceImpl  implements IHomepageDataStatisticsService {

    private final UserDataStatisticsMapper userDataStatisticsMapper;

    private final HomepageDataStatisticsMapper homepageDataStatisticsMapper;


    @Override
    public HomepageUserDataStatisticsVo getUserBaseStats() {

        HomepageUserDataStatisticsVo result = new HomepageUserDataStatisticsVo();
        String tenantId = LoginHelper.getTenantId();
        List<String> tenantIds = new ArrayList<>();
        tenantIds.add(tenantId);

        Map<String, Long> userCountMap = userDataStatisticsMapper.getHomeUserCount();
        if ( ObjectUtils.isEmpty(userCountMap)) {
            return result;
        }
        result.setSystemUserNum(userCountMap.get("currentTotal"));
        result.setSystemUserNumGrowth(calculateGrowth(userCountMap));

        // 咨询师总数及增长率
        Map<String, Long> consultantCountMap = userDataStatisticsMapper.getHomeConsultantCount();
        if (ObjectUtils.isEmpty(consultantCountMap)) {
            return result;
        }

        result.setConsultantNum(consultantCountMap.get("currentTotal"));
        result.setConsultantNumGrowth(calculateGrowth(consultantCountMap));

        // 计算咨询师占比及增长率
        calculateConsultantRatio(result, userCountMap, consultantCountMap);

        //预警人数
        Map<String, Long> warningUserCount = homepageDataStatisticsMapper.getWarningUserCount();
        if (ObjectUtils.isEmpty(warningUserCount)) {
            return result;
        }
        result.setWarningsNum(warningUserCount.get("currentTotal"));
        result.setWarningsNumGrowth(calculateGrowth(warningUserCount));

        //新增人数
        Map<String, Long> newUserCount = homepageDataStatisticsMapper.getNewUserCount();
        if (ObjectUtils.isEmpty(newUserCount)) {
            return result;
        }
        result.setNewUsersNum(newUserCount.get("currentTotal"));
        result.setNewUsersNumGrowth(calculateGrowth(newUserCount));

        //获取档案数量
        Map<String, Long> profileCount = userDataStatisticsMapper.getHomeProfileCount();
        //计算档案占比
        calculateProfileRatio(result, userCountMap, profileCount);
        return result;
    }

    @Override
    public List<GradeInfoCarouselVo> getDeptCarouselData() {
        String tenantId = TenantHelper.getTenantId();

        // 获取部门基本信息 - 不限制数量，返回所有部门
        List<GradeInfoCarouselVo> deptList = homepageDataStatisticsMapper.getDeptBaseInfo();
        if (ObjectUtils.isEmpty(deptList)) {
            return new ArrayList<>();
        }

        String maxStudentCount = homepageDataStatisticsMapper.getMaxStudentCount(tenantId);

//        String maxStudentCount = null;

        // 提取部门ID列表
        List<Long> deptIds = deptList.stream()
            .map(GradeInfoCarouselVo::getDeptId)
            .collect(Collectors.toList());

        // 批量获取学生数量
        Map<Long, Integer> deptStudentCountMap = new HashMap<>();
        List<Map<String, Object>> studentCountList = homepageDataStatisticsMapper.batchGetDeptStudentCount(deptIds);
        if (!ObjectUtils.isEmpty(studentCountList)) {
            for (Map<String, Object> item : studentCountList) {
                Long deptId = Long.parseLong(item.get("deptId").toString());
                Integer count = Integer.parseInt(item.get("studentCount").toString());
                deptStudentCountMap.put(deptId, count);
            }
        }

        // 批量获取班级数量
        Map<Long, Integer> deptClassCountMap = new HashMap<>();
        List<Map<String, Object>> classCountList = homepageDataStatisticsMapper.batchGetDeptClassCount(deptIds);
        if (!ObjectUtils.isEmpty(classCountList)) {
            for (Map<String, Object> item : classCountList) {
                Long deptId = Long.parseLong(item.get("deptId").toString());
                Integer count = Integer.parseInt(item.get("classCount").toString());
                deptClassCountMap.put(deptId, count);
            }
        }

        // 批量获取预警人数
        Map<Long, Integer> deptWarningCountMap = new HashMap<>();
        List<Map<String, Object>> warningCountList = homepageDataStatisticsMapper.batchGetDeptWarningCount(deptIds);
        if (!ObjectUtils.isEmpty(warningCountList)) {
            for (Map<String, Object> item : warningCountList) {
                Long deptId = Long.parseLong(item.get("deptId").toString());
                Integer count = Integer.parseInt(item.get("warningCount").toString());
                deptWarningCountMap.put(deptId, count);
            }
        }

        // 批量获取心理老师数量
        Map<Long, Integer> deptTeacherCountMap = new HashMap<>();
        List<Map<String, Object>> teacherCountList = homepageDataStatisticsMapper.batchGetDeptTeacherCount(deptIds);
        if (!ObjectUtils.isEmpty(teacherCountList)) {
            for (Map<String, Object> item : teacherCountList) {
                Long deptId = Long.parseLong(item.get("deptId").toString());
                Integer count = Integer.parseInt(item.get("teacherCount").toString());
                deptTeacherCountMap.put(deptId, count);
            }
        }

        // 批量获取预警记录
        List<Map<String, Object>> allWarningRecords = homepageDataStatisticsMapper.batchGetDeptWarningRecords(deptIds);

        // 按部门ID分组预警记录
        Map<Long, List<Map<String, Object>>> deptWarningRecordsMap = new HashMap<>();
        if (!ObjectUtils.isEmpty(allWarningRecords)) {
            for (Map<String, Object> record : allWarningRecords) {
                Long deptId = Long.parseLong(record.get("deptId").toString());
                List<Map<String, Object>> records = deptWarningRecordsMap.computeIfAbsent(deptId, k -> new ArrayList<>());
                records.add(record);
            }
        }

        // 填充每个部门的详细信息
        for (GradeInfoCarouselVo dept : deptList) {
            Long deptId = dept.getDeptId();

            // 设置学生数量
            dept.setStudentCount(deptStudentCountMap.getOrDefault(deptId, 0));

            dept.setCapacity(maxStudentCount);

            // 设置班级数量
            dept.setClassCount(deptClassCountMap.getOrDefault(deptId, 0));

            // 设置预警人数
            dept.setWarningCount(deptWarningCountMap.getOrDefault(deptId, 0));

            // 设置心理老师数量
            dept.setTeacherCount(deptTeacherCountMap.getOrDefault(deptId, 0));

            // 处理预警级别分布
            List<Map<String, Object>> warningRecords = deptWarningRecordsMap.get(deptId);
            if (!ObjectUtils.isEmpty(warningRecords)) {
                // 用于存储每个用户的最高预警级别
                Map<Object, Map<String, Object>> userHighestWarning = new HashMap<>();

                // 找出每个用户的最高预警级别
                for (Map<String, Object> record : warningRecords) {
                    Object userId = record.get("user_id");
                    Integer currentLevelId = Integer.parseInt(record.get("warning_level_id").toString());

                    if (!userHighestWarning.containsKey(userId)) {
                        userHighestWarning.put(userId, record);
                    } else {
                        Map<String, Object> existingRecord = userHighestWarning.get(userId);
                        Integer existingLevelId = Integer.parseInt(existingRecord.get("warning_level_id").toString());

                        // 如果当前记录的预警级别更高，则替换
                        if (currentLevelId > existingLevelId) {
                            userHighestWarning.put(userId, record);
                        }
                    }
                }

                // 统计每个预警级别的人数
                Map<String, GradeInfoCarouselVo.WarningLevelInfo> warningLevelMap = new HashMap<>();
                for (Map<String, Object> record : userHighestWarning.values()) {
                    String levelName = (String) record.get("warning_level_name");
                    String levelColor = (String) record.get("warning_color");
                    Integer warningLevelId = Integer.parseInt(record.get("warning_level_id").toString());

                    GradeInfoCarouselVo.WarningLevelInfo levelInfo = warningLevelMap.get(levelName);
                    if (levelInfo == null) {
                        levelInfo = new GradeInfoCarouselVo.WarningLevelInfo();
                        levelInfo.setName(levelName);
                        levelInfo.setColor(levelColor);
                        levelInfo.setWarningLevelId(warningLevelId);
                        levelInfo.setCount(1);
                        warningLevelMap.put(levelName, levelInfo);
                    } else {
                        levelInfo.setCount(levelInfo.getCount() + 1);
                    }
                }

                // 转换为列表
                List<GradeInfoCarouselVo.WarningLevelInfo> warningLevels = new ArrayList<>(warningLevelMap.values());
                dept.setWarningLevels(warningLevels);
            } else {
                dept.setWarningLevels(new ArrayList<>());
            }
        }

        return deptList;
    }

    /**
     * 计算增长率
     */
    private Double calculateGrowth(Map<String, Long> dataMap) {
        Long current = dataMap.get("currentTotal");
        Long lastMonthEnd = dataMap.get("lastMonthEndTotal");

        if (lastMonthEnd == null || lastMonthEnd == 0) {
            return current > 0 ? 100.0 : 0.0;
        }

        double growth = ((current - lastMonthEnd) * 100.0) / lastMonthEnd;
        return formatDecimal(Math.max(-100, Math.min(growth, 100)));
    }

    /**
     * 格式化小数 保留两位
     */
    private Double formatDecimal(double value) {
        BigDecimal bd = new BigDecimal(value);
        bd = bd.setScale(2, RoundingMode.HALF_UP);
        return bd.doubleValue();
    }
    /**
     * 计算比例
     */
    private Double calculateRatio(Long totalNum, Long consultantNum) {
        if (consultantNum == null || consultantNum == 0L) {
            return 0.00;
        }
        return formatDecimal(Math.max(-100.00, Math.min((double)totalNum / consultantNum, 100.00)));
    }

    /**
     * 计算档案占比
     */

    private void calculateProfileRatio(HomepageUserDataStatisticsVo result,
                                          Map<String, Long> userCountMap,
                                          Map<String, Long> consultantCountMap) {
        Long currentUserNum = userCountMap.get("currentTotal");
        Long currentConsultantNum = consultantCountMap.get("currentTotal");
        Double currentRatio = calculateRatio(currentConsultantNum,currentUserNum)*100;
        result.setPsychologicalProfiles(currentRatio);
    }

    /**
     * 计算咨询师占比及增长率
     */
    private void calculateConsultantRatio(HomepageUserDataStatisticsVo result,
                                          Map<String, Long> userCountMap,
                                          Map<String, Long> consultantCountMap) {
        Long currentUserNum = userCountMap.get("currentTotal");
        Long currentConsultantNum = consultantCountMap.get("currentTotal");
        Double currentRatio = calculateRatio( currentConsultantNum,currentUserNum);
        result.setCounselorRatio(currentRatio);
        Long lastMonthUserNum = userCountMap.get("lastMonthEndTotal");
        Long lastMonthConsultantNum = consultantCountMap.get("lastMonthEndTotal");
        Double lastMonthRatio = calculateRatio(lastMonthUserNum, lastMonthConsultantNum);
        if (Objects.equals(currentConsultantNum, lastMonthConsultantNum)) {
            result.setConsultantOnPercentageGrowth(0.00);
        }else {
            result.setConsultantOnPercentageGrowth(calculateRatioGrowth(currentRatio, lastMonthRatio));
        }
    }

    /**
     * 计算比例增长率
     */
    private Double calculateRatioGrowth(Double currentRatio, Double lastMonthRatio) {
        if (lastMonthRatio == null || lastMonthRatio == 0L) {
            return currentRatio > 0 ? 100.0 : 0.0;
        }
        double growth = ((currentRatio - lastMonthRatio) * 100.0) / lastMonthRatio;
        return formatDecimal(Math.max(-100, Math.min(growth, 100)));
    }

}
