import request from '@/utils/request';

/**
 * 简单聊天接口（不保存会话）
 * 
 * @param data 请求数据
 */
export function simpleChat(data: any) {
  return request({
    url: '/ai/chat/simple',
    method: 'post',
    data: data
  });
}

/**
 * 带会话的聊天接口
 * 
 * @param data 请求数据
 */
export function conversationChat(data: any) {
  return request({
    url: '/ai/chat/conversation',
    method: 'post',
    data: data
  });
}

/**
 * 带工具的聊天接口
 * 
 * @param data 请求数据
 */
export function toolChat(data: any) {
  return request({
    url: '/ai/chat/tool',
    method: 'post',
    data: data
  });
}

/**
 * RAG增强的聊天接口
 * 
 * @param data 请求数据
 */
export function ragChat(data: any) {
  return request({
    url: '/ai/chat/rag',
    method: 'post',
    data: data
  });
}

/**
 * 获取流式聊天API的URL
 * 
 * @returns 流式聊天API的URL
 */
export function getStreamChatUrl() {
  // 确保URL格式正确，使用完整的URL路径
  return `${import.meta.env.VITE_APP_BASE_API}/ai/chat/stream`;
}
