import request from '@/utils/request'

export interface ChatRequest {
  conversationId?: string
  message: string
  systemPrompt?: string
  model?: string
  maxTokens?: number
  temperature?: Float32Array
  thinkingMode?: boolean
  enableSearch?: boolean
  knowledgeId?: number
  enableKnowledge?: boolean
}

export interface ChatResponse {
  conversationId: string
  content: string
  model: string
  isNewConversation: boolean
  reasoningContent?: string
  messageId?: number
  tokens?: number
  finishReason?: string
}

// 流式聊天
export function streamChat(data: ChatRequest) {
  return request({
    url: '/ai/chat/stream',
    method: 'post',
    data: data
  })
}

// 获取向量统计信息
export function getVectorStats() {
  return request({
    url: '/ai/chat/vector/stats',
    method: 'get'
  })
}

// 清理过期向量
export function cleanupExpiredVectors() {
  return request({
    url: '/ai/chat/vector/cleanup',
    method: 'post'
  })
}

// 获取流式聊天API的URL
export function getStreamChatUrl() {
  return `${import.meta.env.VITE_APP_BASE_API}/ai/chat/stream`
}
