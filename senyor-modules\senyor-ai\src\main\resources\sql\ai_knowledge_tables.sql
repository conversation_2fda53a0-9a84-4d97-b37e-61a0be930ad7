-- AI知识库相关表结构

-- 知识库表
CREATE TABLE IF NOT EXISTS `ai_knowledge_base` (
  `knowledge_id` bigint NOT NULL AUTO_INCREMENT COMMENT '知识库ID',
  `knowledge_name` varchar(100) NOT NULL COMMENT '知识库名称',
  `knowledge_desc` varchar(500) DEFAULT NULL COMMENT '知识库描述',
  `knowledge_type` varchar(20) DEFAULT 'document' COMMENT '知识库类型（document:文档库, qa:问答库, custom:自定义）',
  `status` char(1) DEFAULT '0' COMMENT '知识库状态（0:启用 1:禁用）',
  `document_count` int DEFAULT 0 COMMENT '文档数量',
  `vector_dimension` int DEFAULT 1536 COMMENT '向量维度',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`knowledge_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库表';

-- 知识库文档表
CREATE TABLE IF NOT EXISTS `ai_knowledge_document` (
  `document_id` bigint NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `knowledge_id` bigint NOT NULL COMMENT '知识库ID',
  `document_name` varchar(200) NOT NULL COMMENT '文档名称',
  `document_type` varchar(20) DEFAULT 'text' COMMENT '文档类型（text:文本, pdf:PDF, docx:Word, txt:纯文本）',
  `content` longtext COMMENT '文档内容',
  `file_size` bigint DEFAULT 0 COMMENT '文档大小（字节）',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `status` char(1) DEFAULT '0' COMMENT '文档状态（0:待处理 1:处理中 2:已完成 3:失败）',
  `progress` int DEFAULT 0 COMMENT '处理进度（0-100）',
  `vector_status` char(1) DEFAULT '0' COMMENT '向量化状态（0:未向量化 1:向量化中 2:已完成 3:失败）',
  `chunk_count` int DEFAULT 0 COMMENT '分块数量',
  `error_message` text COMMENT '错误信息',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`document_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_status` (`status`),
  KEY `idx_vector_status` (`vector_status`),
  CONSTRAINT `fk_document_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge_base` (`knowledge_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库文档表';

-- 知识库文档分块表
CREATE TABLE IF NOT EXISTS `ai_knowledge_chunk` (
  `chunk_id` bigint NOT NULL AUTO_INCREMENT COMMENT '分块ID',
  `document_id` bigint NOT NULL COMMENT '文档ID',
  `knowledge_id` bigint NOT NULL COMMENT '知识库ID',
  `content` text NOT NULL COMMENT '分块内容',
  `chunk_index` int NOT NULL COMMENT '分块序号',
  `chunk_size` int DEFAULT 0 COMMENT '分块大小（字符数）',
  `vector_id` varchar(100) DEFAULT NULL COMMENT '向量ID（在向量数据库中的ID）',
  `vector_status` char(1) DEFAULT '0' COMMENT '向量状态（0:未向量化 1:向量化中 2:已完成 3:失败）',
  `similarity_score` float DEFAULT NULL COMMENT '相似度分数',
  `metadata` json DEFAULT NULL COMMENT '元数据（JSON格式存储额外信息）',
  `user_id` bigint NOT NULL COMMENT '创建用户ID',
  `user_name` varchar(50) NOT NULL COMMENT '创建用户名',
  `tenant_id` varchar(20) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`chunk_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_knowledge_id` (`knowledge_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_tenant_id` (`tenant_id`),
  KEY `idx_vector_status` (`vector_status`),
  KEY `idx_vector_id` (`vector_id`),
  CONSTRAINT `fk_chunk_document` FOREIGN KEY (`document_id`) REFERENCES `ai_knowledge_document` (`document_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_chunk_knowledge` FOREIGN KEY (`knowledge_id`) REFERENCES `ai_knowledge_base` (`knowledge_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='AI知识库文档分块表';

-- 插入示例数据
INSERT INTO `ai_knowledge_base` (`knowledge_name`, `knowledge_desc`, `knowledge_type`, `status`, `document_count`, `vector_dimension`, `user_id`, `user_name`, `tenant_id`, `create_by`, `create_time`, `remark`) VALUES
('产品手册知识库', '存储公司产品相关文档和手册', 'document', '0', 0, 1536, 1, 'admin', '000000', 1, NOW(), '示例知识库'),
('常见问题知识库', '存储常见问题和解答', 'qa', '0', 0, 1536, 1, 'admin', '000000', 1, NOW(), '示例问答库'); 