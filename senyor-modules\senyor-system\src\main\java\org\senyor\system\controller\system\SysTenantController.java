package org.senyor.system.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.lock.annotation.Lock4j;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.constant.TenantConstants;
import org.senyor.common.core.domain.R;
import org.senyor.common.core.validate.AddGroup;
import org.senyor.common.core.validate.EditGroup;
import org.senyor.common.encrypt.annotation.ApiEncrypt;
import org.senyor.common.excel.utils.ExcelUtil;
import org.senyor.common.idempotent.annotation.RepeatSubmit;
import org.senyor.common.log.annotation.Log;
import org.senyor.common.log.enums.BusinessType;
import org.senyor.common.mybatis.core.page.PageQuery;
import org.senyor.common.mybatis.core.page.TableDataInfo;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.common.tenant.helper.TenantHelper;
import org.senyor.common.web.core.BaseController;
import org.senyor.system.domain.bo.SysTenantBo;
import org.senyor.system.domain.bo.SysTenantSyncBo;
import org.senyor.system.domain.bo.SysTenantSyncPackageBo;
import org.senyor.system.domain.vo.SysTenantVo;
import org.senyor.system.service.ISysTenantService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 机构管理
 *
 * <AUTHOR>
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/tenant")
public class SysTenantController extends BaseController {

    private final ISysTenantService tenantService;

    /**
     * 查询机构列表
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/pageList")
    public TableDataInfo<SysTenantVo> list(SysTenantBo bo, PageQuery pageQuery) {
        return tenantService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询机构信息列表
     */
    @SaCheckPermission("system:tenant:list")
    @GetMapping("/list")
    public R<List<SysTenantVo>> list(SysTenantBo bo) {
        List<SysTenantVo> list = tenantService.queryList(bo);
        return R.ok(list);
    }

    /**
     * 查询可合作机构列表（机构类型为0）
     */
    @GetMapping("/listCooperationTenants")
    public TableDataInfo<SysTenantVo> listCooperationTenants(SysTenantBo bo, PageQuery pageQuery) {
        bo.setOrgType(0); // 设置机构类型为0
        // 使用不受ancestors限制的查询方法
        return tenantService.queryPageListWithoutAncestors(bo, pageQuery);
    }

    /**
     * 导出机构列表
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:export")
    @Log(title = "机构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysTenantBo bo, HttpServletResponse response) {
        List<SysTenantVo> list = tenantService.queryList(bo);
        ExcelUtil.exportExcel(list, "机构", SysTenantVo.class, response);
    }

    /**
     * 获取机构详细信息
     *
     * @param id 主键
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:query")
    @GetMapping("/{id}")
    public R<SysTenantVo> getInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable Long id) {
        return R.ok(tenantService.queryById(id));
    }

    /**
     * 新增机构
     */
    @ApiEncrypt
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:add")
    @Log(title = "机构", businessType = BusinessType.INSERT)
    @Lock4j
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysTenantBo bo) {
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("新增机构'" + bo.getCompanyName() + "'失败，企业名称已存在");
        }
        return toAjax(TenantHelper.ignore(() -> tenantService.insertByBo(bo)));
    }

    /**
     * 修改机构
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysTenantBo bo) {
         tenantService.checkTenantAllowed(bo.getTenantId());
        if (!tenantService.checkCompanyNameUnique(bo)) {
            return R.fail("修改机构'" + bo.getCompanyName() + "'失败，公司名称已存在");
        }
        return toAjax(tenantService.updateByBo(bo));
    }

    /**
     * 状态修改
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysTenantBo bo) {
         tenantService.checkTenantAllowed(bo.getTenantId());
        return toAjax(tenantService.updateTenantStatus(bo));
    }

    /**
     * 删除机构
     *
     * @param ids 主键串
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:remove")
    @Log(title = "机构", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(tenantService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 动态切换机构
     *
     * @param tenantId 机构ID
     */
    @SaCheckRole(value = {
            TenantConstants.SUPER_ADMIN_ROLE_KEY,
            TenantConstants.SYS_ADMIN_ROLE_KEY,
            TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:dynamic")
    @GetMapping("/dynamic/{tenantId}")
    public R<Void> dynamicTenant(@NotBlank(message = "机构ID不能为空") @PathVariable String tenantId) {
        boolean check = checkTenantPermission(tenantId);

        if (!check) {
            return R.fail("无权限！");
        }
        TenantHelper.setDynamic(tenantId,true);
        return R.ok();
    }

    /**
     * 校验机构是否有权限切换子级机构，防止越权调用
     */
    private boolean checkTenantPermission(String tenantId) {
        String loginTenantId = LoginHelper.getTenantId();
        if (TenantConstants.DEFAULT_TENANT_ID.equals(loginTenantId)) {// 主机构不检查
            return true;
        }

        List<SysTenantVo> list = tenantService.queryList(new SysTenantBo());
        if (CollUtil.isEmpty(list)) {
            log.error("非法操作！没有子机构，tenantId：{}，loginTenantId:{}", tenantId, loginTenantId);
            return false;
        }

        Optional<SysTenantVo> first = list.stream().filter(tenant -> tenant.getTenantId().equals(loginTenantId)).findFirst();
        if (first.isEmpty()) {
            log.error("非法操作！登录的机构体系不存在该机构id，tenantId：{}，loginTenantId:{}", tenantId, loginTenantId);
            return false;
        }
        SysTenantVo sysTenantVo = first.get();
        // 机构级账号无权限操作
        if ("4".equals(sysTenantVo.getTenantLevel())) {
            return false;
        }
        return list.stream().anyMatch(tenant -> tenant.getTenantId().equals(tenantId));
    }

    /**
     * 清除动态机构
     */
    @SaCheckPermission("system:tenant:dynamic")
    @GetMapping("/dynamic/clear")
    public R<Void> dynamicClear() {
        TenantHelper.clearDynamic();
        return R.ok();
    }


    /**
     * 同步机构套餐
     *
     * @param tenantId  机构id
     * @param packageId 套餐id
     * @param functions 需要同步的功能列表
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @GetMapping("/syncTenantPackage")
    public R<Void> syncTenantPackage(@NotBlank(message = "机构ID不能为空") String tenantId,
                                     @NotNull(message = "套餐ID不能为空") Long packageId,
                                     @RequestParam(value = "functions", required = false) String[] functions) {
        log.info("同步套餐，tenantId: {}, packageId: {}, functions: {}", tenantId, packageId, functions != null ? Arrays.toString(functions) : "null");
        if (functions != null) {
            log.info("functions长度: {}", functions.length);
            for (int i = 0; i < functions.length; i++) {
                log.info("functions[{}]: {}", i, functions[i]);
            }
        }
        return toAjax(TenantHelper.ignore(() -> tenantService.syncTenantPackage(tenantId, packageId, functions)));
    }

    /**
     * 同步机构套餐（POST方法，支持更复杂的参数）
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:edit")
    @Log(title = "机构", businessType = BusinessType.UPDATE)
    @PostMapping("/syncTenantPackage")
    public R<Void> syncTenantPackagePost(@RequestBody @Validated SysTenantSyncPackageBo bo) {
        log.info("同步套餐(POST)，tenantId: {}, packageId: {}, functions: {}", 
                 bo.getTenantId(), bo.getPackageId(), bo.getFunctions());
        return toAjax(TenantHelper.ignore(() -> 
            tenantService.syncTenantPackage(bo.getTenantId(), bo.getPackageId(), 
                bo.getFunctions() != null ? bo.getFunctions().toArray(new String[0]) : null)));
    }

    /**
     * 根据tenantId查询详细信息
     * */
    @GetMapping("/getUnitInfo/{tenantId}")
    public R<SysTenantVo> getUnitInfo(@NotNull(message = "主键不能为空")
                                  @PathVariable String tenantId) {
        return R.ok(tenantService.getUnitInfo(tenantId));
    }

    /**
     * 同步租户数据
     */
    @SaCheckRole(value = {
        TenantConstants.SUPER_ADMIN_ROLE_KEY,
        TenantConstants.SYS_ADMIN_ROLE_KEY,
        TenantConstants.TENANT_ADMIN_ROLE_KEY
    }, mode = SaMode.OR)
    @SaCheckPermission("system:tenant:sync")
    @Log(title = "租户管理", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/syncData")
    public R<Void> syncTenantData(@RequestBody @Validated SysTenantSyncBo bo) {
        return toAjax(tenantService.syncTenantData(bo));
    }

}
