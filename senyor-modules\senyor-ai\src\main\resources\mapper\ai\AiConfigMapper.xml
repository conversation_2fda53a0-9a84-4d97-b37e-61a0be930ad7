<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.senyor.ai.mapper.AiConfigMapper">

    <resultMap type="org.senyor.ai.domain.AiConfig" id="AiConfigResult">
        <id     property="configId"      column="config_id"      />
        <result property="configName"    column="config_name"    />
        <result property="configDesc"    column="config_desc"    />
        <result property="model"         column="model"          />
        <result property="temperature"   column="temperature"    />
        <result property="maxTokens"     column="max_tokens"     />
        <result property="systemPrompt"  column="system_prompt"  />
        <result property="userId"        column="user_id"        />
        <result property="userName"      column="user_name"      />
        <result property="isDefault"     column="is_default"     />
        <result property="isSystem"      column="is_system"      />
        <result property="status"        column="status"         />
        <result property="createBy"      column="create_by"      />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"      column="update_by"      />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"        column="remark"         />
        <result property="knowledgeId"   column="knowledge_id"   />
    </resultMap>

    <sql id="selectAiConfigVo">
        select config_id, config_name, config_desc, model, temperature, max_tokens, system_prompt,
               user_id, user_name, is_default, is_system, status, create_by, create_time, update_by, update_time, remark, knowledge_id
        from ai_config
    </sql>

    <select id="selectAiConfigById" parameterType="Long" resultMap="AiConfigResult">
        <include refid="selectAiConfigVo"/>
        where config_id = #{configId}
    </select>

    <select id="selectAiConfigsByUserId" parameterType="Long" resultMap="AiConfigResult">
        <include refid="selectAiConfigVo"/>
        where (user_id = #{userId} or is_system = 'Y') and status = '0'
        order by is_default desc, create_time desc
    </select>

    <select id="selectAiConfigList" parameterType="org.senyor.ai.domain.AiConfig" resultMap="AiConfigResult">
        <include refid="selectAiConfigVo"/>
        <where>
            <if test="configName != null and configName != ''">
                AND config_name like concat('%', #{configName}, '%')
            </if>
            <if test="model != null and model != ''">
                AND model = #{model}
            </if>
            <if test="isDefault != null and isDefault != ''">
                AND is_default = #{isDefault}
            </if>
            <if test="isSystem != null and isSystem != ''">
                AND is_system = #{isSystem}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="userId != null">
                AND (user_id = #{userId} or is_system = 'Y')
            </if>
        </where>
        order by is_default desc, create_time desc
    </select>


    <update id="clearDefaultConfig">
        update ai_config set is_default = 'N' where is_default = 'Y'
    </update>

    <update id="setDefaultConfig" parameterType="Long">
        update ai_config set is_default = 'Y' where config_id = #{configId}
    </update>
</mapper>
