package org.senyor.ai.service;

import java.util.List;
import org.senyor.ai.domain.AiKnowledgeBase;
import org.senyor.ai.domain.bo.AiKnowledgeBaseBo;
import org.senyor.ai.domain.vo.AiKnowledgeBaseVo;

/**
 * AI知识库服务接口
 * 
 * <AUTHOR>
 */
public interface IAiKnowledgeBaseService {
    
    /**
     * 查询单个知识库
     * 
     * @param knowledgeId 知识库ID
     * @return AI知识库对象
     */
    AiKnowledgeBaseVo getAiKnowledgeBaseById(Long knowledgeId);
    
    /**
     * 根据用户ID查询知识库列表
     * 
     * @param userId 用户ID
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> getAiKnowledgeBasesByUserId(Long userId);
    
    /**
     * 查询所有知识库
     * 
     * @param aiKnowledgeBase 查询参数
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> selectAiKnowledgeBaseList(AiKnowledgeBase aiKnowledgeBase);
    
    /**
     * 查询启用的知识库列表
     * 
     * @return 启用的知识库列表
     */
    List<AiKnowledgeBaseVo> getEnabledKnowledgeBases();
    
    /**
     * 根据ID数组查询知识库列表
     * 
     * @param knowledgeIds 知识库ID数组
     * @return 知识库列表
     */
    List<AiKnowledgeBaseVo> getAiKnowledgeBasesByIds(Long[] knowledgeIds);
    
    /**
     * 创建知识库
     * 
     * @param aiKnowledgeBase 知识库对象
     * @return 知识库ID
     */
    Long createAiKnowledgeBase(AiKnowledgeBase aiKnowledgeBase);
    
    /**
     * 更新知识库
     * 
     * @param aiKnowledgeBase 知识库对象
     * @return 结果
     */
    int updateAiKnowledgeBase(AiKnowledgeBase aiKnowledgeBase);
    
    /**
     * 删除知识库
     * 
     * @param knowledgeId 知识库ID
     * @return 结果
     */
    int deleteAiKnowledgeBaseById(Long knowledgeId);
    
    /**
     * 批量删除知识库
     * 
     * @param knowledgeIds 知识库ID数组
     * @return 结果
     */
    int deleteAiKnowledgeBaseByIds(Long[] knowledgeIds);
    
    /**
     * 启用/禁用知识库
     * 
     * @param knowledgeId 知识库ID
     * @param status 状态
     * @return 结果
     */
    int updateKnowledgeBaseStatus(Long knowledgeId, String status);
    
    /**
     * 更新知识库文档数量
     * 
     * @param knowledgeId 知识库ID
     * @param documentCount 文档数量
     * @return 结果
     */
    int updateDocumentCount(Long knowledgeId, Integer documentCount);
} 