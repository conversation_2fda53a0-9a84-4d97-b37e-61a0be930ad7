package org.senyor.ai.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.senyor.common.mybatis.core.domain.BaseEntity;

/**
 * AI知识库业务对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "AI知识库业务对象")
public class AiKnowledgeBaseBo extends BaseEntity {

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID")
    private Long knowledgeId;

    /**
     * 知识库名称
     */
    @NotBlank(message = "知识库名称不能为空")
    @Size(max = 100, message = "知识库名称长度不能超过100字符")
    @Schema(description = "知识库名称", required = true)
    private String knowledgeName;

    /**
     * 知识库描述
     */
    @Size(max = 500, message = "知识库描述长度不能超过500字符")
    @Schema(description = "知识库描述")
    private String knowledgeDesc;

    /**
     * 知识库类型（document:文档库, qa:问答库, custom:自定义）
     */
    @Schema(description = "知识库类型")
    private String knowledgeType;

    /**
     * 知识库状态（0:启用 1:禁用）
     */
    @Schema(description = "知识库状态")
    private String status;

    /**
     * 文档数量
     */
    @Schema(description = "文档数量")
    private Integer documentCount;

    /**
     * 向量维度
     */
    @Schema(description = "向量维度")
    private Integer vectorDimension;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建用户ID")
    private Long userId;

    /**
     * 创建用户名
     */
    @Schema(description = "创建用户名")
    private String userName;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}
