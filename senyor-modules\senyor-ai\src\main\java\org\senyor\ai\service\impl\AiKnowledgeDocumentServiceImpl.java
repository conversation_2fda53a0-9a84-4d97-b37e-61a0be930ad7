package org.senyor.ai.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.senyor.ai.domain.AiKnowledgeDocument;
import org.senyor.ai.domain.vo.AiKnowledgeDocumentVo;
import org.senyor.ai.mapper.AiKnowledgeDocumentMapper;
import org.senyor.ai.service.IAiKnowledgeDocumentService;
import org.senyor.ai.service.IDocumentChunkingService;
import org.senyor.ai.service.IVectorStoreService;
import org.senyor.common.core.utils.DateUtils;
import org.senyor.common.core.utils.StringUtils;
import org.senyor.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * AI知识库文档服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AiKnowledgeDocumentServiceImpl extends ServiceImpl<AiKnowledgeDocumentMapper, AiKnowledgeDocument> implements IAiKnowledgeDocumentService {

    private final IDocumentChunkingService documentChunkingService;
    private final IVectorStoreService vectorStoreService;

    /**
     * 查询单个文档
     *
     * @param documentId 文档ID
     * @return AI文档对象
     */
    @Override
    public AiKnowledgeDocumentVo getAiKnowledgeDocumentById(Long documentId) {
        return baseMapper.selectAiKnowledgeDocumentById(documentId);
    }

    /**
     * 根据知识库ID查询文档列表
     *
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    @Override
    public List<AiKnowledgeDocumentVo> getAiKnowledgeDocumentsByKnowledgeId(Long knowledgeId) {
        return baseMapper.selectAiKnowledgeDocumentsByKnowledgeId(knowledgeId);
    }

    /**
     * 查询所有文档
     *
     * @param aiKnowledgeDocument 查询参数
     * @return 文档列表
     */
    @Override
    public List<AiKnowledgeDocumentVo> selectAiKnowledgeDocumentList(AiKnowledgeDocument aiKnowledgeDocument) {
        return baseMapper.selectAiKnowledgeDocumentList(aiKnowledgeDocument);
    }

    /**
     * 根据知识库ID统计文档数量
     *
     * @param knowledgeId 知识库ID
     * @return 文档数量
     */
    @Override
    public int countDocumentsByKnowledgeId(Long knowledgeId) {
        return baseMapper.countDocumentsByKnowledgeId(knowledgeId);
    }

    /**
     * 创建文档
     *
     * @param aiKnowledgeDocument 文档对象
     * @return 文档ID
     */
    @Override
    @Transactional
    public Long createAiKnowledgeDocument(AiKnowledgeDocument aiKnowledgeDocument) {
        // 设置当前用户信息
        Long userId = LoginHelper.getUserId();
        String userName = LoginHelper.getUsername();
        String tenantId = LoginHelper.getTenantId();

        aiKnowledgeDocument.setUserId(userId);
        aiKnowledgeDocument.setUserName(userName);

        // 设置默认值
        if (aiKnowledgeDocument.getStatus() == null) {
            aiKnowledgeDocument.setStatus("0"); // 默认待处理
        }
        if (aiKnowledgeDocument.getProgress() == null) {
            aiKnowledgeDocument.setProgress(0);
        }
        if (aiKnowledgeDocument.getVectorStatus() == null) {
            aiKnowledgeDocument.setVectorStatus("0"); // 默认未向量化
        }
        if (aiKnowledgeDocument.getChunkCount() == null) {
            aiKnowledgeDocument.setChunkCount(0);
        }

        // 设置创建者等信息
        aiKnowledgeDocument.setCreateBy(userId);
        aiKnowledgeDocument.setCreateTime(DateUtils.getNowDate());

        baseMapper.insert(aiKnowledgeDocument);
        return aiKnowledgeDocument.getDocumentId();
    }

    /**
     * 更新文档
     *
     * @param aiKnowledgeDocument 文档对象
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAiKnowledgeDocument(AiKnowledgeDocument aiKnowledgeDocument) {
        aiKnowledgeDocument.setUpdateBy(LoginHelper.getUserId());
        aiKnowledgeDocument.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(aiKnowledgeDocument);
    }

    /**
     * 删除文档
     *
     * @param documentId 文档ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAiKnowledgeDocumentById(Long documentId) {
        try {
            // 获取文档信息
            AiKnowledgeDocument document = baseMapper.selectById(documentId);
            if (document != null) {
                // 清理Redis中的向量数据
                String indexName = "ai_knowledge_vectors";
                vectorStoreService.deleteVectorsByDocumentId(indexName, documentId);
                log.info("已清理文档的Redis向量数据，文档ID: {}", documentId);
            }

            return baseMapper.deleteById(documentId);
        } catch (Exception e) {
            log.error("删除文档失败，文档ID: {}", documentId, e);
            throw new RuntimeException("删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 批量删除文档
     *
     * @param documentIds 文档ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAiKnowledgeDocumentByIds(Long[] documentIds) {
        try {
            // 清理Redis中的向量数据
            String indexName = "ai_knowledge_vectors";
            for (Long documentId : documentIds) {
                vectorStoreService.deleteVectorsByDocumentId(indexName, documentId);
            }
            log.info("已清理批量文档的Redis向量数据，文档数量: {}", documentIds.length);

            return baseMapper.deleteBatchIds(java.util.Arrays.asList(documentIds));
        } catch (Exception e) {
            log.error("批量删除文档失败", e);
            throw new RuntimeException("批量删除文档失败: " + e.getMessage());
        }
    }

    /**
     * 上传文档
     *
     * @param knowledgeId 知识库ID
     * @param file 文件
     * @return 文档ID
     */
    @Override
    @Transactional
    public Long uploadDocument(Long knowledgeId, MultipartFile file) {
        try {
            AiKnowledgeDocument document = new AiKnowledgeDocument();
            document.setKnowledgeId(knowledgeId);

            // 修复文档名称：只取文件名，不包含路径
            String originalFilename = file.getOriginalFilename();
            String documentName = originalFilename;
            if (originalFilename != null && originalFilename.contains("/")) {
                documentName = originalFilename.substring(originalFilename.lastIndexOf("/") + 1);
            } else if (originalFilename != null && originalFilename.contains("\\")) {
                documentName = originalFilename.substring(originalFilename.lastIndexOf("\\") + 1);
            }
            document.setDocumentName(documentName);

            document.setDocumentType(getDocumentType(documentName));
            document.setFileSize(file.getSize());
            document.setContent(extractContent(file));
            document.setStatus("0"); // 待处理
            document.setProgress(0);
            document.setVectorStatus("0"); // 未向量化
            document.setChunkCount(0);

            return createAiKnowledgeDocument(document);
        } catch (Exception e) {
            log.error("上传文档失败", e);
            throw new RuntimeException("上传文档失败: " + e.getMessage());
        }
    }

    /**
     * 更新文档状态
     *
     * @param documentId 文档ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public int updateDocumentStatus(Long documentId, String status) {
        AiKnowledgeDocument document = new AiKnowledgeDocument();
        document.setDocumentId(documentId);
        document.setStatus(status);
        document.setUpdateBy(LoginHelper.getUserId());
        document.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(document);
    }

    /**
     * 更新文档处理进度
     *
     * @param documentId 文档ID
     * @param progress 进度
     * @return 结果
     */
    @Override
    public int updateDocumentProgress(Long documentId, Integer progress) {
        AiKnowledgeDocument document = new AiKnowledgeDocument();
        document.setDocumentId(documentId);
        document.setProgress(progress);
        document.setUpdateBy(LoginHelper.getUserId());
        document.setUpdateTime(DateUtils.getNowDate());
        return baseMapper.updateById(document);
    }

    /**
     * 处理文档（解析、分块，不进行向量化）
     *
     * @param documentId 文档ID
     * @return 结果
     */
    @Override
    public boolean processDocument(Long documentId) {
        try {
            // 更新状态为处理中
            updateDocumentStatus(documentId, "1");
            updateDocumentProgress(documentId, 10);

            // 获取文档信息
            AiKnowledgeDocument document = baseMapper.selectById(documentId);
            if (document == null) {
                throw new RuntimeException("文档不存在");
            }

            // 分块处理
            updateDocumentProgress(documentId, 50);
            int chunkCount = documentChunkingService.processDocument(documentId, document.getContent(), document.getKnowledgeId());

            // 更新分块数量
            document.setChunkCount(chunkCount);
            baseMapper.updateById(document);

            updateDocumentProgress(documentId, 100);
            updateDocumentStatus(documentId, "2"); // 已完成（仅分块，未向量化）

            log.info("文档处理完成（仅分块），文档ID: {}, 分块数量: {}", documentId, chunkCount);
            return true;
        } catch (Exception e) {
            log.error("处理文档失败", e);
            updateDocumentStatus(documentId, "3");
            // 更新错误信息
            AiKnowledgeDocument document = new AiKnowledgeDocument();
            document.setDocumentId(documentId);
            document.setErrorMessage(e.getMessage());
            document.setUpdateBy(LoginHelper.getUserId());
            document.setUpdateTime(DateUtils.getNowDate());
            baseMapper.updateById(document);
            return false;
        }
    }

    /**
     * 处理向量化
     *
     * @param documentId 文档ID
     * @param knowledgeId 知识库ID
     * @return 是否成功
     */
    private boolean processVectorization(Long documentId, Long knowledgeId) {
        try {
            // 创建向量索引名称
            String indexName = "ai_knowledge_vectors";

            // 确保向量索引存在
            vectorStoreService.createIndex(indexName);

            log.info("向量化处理已完成，文档ID: {}, 知识库ID: {}", documentId, knowledgeId);
            return true;
        } catch (Exception e) {
            log.error("向量化处理失败，文档ID: {}, 知识库ID: {}", documentId, knowledgeId, e);
            return false;
        }
    }

    /**
     * 重新处理文档
     *
     * @param documentId 文档ID
     * @return 结果
     */
    @Override
    public boolean reprocessDocument(Long documentId) {
        // 清除错误信息
        AiKnowledgeDocument document = new AiKnowledgeDocument();
        document.setDocumentId(documentId);
        document.setErrorMessage(null);
        document.setUpdateBy(LoginHelper.getUserId());
        document.setUpdateTime(DateUtils.getNowDate());
        baseMapper.updateById(document);

        return processDocument(documentId);
    }

    /**
     * 获取文档类型
     */
    private String getDocumentType(String fileName) {
        if (StringUtils.isBlank(fileName)) {
            return "text";
        }
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "pdf":
                return "pdf";
            case "docx":
            case "doc":
                return "docx";
            case "txt":
                return "txt";
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            default:
                return "text";
        }
    }

    /**
     * 提取文件内容
     *
     * @param file 文件
     * @return 文件内容
     */
    @Override
    public String extractContent(MultipartFile file) {
        try {
            String fileName = file.getOriginalFilename();
            if (fileName == null) {
                throw new RuntimeException("文件名不能为空");
            }

            String fileExtension = getDocumentType(fileName);
            switch (fileExtension) {
                case "pdf":
                    return extractPdfContent(file);
                case "docx":
                    return extractDocxContent(file);
                case "txt":
                case "text":
                    return extractTextContent(file);
                case "image":
                    // 图片类型，返回图片描述或路径信息
                    return "图片文件：" + fileName;
                default:
                    return extractTextContent(file);
            }
        } catch (IOException e) {
            log.error("提取文件内容失败: {}", e.getMessage(), e);
            throw new RuntimeException("文件内容提取失败: " + e.getMessage(), e);
        }
    }

    /**
     * 提取PDF内容
     */
    private String extractPdfContent(MultipartFile file) throws IOException {
        try (PDDocument document = PDDocument.load(file.getInputStream())) {
            PDFTextStripper stripper = new PDFTextStripper();
            return stripper.getText(document);
        }
    }

    /**
     * 提取Word文档内容
     */
    private String extractDocxContent(MultipartFile file) throws IOException {
        try (XWPFDocument document = new XWPFDocument(file.getInputStream())) {
            StringBuilder content = new StringBuilder();
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                content.append(paragraph.getText()).append("\n");
            }
            return content.toString();
        }
    }

    /**
     * 提取文本内容
     */
    private String extractTextContent(MultipartFile file) throws IOException {
        return new String(file.getBytes(), StandardCharsets.UTF_8);
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes == 0) return "0 B";
        int k = 1024;
        String[] sizes = {"B", "KB", "MB", "GB"};
        int i = (int) Math.floor(Math.log(bytes) / Math.log(k));
        return String.format("%.2f %s", bytes / Math.pow(k, i), sizes[i]);
    }
}
