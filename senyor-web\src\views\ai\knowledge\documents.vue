<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>知识库：{{ knowledgeInfo.knowledgeName }}</span>
          <el-button type="primary" @click="handleUpload" v-hasPermi="['ai:knowledge:add']">
            <el-icon><Upload /></el-icon>上传文档
          </el-button>
        </div>
      </template>
      
      <el-descriptions :column="3" border>
        <el-descriptions-item label="知识库类型">
          <dict-tag :options="knowledgeTypeOptions" :value="knowledgeInfo.knowledgeType" />
        </el-descriptions-item>
        <el-descriptions-item label="文档数量">{{ knowledgeInfo.documentCount || 0 }}</el-descriptions-item>
        <el-descriptions-item label="向量维度">{{ knowledgeInfo.vectorDimension }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ knowledgeInfo.userName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(knowledgeInfo.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="statusOptions" :value="knowledgeInfo.status" />
        </el-descriptions-item>
        <el-descriptions-item label="向量统计">
          <el-button type="text" @click="showVectorStats">
            <el-icon><DataAnalysis /></el-icon>查看统计
          </el-button>
        </el-descriptions-item>
        <el-descriptions-item label="批量操作">
          <el-button type="text" @click="showBatchVectorizeDialog">
            <el-icon><Cpu /></el-icon>批量向量化
          </el-button>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <el-card class="box-card" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span>文档列表</span>
        </div>
      </template>

      <el-table v-loading="loading" :data="documentList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" />
        <el-table-column label="文档名称" prop="documentName" :show-overflow-tooltip="true" />
        <el-table-column label="文档类型" align="center" prop="documentType" width="100">
          <template #default="scope">
            <dict-tag :options="documentTypeOptions" :value="scope.row.documentType" />
          </template>
        </el-table-column>
        <el-table-column label="文件大小" align="center" prop="fileSize" width="120">
          <template #default="scope">
            {{ formatFileSize(scope.row.fileSize) }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" prop="status" width="100">
          <template #default="scope">
            <dict-tag :options="documentStatusOptions" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="处理进度" align="center" prop="progress" width="120">
          <template #default="scope">
            <el-progress :percentage="scope.row.progress || 0" :status="getProgressStatus(scope.row.status)" />
          </template>
        </el-table-column>
        <el-table-column label="向量化状态" align="center" prop="vectorStatus" width="120">
          <template #default="scope">
            <dict-tag :options="vectorStatusOptions" :value="scope.row.vectorStatus" />
          </template>
        </el-table-column>
        <el-table-column label="分块数量" align="center" prop="chunkCount" width="100" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="280">
          <template #default="scope">
            <el-button
              type="text"
              icon="View"
              @click="handleView(scope.row)"
              v-hasPermi="['ai:knowledge:query']"
            >查看</el-button>
            <el-button
              type="text"
              icon="Refresh"
              @click="handleReprocess(scope.row)"
              v-hasPermi="['ai:knowledge:edit']"
              :disabled="scope.row.status === '1'"
            >重新处理</el-button>
            <el-button
              type="text"
              icon="Cpu"
              @click="handleVectorize(scope.row)"
              v-hasPermi="['ai:knowledge:edit']"
              :disabled="scope.row.vectorStatus === '1' || scope.row.status !== '2'"
            >向量化</el-button>
            <el-button
              type="text"
              icon="Refresh"
              @click="handleReVectorize(scope.row)"
              v-hasPermi="['ai:knowledge:edit']"
              :disabled="scope.row.vectorStatus === '1'"
            >重新向量化</el-button>
            <el-button
              type="text"
              icon="Delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['ai:knowledge:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 上传文档对话框 -->
    <el-dialog title="上传文档" v-model="uploadOpen" width="500px" append-to-body>
      <el-form ref="uploadFormRef" :model="uploadForm" :rules="uploadRules" label-width="80px">
        <el-form-item label="文档名称" prop="documentName">
          <el-input v-model="uploadForm.documentName" placeholder="请输入文档名称" />
        </el-form-item>
        <el-form-item label="文档类型" prop="documentType">
          <el-select v-model="uploadForm.documentType" placeholder="请选择文档类型">
            <el-option label="文本" value="text" />
            <el-option label="PDF" value="pdf" />
            <el-option label="Word" value="docx" />
            <el-option label="纯文本" value="txt" />
            <el-option label="图片" value="image" />
          </el-select>
        </el-form-item>
        <el-form-item label="文档内容" prop="content" v-if="uploadForm.documentType === 'text'">
          <el-input v-model="uploadForm.content" type="textarea" :rows="10" placeholder="请输入文档内容" />
        </el-form-item>
        <el-form-item label="上传文件" prop="file" v-else>
          <el-upload
            ref="fileUploadRef"
            :limit="1"
            :accept="uploadForm.documentType === 'image' ? '.jpg,.jpeg,.png,.gif,.bmp,.webp' : '.pdf,.docx,.txt'"
            :auto-upload="false"
            :on-change="handleFileChange"
            :file-list="fileList"
          >
            <el-button type="primary">选择文件</el-button>
            <template #tip>
              <div class="el-upload__tip">
                <span v-if="uploadForm.documentType === 'image'">
                  支持 JPG、PNG、GIF、BMP、WebP 格式图片，文件大小不超过 10MB
                </span>
                <span v-else>
                  支持 PDF、Word、TXT 格式文件，文件大小不超过 10MB
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="uploadForm.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUpload">确 定</el-button>
          <el-button @click="cancelUpload">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看文档对话框 -->
    <el-dialog title="文档详情" v-model="viewOpen" width="800px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="文档名称">{{ viewForm.documentName }}</el-descriptions-item>
        <el-descriptions-item label="文档类型">
          <dict-tag :options="documentTypeOptions" :value="viewForm.documentType" />
        </el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(viewForm.fileSize) }}</el-descriptions-item>
        <el-descriptions-item label="分块数量">{{ viewForm.chunkCount }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <dict-tag :options="documentStatusOptions" :value="viewForm.status" />
        </el-descriptions-item>
        <el-descriptions-item label="向量化状态">
          <dict-tag :options="vectorStatusOptions" :value="viewForm.vectorStatus" />
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ parseTime(viewForm.createTime) }}</el-descriptions-item>
        <el-descriptions-item label="创建者">{{ viewForm.userName }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>文档内容：</h4>
        <!-- 图片预览 -->
        <div v-if="viewForm.documentType === 'image' && viewForm.filePath" style="margin-bottom: 20px;">
          <el-image
            :src="viewForm.filePath"
            :preview-src-list="[viewForm.filePath]"
            fit="contain"
            style="max-width: 100%; max-height: 400px;"
            :preview-teleported="true"
          >
            <template #error>
              <div class="image-slot">
                <el-icon><Picture /></el-icon>
                <span>图片加载失败</span>
              </div>
            </template>
          </el-image>
        </div>
        <!-- 文本内容 -->
        <el-input
          v-model="viewForm.content"
          type="textarea"
          :rows="15"
          readonly
          placeholder="暂无内容"
        />
      </div>
      
      <div v-if="viewForm.errorMessage" style="margin-top: 20px;">
        <h4>错误信息：</h4>
        <el-alert :title="viewForm.errorMessage" type="error" show-icon />
      </div>
    </el-dialog>

    <!-- 批量向量化对话框 -->
    <el-dialog title="批量向量化" v-model="batchVectorizeOpen" width="600px" append-to-body>
      <div style="margin-bottom: 20px;">
        <el-alert
          title="批量向量化说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>• 将选中的文档进行批量向量化处理</p>
          <p>• 只有已完成处理的文档才能进行向量化</p>
          <p>• 向量化过程为异步处理，请稍后查看状态</p>
        </el-alert>
      </div>
      
      <el-table :data="selectedDocuments" style="width: 100%">
        <el-table-column label="文档名称" prop="documentName" />
        <el-table-column label="文档类型" align="center" width="100">
          <template #default="scope">
            <dict-tag :options="documentTypeOptions" :value="scope.row.documentType" />
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <dict-tag :options="documentStatusOptions" :value="scope.row.status" />
          </template>
        </el-table-column>
        <el-table-column label="向量化状态" align="center" width="120">
          <template #default="scope">
            <dict-tag :options="vectorStatusOptions" :value="scope.row.vectorStatus" />
          </template>
        </el-table-column>
      </el-table>
      
      <div style="margin-top: 20px; color: #909399; font-size: 14px;">
        共选中 {{ selectedDocuments.length }} 个文档
      </div>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchVectorize" :loading="batchVectorizeLoading">
            开始批量向量化
          </el-button>
          <el-button @click="batchVectorizeOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="KnowledgeDocuments">
import { getKnowledge } from "@/api/ai/knowledge";
import { listDocuments, getDocument, delDocument, uploadDocument, reprocessDocument, vectorizeDocument, batchVectorizeDocuments, reVectorizeDocument } from "@/api/ai/documents";
import { getVectorStats, cleanupExpiredVectors } from "@/api/ai/chat";
import { DataAnalysis, Cpu, Delete, Refresh } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();
const route = useRoute();

const knowledgeId = Number(route.params.knowledgeId);
const knowledgeInfo = ref({});
const documentList = ref([]);
const loading = ref(true);
const uploadOpen = ref(false);
const viewOpen = ref(false);
const fileList = ref([]);
const vectorStatsOpen = ref(false);
const vectorizeOpen = ref(false);
const vectorizeLoading = ref(false);
const cleanupLoading = ref(false);
const vectorStats = ref({});
const vectorizeForm = ref({});
const batchVectorizeOpen = ref(false);
const batchVectorizeLoading = ref(false);
const selectedDocuments = ref([]);

const knowledgeTypeOptions = ref([
  { label: "文档库", value: "document" },
  { label: "问答库", value: "qa" },
  { label: "自定义", value: "custom" }
]);

const statusOptions = ref([
  { label: "启用", value: "0" },
  { label: "禁用", value: "1" }
]);

const documentTypeOptions = ref([
  { label: "文本", value: "text" },
  { label: "PDF", value: "pdf" },
  { label: "Word", value: "docx" },
  { label: "纯文本", value: "txt" },
  { label: "图片", value: "image" }
]);

const documentStatusOptions = ref([
  { label: "待处理", value: "0" },
  { label: "处理中", value: "1" },
  { label: "已完成", value: "2" },
  { label: "失败", value: "3" }
]);

const vectorStatusOptions = ref([
  { label: "未向量化", value: "0" },
  { label: "向量化中", value: "1" },
  { label: "已完成", value: "2" },
  { label: "失败", value: "3" }
]);

const data = reactive({
  uploadForm: {
    documentName: null,
    documentType: "text",
    content: null,
    remark: null
  },
  viewForm: {},
  uploadRules: {
    documentName: [
      { required: true, message: "文档名称不能为空", trigger: "blur" }
    ],
    documentType: [
      { required: true, message: "文档类型不能为空", trigger: "change" }
    ]
  }
});

const { uploadForm, viewForm, uploadRules } = toRefs(data);

/** 获取知识库信息 */
function getKnowledgeInfo() {
  getKnowledge(knowledgeId).then(response => {
    knowledgeInfo.value = response.data || response;
  }).catch(() => {
    knowledgeInfo.value = {};
  });
}

/** 获取文档列表 */
function getDocumentList() {
  loading.value = true;
  listDocuments({ knowledgeId: knowledgeId }).then(response => {
    documentList.value = response.rows || response.data || [];
    loading.value = false;
  }).catch(() => {
    loading.value = false;
  });
}

/** 格式化文件大小 */
function formatFileSize(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/** 获取进度状态 */
function getProgressStatus(status) {
  switch (status) {
    case '0': return '';
    case '1': return 'warning';
    case '2': return 'success';
    case '3': return 'exception';
    default: return '';
  }
}

/** 上传文档 */
function handleUpload() {
  uploadForm.value = {
    documentName: null,
    documentType: "text",
    content: null,
    remark: null
  };
  fileList.value = [];
  uploadOpen.value = true;
}

/** 文件选择事件 */
function handleFileChange(file) {
  fileList.value = [file];
}

/** 提交上传 */
function submitUpload() {
  proxy.$refs["uploadFormRef"].validate(valid => {
    if (valid) {
      const formData = new FormData();
      formData.append('knowledgeId', knowledgeId);
      formData.append('documentName', uploadForm.value.documentName);
      formData.append('documentType', uploadForm.value.documentType);
      formData.append('remark', uploadForm.value.remark);
      
      if (uploadForm.value.documentType === 'text') {
        formData.append('content', uploadForm.value.content);
      } else if (fileList.value.length > 0) {
        formData.append('file', fileList.value[0].raw);
      }
      
      uploadDocument(formData).then(response => {
        proxy.$modal.msgSuccess("上传成功");
        uploadOpen.value = false;
        getDocumentList();
        getKnowledgeInfo();
      });
    }
  });
}

/** 取消上传 */
function cancelUpload() {
  uploadOpen.value = false;
  proxy.$refs["uploadFormRef"]?.resetFields();
}

/** 查看文档 */
function handleView(row) {
  getDocument(row.documentId).then(response => {
    viewForm.value = response.data;
    viewOpen.value = true;
  });
}

/** 重新处理文档 */
function handleReprocess(row) {
  proxy.$modal.confirm('是否确认重新处理文档"' + row.documentName + '"？').then(function() {
    return reprocessDocument(row.documentId);
  }).then(() => {
    proxy.$modal.msgSuccess("重新处理成功");
    getDocumentList();
  }).catch(() => {});
}

/** 删除文档 */
function handleDelete(row) {
  proxy.$modal.confirm('是否确认删除文档"' + row.documentName + '"？').then(function() {
    return delDocument(row.documentId);
  }).then(() => {
    getDocumentList();
    getKnowledgeInfo();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => {});
}

/** 显示向量统计 */
function showVectorStats() {
  vectorStatsOpen.value = true;
  refreshVectorStats();
}

/** 刷新向量统计 */
function refreshVectorStats() {
  getVectorStats().then(response => {
    if (response.code === 200) {
      vectorStats.value = response.data || {};
    }
  }).catch(() => {
    proxy.$modal.msgError("获取向量统计信息失败");
  });
}

/** 清理过期向量 */
function handleCleanupVectors() {
  cleanupLoading.value = true;
  cleanupExpiredVectors().then(response => {
    if (response.code === 200) {
      proxy.$modal.msgSuccess(`清理完成，共清理 ${response.data} 个过期向量`);
      refreshVectorStats();
    }
  }).catch(() => {
    proxy.$modal.msgError("清理过期向量失败");
  }).finally(() => {
    cleanupLoading.value = false;
  });
}

/** 向量化文档 */
function handleVectorize(row) {
  vectorizeForm.value = {
    documentId: row.documentId,
    documentName: row.documentName,
    documentType: row.documentType,
    chunkCount: row.chunkCount
  };
  vectorizeOpen.value = true;
}

/** 提交向量化 */
function submitVectorize() {
  vectorizeLoading.value = true;
  vectorizeDocument(vectorizeForm.value.documentId).then(response => {
    if (response.code === 200) {
      proxy.$modal.msgSuccess("向量化任务已启动，请稍后查看状态");
      vectorizeOpen.value = false;
      getDocumentList(); // 刷新文档列表
    } else {
      proxy.$modal.msgError(response.msg || "向量化任务启动失败");
    }
  }).catch(() => {
    proxy.$modal.msgError("向量化任务启动失败");
  }).finally(() => {
    vectorizeLoading.value = false;
  });
}

/** 获取文档类型标签 */
function getDocumentTypeLabel(type) {
  const option = documentTypeOptions.value.find(item => item.value === type);
  return option ? option.label : type;
}

/** 处理表格选择变化 */
function handleSelectionChange(selection) {
  selectedDocuments.value = selection;
}

/** 显示批量向量化对话框 */
function showBatchVectorizeDialog() {
  if (selectedDocuments.value.length === 0) {
    proxy.$modal.msgWarning("请先选择要向量化的文档");
    return;
  }
  
  // 过滤出可以向量化的文档
  const vectorizableDocs = selectedDocuments.value.filter(doc => 
    doc.status === '2' && doc.vectorStatus !== '1'
  );
  
  if (vectorizableDocs.length === 0) {
    proxy.$modal.msgWarning("选中的文档中没有可以向量化的文档");
    return;
  }
  
  selectedDocuments.value = vectorizableDocs;
  batchVectorizeOpen.value = true;
}

/** 提交批量向量化 */
function submitBatchVectorize() {
  const documentIds = selectedDocuments.value.map(doc => doc.documentId);
  
  batchVectorizeLoading.value = true;
  batchVectorizeDocuments(documentIds).then(response => {
    if (response.code === 200) {
      proxy.$modal.msgSuccess(`批量向量化任务已启动，共 ${response.data.successCount} 个文档`);
      batchVectorizeOpen.value = false;
      getDocumentList(); // 刷新文档列表
    } else {
      proxy.$modal.msgError(response.msg || "批量向量化任务启动失败");
    }
  }).catch(() => {
    proxy.$modal.msgError("批量向量化任务启动失败");
  }).finally(() => {
    batchVectorizeLoading.value = false;
  });
}

/** 重新向量化文档 */
function handleReVectorize(row) {
  proxy.$modal.confirm('是否确认重新向量化文档"' + row.documentName + '"？').then(function() {
    return reVectorizeDocument(row.documentId);
  }).then(response => {
    if (response.code === 200) {
      proxy.$modal.msgSuccess("重新向量化任务已启动");
      getDocumentList();
    } else {
      proxy.$modal.msgError(response.msg || "重新向量化任务启动失败");
    }
  }).catch(() => {});
}

getKnowledgeInfo();
getDocumentList();
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.box-card {
  margin-bottom: 20px;
}

.image-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 200px;
  background: #f5f7fa;
  color: #909399;
  font-size: 14px;
}

.image-slot .el-icon {
  font-size: 30px;
  margin-bottom: 10px;
}
</style> 