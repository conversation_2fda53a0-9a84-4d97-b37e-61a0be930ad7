package org.senyor.system.domain.vo;

import java.util.Date;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.senyor.system.domain.SysFileDownload;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;



/**
 * 文件下载视图对象 sys_file_download
 *
 * <AUTHOR>
 * @date 2025-06-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = org.senyor.system.domain.SysFileDownload.class)
public class SysFileDownloadVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 文件名
     */
    @ExcelProperty(value = "文件名")
    private String fileName;

    /**
     * 文件下载URL
     */
    @ExcelProperty(value = "文件下载URL")
    private String url;

    /**
     * 文件类型
     */
    @ExcelProperty(value = "文件类型")
    private String fileType;

    /**
     * 文件大小
     */
    @ExcelProperty(value = "文件大小")
    private Long fileSize;

    /**
     * 下载时间
     */
    @ExcelProperty(value = "下载时间")
    private Date createTime;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    private Integer status;

    /**
     * 失败原因
     */
    @ExcelProperty(value = "失败原因")
    private String failReason;

    /**
     * 下载类型
     */
    @ExcelProperty(value = "下载类型")
    private Long downloadType;



}
