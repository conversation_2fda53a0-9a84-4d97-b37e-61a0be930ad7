package org.senyor.statistics.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.common.core.domain.R;
import org.senyor.common.web.core.BaseController;
import org.senyor.statistics.domain.vo.*;
import org.senyor.statistics.service.IUserDataStatisticsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 用户数据统计
 */
@Slf4j
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/statistics/userData")
public class UserDataStatisticsController extends BaseController {

    private final IUserDataStatisticsService userDataStatisticsService;

    /**
     * 获取用户当前机构的信息 地区级别,adCode等
     * @param tenantId 机构编号
     * */
    @GetMapping("/getTenantBaseCode/{tenantId}")
    public R<TenantBaseCodeVo> getTenantBaseCode(@PathVariable String tenantId) {
        TenantBaseCodeVo vo = userDataStatisticsService.getTenantBaseCode(tenantId);
        return R.ok(vo);
    }

    /**
     * 基础数据实时看板 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getUserBaseStats")
    public R<UserBaseDataStatisticsVo> getUserBaseStats(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        UserBaseDataStatisticsVo userStats = userDataStatisticsService.getUserBaseStats(level, levelId);
        return R.ok(userStats);
    }

    /**
     * 人员状况分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getUserConditionStats")
    public R<UserConditionDataStatisticsVo> getUserConditionStats(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        UserConditionDataStatisticsVo userStats = userDataStatisticsService.getUserConditionStats(level, levelId);
        return R.ok(userStats);
    }

    /**
     * 科普解压 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getRelaxationStatistics")
    public R<RelaxationStatisticsVo> getRelaxationStatistics(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId,
        @RequestParam("flagStr") String flagStr){
        RelaxationStatisticsVo relaxStats = userDataStatisticsService.getRelaxationStatistics(level, levelId, flagStr);
        return R.ok(relaxStats);
    }

    /**
     * 附属单位预警分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getTenantWarningStats")
    public R<List<TenantWarningStatisticsVo>> getTenantWarningStats(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        List<TenantWarningStatisticsVo> userStats = userDataStatisticsService.getTenantWarningStats(level, levelId);
        return R.ok(userStats);
    }

    /**
     * 心理咨询统计 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getConsultationRecordsStats")
    public R<ConsultationRecordsStatisticsVo> getConsultationRecordsStats(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        ConsultationRecordsStatisticsVo recordsStatisticsVo = userDataStatisticsService.getConsultationRecordsStats(level, levelId);
        return R.ok(recordsStatisticsVo);
    }

    /**
     * 测评活动分析 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getScaleWarningRecords")
    public R<ScaleWarningRecordsStatisticsVo> getScaleWarningRecords(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        ScaleWarningRecordsStatisticsVo vo = userDataStatisticsService.getScaleWarningRecords(level,levelId);
        return R.ok(vo);
    }

    /**
     * 各区域人数 统计数据
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getUserNumOnLevelCode")
    public R<Map<String, Long>> getUserNumOnLevelCode(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        Map<String,Long> vo = userDataStatisticsService.getUserNumOnLevelCode(level,levelId);
        return R.ok(vo);
    }

    /**
     * 区域内的机构具体信息 统计
     * @param level 层级
     * @param levelId 区域ID
     * */
    @GetMapping("/getTenantsInArea")
    public R<List<TenantsResultObjVo>> getTenantsInArea(
        @RequestParam("level") String level,
        @RequestParam("levelId") Integer levelId){
        List<TenantsResultObjVo> vo = userDataStatisticsService.getTenantsInArea(level,levelId);
        return R.ok(vo);
    }

    /**
     * 获取当前机构的全称名字
     * */


}
