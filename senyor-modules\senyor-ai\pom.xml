<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>senyor-modules</artifactId>
        <groupId>org.senyor</groupId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>senyor-ai</artifactId>

    <description>
        senyor-ai AI智能模块
    </description>

    <properties>
        <langchain4j.version>1.0.0-beta3</langchain4j.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>dashscope-sdk-java</artifactId>
                <version>2.20.8</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- 阿里云DashScope SDK -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>dashscope-sdk-java</artifactId>
            <version>2.20.8</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>dashscope-sdk-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 系统内部依赖 -->
        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.senyor</groupId>
            <artifactId>senyor-common-doc</artifactId>
        </dependency>

        <!-- Swagger注解 -->
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>2.2.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-core</artifactId>
        </dependency>
    </dependencies>

    <repositories>
        <!-- 阿里云Maven仓库 -->
        <repository>
            <id>aliyun</id>
            <name>阿里云公共仓库</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <!-- Maven Central -->
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
        </repository>
    </repositories>
</project>
