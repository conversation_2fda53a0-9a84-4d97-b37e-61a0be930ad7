<template>
  <div class="app-container">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span class="header-title">AI配置管理</span>
          <div class="right-menu">
            <el-button type="primary" @click="handleAdd" v-hasPermi="['ai:config:add']">
              <el-icon><plus /></el-icon> 新增配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索与工具栏 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="danger" plain :disabled="selectedIds.length === 0" @click="() => handleDelete()" v-hasPermi="['ai:config:remove']">
            <el-icon><delete /></el-icon> 批量删除
          </el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain @click="handleRefresh">
            <el-icon><refresh /></el-icon> 刷新
          </el-button>
        </el-col>
        <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 数据表格 -->
      <el-table
        v-loading="loading"
        :data="configList"
        @selection-change="handleSelectionChange"
        border
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="序号" type="index" width="80" align="center" />
        <el-table-column prop="configName" label="配置名称" align="center" show-overflow-tooltip />
        <el-table-column prop="model" label="模型" align="center">
          <template #default="scope">
            <el-tag>{{ getModelLabel(scope.row.model) }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="temperature" label="温度参数" align="center" width="100" />
        <el-table-column prop="maxTokens" label="最大令牌数" align="center" width="100" />
        <el-table-column prop="isDefault" label="是否默认" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isDefault === 'Y' ? 'success' : 'info'">
              {{ scope.row.isDefault === 'Y' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isSystem" label="配置类型" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.isSystem === 'Y' ? 'danger' : 'primary'">
              {{ scope.row.isSystem === 'Y' ? '系统' : '用户' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" align="center" width="100">
          <template #default="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'info'">
              {{ scope.row.status === '0' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" align="center" width="160" />
        <el-table-column label="操作" align="center" width="220" class-name="small-padding fixed-width">
          <template #default="scope">
            <el-button
              type="primary"
              link
              v-if="scope.row.isDefault !== 'Y'"
              @click="handleSetDefault(scope.row)"
              v-hasPermi="['ai:config:edit']"
            >
              设为默认
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleUpdate(scope.row)"
              v-hasPermi="['ai:config:edit']"
            >
              修改
            </el-button>
            <el-button
              type="primary"
              link
              @click="handleDetail(scope.row)"
              v-hasPermi="['ai:config:query']"
            >
              详情
            </el-button>
            <el-button
              type="primary"
              link
              v-if="!scope.row.isSystem || scope.row.isSystem !== 'Y'"
              @click="handleDelete(scope.row.configId)"
              v-hasPermi="['ai:config:remove']"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 配置表单对话框 -->
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="600px" append-to-body>
      <el-form ref="configFormRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="配置名称" prop="configName">
          <el-input v-model="form.configName" placeholder="请输入配置名称" />
        </el-form-item>
        <el-form-item label="模型" prop="model">
          <el-select v-model="form.model" placeholder="请选择模型" style="width: 100%">
            <el-option
              v-for="item in modelOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="温度参数" prop="temperature">
          <el-slider
            v-model="form.temperature"
            :min="0"
            :max="1"
            :step="0.1"
            show-input
          />
        </el-form-item>
        <el-form-item label="最大令牌数" prop="maxTokens">
          <el-input-number
            v-model="form.maxTokens"
            :min="100"
            :max="4000"
            :step="100"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="系统提示词" prop="systemPrompt">
          <el-input
            v-model="form.systemPrompt"
            type="textarea"
            placeholder="请输入系统提示词"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="0">启用</el-radio>
            <el-radio label="1">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            :rows="2"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="dialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog title="配置详情" v-model="detailDialogVisible" width="600px" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="配置名称">{{ detail.configName }}</el-descriptions-item>
        <el-descriptions-item label="模型">{{ getModelLabel(detail.model) }}</el-descriptions-item>
        <el-descriptions-item label="温度参数">{{ detail.temperature }}</el-descriptions-item>
        <el-descriptions-item label="最大令牌数">{{ detail.maxTokens }}</el-descriptions-item>
        <el-descriptions-item label="系统提示词">{{ detail.systemPrompt || '无' }}</el-descriptions-item>
        <el-descriptions-item label="是否默认">
          {{ detail.isDefault === 'Y' ? '是' : '否' }}
        </el-descriptions-item>
        <el-descriptions-item label="配置类型">
          {{ detail.isSystem === 'Y' ? '系统配置' : '用户配置' }}
        </el-descriptions-item>
        <el-descriptions-item label="状态">
          {{ detail.status === '0' ? '启用' : '禁用' }}
        </el-descriptions-item>
        <el-descriptions-item label="创建者">{{ detail.userName }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ detail.createTime }}</el-descriptions-item>
        <el-descriptions-item label="备注">{{ detail.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="detailDialogVisible = false">关 闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { Plus, Delete, Refresh } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox, FormInstance, FormRules } from 'element-plus';
import { getAiConfigList, getAiConfig, createAiConfig, updateAiConfig, deleteAiConfig, setDefaultConfig } from '@/api/ai/config';
import { AiConfig, modelOptions } from '@/api/ai/config/types';

// 加载状态
const loading = ref(false);
// 显示搜索条件
const showSearch = ref(true);
// 配置列表
const configList = ref<AiConfig[]>([]);
// 总记录数
const total = ref(0);
// 选中的配置ID数组
const selectedIds = ref<number[]>([]);

// 查询参数
const queryParams = reactive({
  pageNum: 1,
  pageSize: 10,
  configName: undefined,
  model: undefined,
  status: undefined,
});

// 表单参数
const configFormRef = ref<FormInstance>();
const form = reactive<AiConfig>({
  configName: '',
  model: 'qwen-max',
  temperature: 0.7,
  maxTokens: 1500,
  systemPrompt: '',
  status: '0',
  remark: '',
});

// 表单校验规则
const rules = reactive<FormRules>({
  configName: [{ required: true, message: '配置名称不能为空', trigger: 'blur' }],
  model: [{ required: true, message: '请选择模型', trigger: 'change' }],
  temperature: [{ required: true, message: '温度参数不能为空', trigger: 'blur' }],
  maxTokens: [{ required: true, message: '最大令牌数不能为空', trigger: 'blur' }],
});

// 对话框状态
const dialogVisible = ref(false);
const dialogTitle = ref('');
const dialogType = ref('add'); // add or edit
const detailDialogVisible = ref(false);
const detail = ref<AiConfig>({} as AiConfig);

// 初始化
onMounted(() => {
  getList();
});

// 获取配置列表
const getList = async () => {
  loading.value = true;
  try {
    const res = await getAiConfigList(queryParams);
    if (res.code === 200) {
      configList.value = res.data || [];
      total.value = res.total || 0;
    } else {
      configList.value = [];
      total.value = 0;
      ElMessage.error(res.msg || '获取配置列表失败');
    }
  } catch (error) {
    console.error('获取配置列表错误', error);
    ElMessage.error('系统错误，请联系管理员');
  } finally {
    loading.value = false;
  }
};

// 获取模型名称
const getModelLabel = (value: string) => {
  const model = modelOptions.find(item => item.value === value);
  return model ? model.label : value;
};

// 表格选择项变化
const handleSelectionChange = (selection: AiConfig[]) => {
  selectedIds.value = selection.map(item => item.configId) as number[];
};

// 刷新列表
const handleRefresh = () => {
  getList();
};

// 添加配置
const handleAdd = () => {
  dialogTitle.value = '添加配置';
  dialogType.value = 'add';
  dialogVisible.value = true;
  resetForm();
};

// 修改配置
const handleUpdate = async (row: AiConfig) => {
  dialogTitle.value = '修改配置';
  dialogType.value = 'edit';
  dialogVisible.value = true;
  resetForm();
  
  // 获取详细信息
  const res = await getAiConfig(row.configId as number);
  if (res.code === 200 && res.data) {
    Object.assign(form, res.data);
  }
};

// 查看详情
const handleDetail = async (row: AiConfig) => {
  const res = await getAiConfig(row.configId as number);
  if (res.code === 200 && res.data) {
    detail.value = res.data;
    detailDialogVisible.value = true;
  }
};

// 设为默认配置
const handleSetDefault = (row: AiConfig) => {
  ElMessageBox.confirm(`确认将 ${row.configName} 设置为默认配置？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const res = await setDefaultConfig(row.configId as number);
      if (res.code === 200) {
        ElMessage.success('设置默认配置成功');
        getList();
      } else {
        ElMessage.error(res.msg || '设置默认配置失败');
      }
    } catch (error) {
      console.error('设置默认配置错误', error);
      ElMessage.error('系统错误，请联系管理员');
    }
  }).catch(() => {});
};

// 删除配置
const handleDelete = (configId?: number | number[]) => {
  const ids = configId || selectedIds.value;
  if (!ids || (Array.isArray(ids) && ids.length === 0)) {
    ElMessage.warning('请选择要删除的配置');
    return;
  }
  
  ElMessageBox.confirm('确认删除选中的配置？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    try {
      const res = await deleteAiConfig(ids);
      if (res.code === 200) {
        ElMessage.success('删除成功');
        getList();
      } else {
        ElMessage.error(res.msg || '删除失败');
      }
    } catch (error) {
      console.error('删除配置错误', error);
      ElMessage.error('系统错误，请联系管理员');
    }
  }).catch(() => {});
};

// 重置表单
const resetForm = () => {
  if (configFormRef.value) {
    configFormRef.value.resetFields();
  }
  form.configId = undefined;
  form.configName = '';
  form.configDesc = '';
  form.model = 'qwen-max';
  form.temperature = 0.7;
  form.maxTokens = 1500;
  form.systemPrompt = '';
  form.status = '0';
  form.remark = '';
};

// 提交表单
const submitForm = async () => {
  if (!configFormRef.value) return;
  
  await configFormRef.value.validate(async (valid) => {
    if (!valid) return;
    
    try {
      let res;
      if (dialogType.value === 'add') {
        res = await createAiConfig(form);
      } else {
        res = await updateAiConfig(form);
      }
      
      if (res.code === 200) {
        ElMessage.success(dialogType.value === 'add' ? '添加成功' : '修改成功');
        dialogVisible.value = false;
        getList();
      } else {
        ElMessage.error(res.msg || (dialogType.value === 'add' ? '添加失败' : '修改失败'));
      }
    } catch (error) {
      console.error('保存配置错误', error);
      ElMessage.error('系统错误，请联系管理员');
    }
  });
};
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-title {
  font-weight: bold;
  font-size: 16px;
}

.right-menu {
  display: flex;
  align-items: center;
}

.mb8 {
  margin-bottom: 8px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style> 