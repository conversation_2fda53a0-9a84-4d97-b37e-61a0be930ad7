package org.senyor.api.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.senyor.api.service.IPlanApiService;
import org.senyor.common.core.exception.ServiceException;
import org.senyor.common.core.utils.StringUtils;
import org.senyor.common.json.utils.JsonUtils;
import org.senyor.common.satoken.utils.LoginHelper;
import org.senyor.scale.mapper.ScaleAssessPlanMapper;
import org.senyor.system.domain.bo.SysUserBo;
import org.senyor.system.domain.vo.SysFieldVo;
import org.senyor.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@RequiredArgsConstructor
@Service
public class PlanApiServiceImpl  implements IPlanApiService {

    private final SysUserMapper baseMapper;
    private final ScaleAssessPlanMapper scaleAssessPlanMapper;

    @Override
    public Boolean updateUserByField(SysUserBo bo) {
        Long userId = LoginHelper.getUserId();
        bo.setUserId(userId);

        Long planId = bo.getPlanId();
        Map<String, String> planInfo = scaleAssessPlanMapper.getPlanInfo(planId, LoginHelper.getTenantId());
        String selfOther = planInfo.get("selfOther");
        String selfUserIds = planInfo.get("selfUserIds");
        if(StringUtils.isNotBlank(selfOther) && StringUtils.isNotBlank(selfUserIds)) {
            String[] userIdLists = selfUserIds.split(",");
            if("T".equals(selfOther)) {
                if(StringUtils.isEmpty(selfUserIds)) {
                    throw new ServiceException("他评对象不存在");
                }

                bo.setUserId(Long.valueOf(userIdLists[0]));
            }

        }

        List<SysFieldVo> fieldList = bo.getFieldList();
        if (CollectionUtils.isEmpty(fieldList)) {
            return true; // 没有字段需要更新
        }
        Map<String, Object> fieldMap = new HashMap<>();
        Map<String, String> extensionFields = new HashMap<>();
        for (SysFieldVo field : fieldList) {
            if ("0".equals(field.getStatus())) {//原始字段
                fieldMap.put(field.getFieldFlag(), field.getRealValue());
            } else if ("1".equals(field.getStatus())) {//扩展字段
                extensionFields.put(field.getFieldFlag(), field.getRealValue());
            }
        }
        String jsonStr = JsonUtils.toJsonString(extensionFields);
        // 动态更新
        int rows = baseMapper.updateUserFields(bo.getUserId(), fieldMap, jsonStr);
        return rows > 0;
    }
}
